{"name": "bander", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@abacritt/angularx-social-login": "^1.2.5", "@agm/core": "^1.1.0", "@angular/animations": "^14.2.12", "@angular/common": "^14.2.12", "@angular/compiler": "^14.2.12", "@angular/core": "^14.2.12", "@angular/forms": "^14.2.12", "@angular/platform-browser": "^14.2.12", "@angular/platform-browser-dynamic": "^14.2.12", "@angular/router": "^14.2.12", "@auth0/angular-jwt": "^5.1.2", "@ng-select/ng-select": "^9.0.2", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@twilio/conversations": "^2.2.1", "ngx-dropzone": "^3.1.0", "ngx-owl-carousel-o": "^14.0.1", "ngx-pagination": "^6.0.3", "ngx-toastr": "^15.2.2", "rxjs": "~7.5.0", "swiper": "^8.4.5", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.10", "@angular/cli": "^14.2.10", "@angular/compiler-cli": "^14.2.12", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~4.0.0", "autoprefixer": "^10.4.12", "jasmine-core": "~4.3.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "postcss": "^8.4.19", "tailwindcss": "^3.2.4", "typescript": "~4.8.4"}}