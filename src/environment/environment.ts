// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

let hostName = window.location.hostname;
let substring = "web.bandarestate.com";
// let url = 'http://localhost:4200/';
let url = 'https://dev-cp.bandarestate.com/api/';
let substringStg = "http://localhost:4200/";

if(hostName.includes(substring)){
  url = 'https://dev-cp.bandarestate.com/api/';
}

// temporarily added to change after getting correct url
// if(hostName.includes(substringStg)){
//   url = 'https://dev-cp.bandarestate.com/api/';
// }

export const environment = {
  production: false,
  apiurl:url,
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
