import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class AddressService {

  constructor(
   private configServices:ConfigServices
  ) { }

  baseRoute = ''

  getCountry(getParams?: any) {
    return this.configServices.readRequest(this.baseRoute + '/country', getParams).pipe(map(response => {
      return response
    }))
  }
  getState(getParams?: any) {
    return this.configServices.readRequest(this.baseRoute + '/state', getParams).pipe(map(response => {
      return response
    }))
  }
  getAreas(getParams?: any) {
    return this.configServices.readRequest(this.baseRoute + '/area', getParams).pipe(map(response => {
      return response
    }))
  }
}

