import { Injectable } from '@angular/core';
import { debounceTime, map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class AreaGuidesService {

  constructor(
    private configServices: ConfigServices
  ) { }


  areaGuides(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/area-guides', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  areaDetails(getParams?: any) {
    return this.configServices.readRequest('/area-guide-details', getParams).pipe(map(response => {
      return response;
    }))
  }

  areaGuidesSearch(getParams?: any) {
    return this.configServices.readRequest('/area-guide-search', getParams).pipe(map(response => {
      return response;
    }))
  }

}
