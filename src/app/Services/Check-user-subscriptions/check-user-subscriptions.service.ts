import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class CheckUserSubscriptionsService {

  constructor(private configServices: ConfigServices) { }



  checkSubscriptionValidity(getParams?: any) {
    return this.configServices.readRequest('/check-subscription-validitiy', getParams).pipe(map(response => {
      return response
    }))
  }
}
