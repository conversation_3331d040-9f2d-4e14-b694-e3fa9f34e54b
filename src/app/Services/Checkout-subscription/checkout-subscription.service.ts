import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class CheckoutSubscriptionService {

  constructor(
    private configServices: ConfigServices
  ) { }


  checkoutPlan(getParams?: any, postParams?: any) {

    return this.configServices.postNewRequest('/checkout', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }
}
