import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class MyAccountService {

  constructor(
    private configServices: ConfigServices
  ) { }


  getMyProperties(getParams?: any) {
    return this.configServices.readRequest('/my-properties', getParams).pipe(map(response => {
      return response
    }))
  }

  getMyStories(getParams?: any) {
    return this.configServices.readRequest('/user-story-list', getParams).pipe(map(response => {
      return response
    }))
  }

  getMyPlans(getParams?: any) {
    return this.configServices.readRequest('/user-story-list', getParams).pipe(map(response => {
      return response
    }))
  }
}
