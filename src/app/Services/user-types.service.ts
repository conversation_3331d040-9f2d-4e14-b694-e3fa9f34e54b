import { Injectable } from '@angular/core';
import { configSettings } from '../Config/config.settings';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class UserTypesService {

  constructor(
    private configSettings: configSettings,
    private http: HttpClient,
  ) { }

  baseroute = this.configSettings.baserouteAPI
  lang = this.configSettings.getLang()

  userTypes() {
    return this.http.get<any>(this.baseroute + 'user-types'+ `?lang=${this.lang}`).pipe(map(res => res))
  }

}
