import { Injectable, OnInit } from '@angular/core';
import { BehaviorSubject, map } from 'rxjs';
import { Client, Conversation, Message, ConversationUpdateReason, } from "@twilio/conversations";
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class ChatService implements OnInit {
  chatClient: Client;
  conversation: Conversation
  constructor(
    private configServices: ConfigServices
  ) { }
  ngOnInit(): void {
  }



  getToken(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/chat-init', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  // connect(token: any, sid: any) {
  //   this.chatClient = new Client(token);
  //   // this.conversation = new Conversation('', 'CHe034248f41c84fb58fdb120a236a034e', '', '', '')
  //   this.listenToEvents(sid);
  // }
  listenToEvents(sid: any) {
    this.chatClient.on('stateChanged', (state) => {
      if (state === 'initialized') {
        console.log('initialized');
        this.getChannnel(sid).then((channel: any) => {
          // channel.sendMessage('hi nasr3')
          channel.getMessages(10, 99999, 'backwards').then((m: any) => {
            console.log(m.items[5].state.body)
            console.log(m.items)
          })
        }).catch((e: any) => {
          console.log(e)
        })
      }
    });

    this.chatClient.on('conversationAdded', (conv: Conversation) => {
      // if (conv.dateCreated > this.currentTime) {
      console.log('new chat created');
      // }
    });

    this.chatClient.on('messageAdded', (msg: Message) => {
      console.log('message added');
    });

    this.chatClient.on('connectionError', (err) => {
      console.log('Connection not made, please refresh the page.');
    });
  }

  // twilio functions
  getChannnel(sid: string): any {
    return this.chatClient.getConversationBySid(sid);
  }

  getChannelChat(channel: any, index: any) {
    let size = 30;
    return channel.getMessages(size, index);
  }

  createPrivateChannel(friendlyName: string) {
    return this.chatClient.createConversation({ friendlyName });
  }

  async getLastMessage(channel: Conversation, no: any) {
    let response = await channel.getMessages(1, no);
    return response.items.length > 0 ? response.items[0].body : '';
  }

  async getUnconsumedMessage(channel: Conversation) {
    let response = await channel.getUnreadMessagesCount();
    if (response == null) {
      let lastMsg = await channel.getMessages(1);
      response = lastMsg && lastMsg.items.length > 0 ? lastMsg.items[0].index + 1 : 0;
    }
    return response;
  }

  // joinChannel(sid: any) {
  //   return this.conversation.join()
  // }

  conversationArr = new BehaviorSubject([]);

  setConversation(data: any) {
    this.conversationArr.next(data)
  }

  getConversation(){
    return this.conversationArr.asObservable()
  }




  // chat-history
  getContacts(getParams?: any) {
    return this.configServices.readRequest('/chat-history', getParams).pipe(map(response => {
      return response
    }))
  }
}
