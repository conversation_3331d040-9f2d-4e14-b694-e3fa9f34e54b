import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class PlansService {

  constructor(
    private configServices: ConfigServices

  ) { }


  getPlans(getParams?: any) {
    return this.configServices.readRequest('/plans', getParams).pipe(map(response => {
      return response
    }))
  }
}
