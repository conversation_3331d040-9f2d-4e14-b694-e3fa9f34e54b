import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class BuildingGuideService {

  constructor(
    private configServices: ConfigServices,
  ) { }

  getBuildingGuides(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/building-guides', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  getBuildingGuidesDetails(getParams?: any, postParams?: any) {
    return this.configServices.readRequest('/building-guide', getParams).pipe(map(response => {
      return response;
    }))
  }


}
