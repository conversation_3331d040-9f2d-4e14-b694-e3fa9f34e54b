import { Injectable } from '@angular/core';
import { configSettings } from '../Config/config.settings';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';
import { ConfigServices } from '../Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class PropertiesService {


  constructor(
    private configSettings: configSettings,
    private http: HttpClient,
    private configServices:ConfigServices
  ) { }

  baseroute = this.configSettings.baserouteAPI
  lang = this.configSettings.getLang()

  getProperties() {
    return this.http.get<any>(this.baseroute + 'properties' + `?lang=${this.lang}`).pipe(map(res => res))
  }


  getPropertyCategoryList(getParams: any){
    return this.configServices.readRequest('/property-categories-list', getParams).pipe(map(response => {
      return response
    }))
  }

}
