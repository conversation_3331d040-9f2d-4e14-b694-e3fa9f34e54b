import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class UserHistoryPlansService {

  constructor(
    private configServices: ConfigServices
  ) { }


  plansHistory(getParams?: any) {
    return this.configServices.readRequest('/user-subscription-history', getParams).pipe(map(response => {
      return response;
    }))
  }

  // cancelPlan(getParams?: any) {
  //   return this.configServices.readRequest('/CancelUserSubscription', getParams).pipe(map(response => {
  //     return response;
  //   }))
  // }

  cancelPlan(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/cancel-user-subscription', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }
}
