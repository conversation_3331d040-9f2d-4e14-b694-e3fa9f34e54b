import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class ArchitectListingService {
  
  constructor(
    private configServices: ConfigServices
  ) { }


  baseRoute = ''

  architectListing(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest(this.baseRoute + '/architect-listing', getParams, postParams).pipe(map(response => {
      return response
    }))
  }

    featuredArchitectListing(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest(this.baseRoute + '/featured-architect-listing', getParams, postParams).pipe(map(response => {
      return response
    }))
  }
}
