import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class ExpertiseService {

  constructor(
    private configServices: ConfigServices,
  ) { }


  getExpertise(getParams?: any) {
    return this.configServices.readRequest('/expertise-list', getParams).pipe(map(response => {
      return response
    }))
  }


}
