import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class FavoritesService {

  constructor(
    private configServices: ConfigServices
  ) { }



  toggleFavorite(getParams?: any, postParams?: any) {

    return this.configServices.postNewRequest('/toggle-user-favourites', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }


  favoriteListing(getParams?: any) {
    return this.configServices.readRequest('/favourites', getParams).pipe(map(response => {
      return response
    }))
  }
}
