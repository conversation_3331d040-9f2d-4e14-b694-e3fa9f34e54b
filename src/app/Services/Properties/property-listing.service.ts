import { Injectable } from '@angular/core';
import { configSettings } from '../../Config/config.settings';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class PropertyListingService {

  baseRoute: string = ''
  constructor(
    private configServices:ConfigServices
  ) { }


  postProperties(getParams?:any,postParams?:any){

    return this.configServices.postNewRequest(this.baseRoute+'/property-listing',getParams,postParams).pipe(map(response=>{      
      return response;
    }))
  }

  getPropertyDetails(getParams?:any){
    return this.configServices.readRequest(this.baseRoute+'/property-details',getParams).pipe(map(response=>{
      return response
    }))
  }


  getPropertyAmenities(getParams?:any){
    return this.configServices.readRequest(this.baseRoute+'/property-amenity-list',getParams).pipe(map(response=>{
      return response
    }))
  }

}
