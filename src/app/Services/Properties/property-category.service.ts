import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class PropertyCategoryService {

  constructor(
    private configServices:ConfigServices
  ) { }

  getPropertyCategory(getParams?: any) {
    return this.configServices.readRequest('/property-categories-list', getParams).pipe(map(response => {
      return response
    }))
  }
}
