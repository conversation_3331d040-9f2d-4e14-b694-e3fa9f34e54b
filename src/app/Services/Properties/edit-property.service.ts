import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class EditPropertyService {

  constructor(
    private configServices: ConfigServices
  ) { }


  getDetails(getParams?: any) {
    return this.configServices.readRequest('/edit-property-detail', getParams).pipe(map(response => {
      return response
    }))
  }
  
  editProperty(getParams?: any, postParams?: any) {
    
    return this.configServices.postNewRequest('/edit-property', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }
  
  deleteMedia(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/delete-property-image', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  deleteProperty(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/delete-property', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  markPropertySold(getParams?:any,postParams?:any){
    return this.configServices.postNewRequest('/mark-sold-property',getParams,postParams).pipe(map(response=>{      
      return response;
    }))
  }
}
