import { Injectable } from '@angular/core';
import { configSettings } from '../../Config/config.settings';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class PostPropertyService {

  constructor(
    private configServices: ConfigServices
  ) { }


  postProperties(getParams?: any, postParams?: any) {

    return this.configServices.postNewRequest('/post-property', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  getAmenitiesAndFeatures(getParams?: any) {
    return this.configServices.readRequest('/property-amenity-list', getParams).pipe(map(response => {
      return response
    }))
  }


}
