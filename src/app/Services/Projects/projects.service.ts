import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class ProjectsService {

  constructor(
    private configServices: ConfigServices
  ) { }


  getProjectTypes(getParams?: any) {
    return this.configServices.readRequest('/project-types', getParams).pipe(map(response => {
      return response
    }))
  }

  postProject(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/post-project', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  projectDetails(getParams?: any) {
    return this.configServices.readRequest('/project-details', getParams).pipe(map(response => {
      return response
    }))
  }

  editProject(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/edit-project', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  myProjects(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/my-projects', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  deleteProject(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/delete-project', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  deleteMedia(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/delete-project-image', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }
}
