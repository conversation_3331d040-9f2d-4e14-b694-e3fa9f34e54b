import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class AgentService {

    constructor(
    private configServices: ConfigServices

    ) { }

  
  baseRoute = ''

  agentListing(getParams?: any) {
    return this.configServices.readRequest('/agent-listing', getParams).pipe(map(response => {
      return response;
    }))
  }
}
