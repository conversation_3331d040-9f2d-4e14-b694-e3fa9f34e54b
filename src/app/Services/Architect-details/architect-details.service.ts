import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';
import { configSettings } from 'src/app/Config/config.settings';

@Injectable({
  providedIn: 'root'
})
export class ArchitectDetailsService {

  constructor(
    private configServices: ConfigServices,
    private configSettings: configSettings,
  ) { }

  baseRoute = ''

  getArchitectDetails(getParams?: any) {
    return this.configServices.readRequest(this.baseRoute + '/architect-details', getParams).pipe(map(response => {
      return response
    }))
  }


}
