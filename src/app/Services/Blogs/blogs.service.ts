import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class BlogsService {

  constructor(private configServices: ConfigServices) { }


  blogsListing(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/blog-listing', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  blogsDetails(getParams?: any) {
    return this.configServices.readRequest('/blog-details', getParams).pipe(map(response => {
      return response
    }))
  }

}
