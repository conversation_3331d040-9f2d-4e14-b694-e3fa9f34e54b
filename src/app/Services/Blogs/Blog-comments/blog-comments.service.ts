import { Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class BlogCommentsService {

  constructor(private configServices: ConfigServices) { }

  public comments = new BehaviorSubject<any>([]);


  blogComments(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/post-blog-comment', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }


  setComments(data: any) {
    this.comments.next(data)
  }

  getComments(): Observable<any> {
    return this.comments.asObservable()
  }
}
