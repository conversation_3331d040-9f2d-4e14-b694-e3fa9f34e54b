import { Injectable } from '@angular/core';
import { BehaviorSubject, map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  constructor(
    private configServices: ConfigServices
  ) { }

  allNotifications = new BehaviorSubject<any>([])

  setNotifications(data: any) {
    this.allNotifications.next(data)
  }

  getNotifications() {
    return this.allNotifications.asObservable()
  }


  getNotificationsApi(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/notification-user-listing', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }



  markAsSeen(getParams: any) {
    return this.configServices.readRequest('/notification-mark-seen', getParams).pipe(map(response => {
      return response
    }))
  }
}
