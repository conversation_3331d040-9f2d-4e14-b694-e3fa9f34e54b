import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class FeaturedPlanListService {

  constructor(
    private configServices: ConfigServices,

  ) { }

  featuredPlans(getParams?: any) {
    return this.configServices.readRequest('/featured-plan-list', getParams).pipe(map(response => {
      return response
    }))
  }
}
