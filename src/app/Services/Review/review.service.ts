import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';
import { configSettings } from 'src/app/Config/config.settings';

@Injectable({
  providedIn: 'root'
})


export class ReviewService {

  constructor(
    private configServices: ConfigServices,
    private configSettings: configSettings,
  ) { }


  baseRoute = ''


  architectReviews(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest(this.baseRoute + '/add-review', getParams, postParams).pipe(map(response => {
      return response
    }))
  }
}
