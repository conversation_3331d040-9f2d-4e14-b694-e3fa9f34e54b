import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class CmsService {

  constructor(
    private configServices: ConfigServices
  ) { }


  baseRoute = ''

  getCms(getParams?: any) {
    return this.configServices.readRequest(this.baseRoute + '/cms', getParams).pipe(map(response => {
      return response
    }))
  }
}
