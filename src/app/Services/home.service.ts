import { Injectable } from '@angular/core';
import { configSettings } from '../Config/config.settings';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs';
import { ConfigServices } from '../Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class HomeService {

  baseroute = this.configSettings.baserouteAPI
  lang = this.configSettings.getLang()
  baseRoute = ''

  constructor(
    private configSettings: configSettings,
    private http: HttpClient,
    private configServices: ConfigServices,
  ) { }


  home(getParams: any) {
    return this.configServices.readRequest(this.baseRoute + '/home', getParams).pipe(map(response => {
      return response
    }))
  }

  getBanners(getParams?: any) {
    return this.configServices.readRequest(this.baseRoute + '/banner-listing', getParams).pipe(map(response => {
      return response
    }))
  }

  search(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/search', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  footer(getParams?: any) {
    return this.configServices.readRequest('/footer-menu', getParams).pipe(map(response => {
      return response;
    }))
  }

  getallCountryApi(getParams?: any) {
    return this.configServices.readRequest('/home-search-with-country', getParams).pipe(map(response => {
      return response;
    }))
  }
}
