import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';
@Injectable({
  providedIn: 'root'
})
export class FaqsService {

  constructor(
    private configServices: ConfigServices
  ) { }


  getFAQS(getParams?: any) {
    return this.configServices.readRequest('/faq', getParams).pipe(map(response => {
      return response
    }))
  }

  getFAQsDetails(getParams: any){
    return this.configServices.readRequest('/faq-detail', getParams).pipe(map(response => {
      return response
    }))
  }
}
