import {Injectable} from '@angular/core'

import {Observable, Subject} from 'rxjs';


@Injectable({
  providedIn: 'root'
})
export class UserService {


  constructor() {
    this.configSubject = new Subject();
    this.isUserLoggedIn = this.configSubject.asObservable();

  }

  public isUserLoggedIn = new Observable((observer) => {
    // observable execution
    observer.next();
    observer.complete();
  });
    private configSubject: Subject<string>;


  setUser(val: any) {
    this.configSubject.next(val); 
 
  }
}
