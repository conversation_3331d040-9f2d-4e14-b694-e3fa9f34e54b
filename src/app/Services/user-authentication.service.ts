import { Injectable } from '@angular/core';
import { configSettings } from '../Config/config.settings';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { catchError, map, throwError } from 'rxjs';
import { ConfigServices } from '../Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class UserAuthenticationService {

  baseroute = this.configSettings.baserouteAPI
  lang = this.configSettings.getLang()

  constructor(
    private configSettings: configSettings,
    private configServices: ConfigServices,
    private http: HttpClient,
  ) { }


  register(params: any) {
    return this.http.post<any>(this.baseroute + 'registration' + `?lang=${this.lang}`, params).pipe(map(res => res))
  }

  login(params: any) {
    return this.http.post<any>(this.baseroute + 'login' + `?lang=${this.lang}`, params).pipe(map(res => res))
  }

  verifyOTP(params: any) {
    return this.http.post<any>(this.baseroute + 'verify-code' + `?lang=${this.lang}`, params).pipe(map(res => res))
  }

  postEditProfile(getParams?: any, postParams?: any) {

    return this.configServices.postNewRequest('/edit-profile', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  forgotPassword(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/forgot-password', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }

  resendOtp(getParams?: any) {
    return this.configServices.readRequest('/verify-phone', getParams).pipe(map(response => {
      return response
    }))
  }

  socialLoginAndRegister(getParams?: any, postParams?: any) {
    return this.configServices.postNewRequest('/social-register', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }


}
