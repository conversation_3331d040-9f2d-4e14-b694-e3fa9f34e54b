import { Injectable } from '@angular/core';
import { BehaviorSubject, map } from 'rxjs';
import { ConfigServices } from 'src/app/Config/config.service';

@Injectable({
  providedIn: 'root'
})
export class SavedSearchesService {

  constructor(
    private configServices: ConfigServices


  ) { }


  searchList = new BehaviorSubject<any>(null);

  setSearchList(list: any) {
    this.searchList.next(list)
  }

  getSearchList() {
    return this.searchList.asObservable()
  }


  getRecentSearchListing(getParams?: any, postParams?: any) {

    return this.configServices.postNewRequest('/recent-search-listing', getParams, postParams).pipe(map(response => {
      return response;
    }))
  }



}
