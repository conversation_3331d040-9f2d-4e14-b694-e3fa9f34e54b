import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-featured-architects-card',
  templateUrl: './featured-architects-card.component.html',
  styleUrls: ['./featured-architects-card.component.scss']
})
export class FeaturedArchitectsCardComponent implements OnInit {

  @Input() featuredProject: any
  stars: any = []
  halfStar: any = false
  emptyStars: any = []
  constructor() { }

  ngOnInit(): void {

    // const rating = this.featuredProject.rating ? this.featuredProject.rating : 0

    // for (let i = 0; i < Math.floor(rating); i++) {
    //   this.stars.push('star')
    // }


    // if (!(parseInt(rating) == rating)) {
    //   this.halfStar = true
    // }

    // const emptyLength = 5 - (this.stars.length + ((this.halfStar) ? 1 : 0))

    // for (let i = 0; i < emptyLength; i++) {
    //   this.emptyStars.push('empty')
    // }


  }
}
