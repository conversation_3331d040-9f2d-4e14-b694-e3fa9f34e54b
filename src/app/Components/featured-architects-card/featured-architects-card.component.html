<div [routerLink]="['/architect-details',featuredProject?.id]" class="hover:cursor-pointer h-full w-full">
    <div class="relative bg-white h-full">
        <img src="{{featuredProject?.banner}}" class="aspect-square h-full w-full object-cover block" alt="">
        <div
            class="absolute -right-1 -left-1 -top-1 -bottom-1   overlay-hover  opacity-0 hover:opacity-100 duration-150 transform-gpu">
            <!-- opacity-0 hover:opacity-100 duration-150 transform-gpu -->
            <div class="mt-5  lg:mt-14 px-7 flex items-center gap-3 md:gap-5 xl:gap-9 mb-10">
                <img src="{{featuredProject?.logo}}"
                    class="logo w-14 h-14 lg:w-16 lg:h-16 2xl:w-24 2xl:h-24 rounded-full" alt="">
                <div>
                    <p class="text-2xl font-bold">{{featuredProject?.project_name}}</p>
                    <div class="flex items-center gap-2 xl:gap-3">
                        <p class=" flex gap-2">
                            <img *ngFor="let item of stars" src="../../../assets/icons/review-full-star.svg"
                                class="w-4 h-4 xl:w-6 xl:h-6" alt="">
                            <img *ngIf="halfStar" src="../../../assets/icons/review-half-star.svg"
                                class="w-4 h-4 xl:w-6 xl:h-6" alt="">
                            <img *ngFor="let item of emptyStars" src="../../../assets/icons/review-empty-star.svg"
                                class="w-4 h-4 xl:w-6 xl:h-6" alt="">
                        </p>
                        <!-- <span
                            class="text-accent  md:text-xl xl:text-3xl font-normal">{{featuredProject?.rating}}</span> -->
                    </div>
                </div>
            </div>
            <div class="px-7 text-sm md:text-base xl:text-lg font-normal tracking-wider customTruncate">
                {{featuredProject?.about}}
            </div>
        </div>
    </div>
</div>