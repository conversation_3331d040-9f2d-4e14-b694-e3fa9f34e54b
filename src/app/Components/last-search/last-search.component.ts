import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-last-search',
  templateUrl: './last-search.component.html',
  styleUrls: ['./last-search.component.scss']
})
export class LastSearchComponent implements OnInit {
  @Input() lastSearch: any
  constructor(
    private router:Router
  ) { }

  ngOnInit(): void {
  }


  routeTo(value: string){
    this.router.navigate(['/property-listing'],{ queryParams: { recentSearchID: value } });
  }
}
