<div class="bg-cover h-full w-full relative shadow-lg" [ngStyle]="{'background-image': 'url(' + property.thumbnail+ ')'}" [routerLink]="['/property-details',property?.id]"  >
    <div class="absolute  left-2 right-2 bottom-2" >
        <img [src]="property.logo" class="logo rounded-full left-2 mb-2" alt="" >
        <p class="text-white font-bold text-sm">{{property.title}}</p>
        <div class="flex items-center gap-1 text-[.65rem] text-steelBlue "><img src="../../../assets/icons/location.png"
                class="h-3 w-3">{{property.address}}</div>
        <div class="text-end text-accent text-[.65rem] font-bold">{{property.price}}{{'KD'|translate}} / {{"Month" | translate}}</div>
    </div>
</div>