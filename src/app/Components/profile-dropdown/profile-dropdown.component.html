<div  #profileDropdownEle class="drop-down  relative h-full" (click)="dropDown()" >
    <div id="main"
        class=" outline-none cursor-pointer px-2 lg:px-0 xl:px-2 h-full  bg-primary text-white relative flex items-center gap-2 lg:gap-1 xl:gap-2">
        <span><img src="../../../assets/icons/avatar.png" class="h-7 w-7"></span><span class="text">{{firstName}}</span>
        <span class="absolute rtl:left-2 ltr:right-2 top-1/2 h-3 w-3 -translate-y-1/2">
            <img class="h-full w-full"
                src="../../../assets/icons/arrow-semi-down-white.png"></span>
    </div>
    <div *ngIf="open"
        class="p-2 absolute rtl:right-5 rtl:lg:left-1 ltr:left-5  ltr:lg:right-1 mt-1 bg-white min-w-[12.5rem] w-full text-black shadow-md rounded-md overflow-hidden">
        <div class="blue-gradient rounded-md flex items-center gap-2 py-2 px-2 cursor-pointer">
            <div class="h-full overflow-hidden"><img class="h-8 w-8 shadow rounded-full"
                    [src]="profilePImg" alt=""></div>
            <div>
                <p class="text-white">{{firstName + ' ' + lastName}}</p>
                <p [routerLink]="['/my-account/my-profile']" fragment="edit" class="text-accent text-xs">{{"View/Edit profile" | translate}}</p>
            </div>
        </div>
        <ul>
             <li [routerLink]="['/my-account']" class="hover:underline rounded-md cursor-pointer p-2">{{"My account" | translate}}</li>
            <p [routerLink]="['/my-account/settings']" class="hover:underline rounded-md cursor-pointer p-2">{{"Settings" | translate}}</p>
            <li class="hover:underline rounded-md cursor-pointer p-2" (click)="logout()">{{"Log Out" | translate}}</li> 
        </ul>
    </div>
</div>
