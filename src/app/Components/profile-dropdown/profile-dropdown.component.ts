import { Component, ElementRef, OnInit, ViewChild,Renderer2 } from '@angular/core';

import { Router } from '@angular/router';
import { configSettings } from 'src/app/Config/config.settings';
import { HomeComponent } from 'src/app/Pages/home/<USER>';
import { UserService } from 'src/app/Services/User/user.service';

@Component({
  selector: 'app-profile-dropdown',
  templateUrl: './profile-dropdown.component.html',
  styleUrls: ['./profile-dropdown.component.scss'],
  host: {
    '(document:click)': 'closeDropDowns($event)',
  },
})
export class ProfileDropdownComponent implements OnInit {
isLoggedIn: any;
  // lang: string;

  constructor(
    private configSettings: configSettings,
    private renderer: Renderer2,
    private router: Router,
    private userService: UserService

  ) { }

  firstName:string= this.configSettings.getUserDetails().first_name
  lastName:string= this.configSettings.getUserDetails().last_name
  profilePImg:string= this.configSettings.getUserDetails().profile_img

  open: boolean = false
  isUserLoggedIn?: any
  lang?= this.configSettings.getLang();
  @ViewChild('profileDropdownEle') profileDropdownEleRef: ElementRef;
  @ViewChild('menu') menu: ElementRef<HTMLInputElement> | undefined;

  ngOnInit(): void {
  }

  dropDown() {
    this.open = !this.open
  }

  clickedOutside() {
    this.open = false
  }
  closeMenu() {
    if (this.lang == 'en') {
      this.renderer.addClass(this.menu?.nativeElement, '-translate-x-full')
    } else {
      this.renderer.addClass(this.menu?.nativeElement, 'rtl:translate-x-full')
      this.renderer.addClass(this.menu?.nativeElement, 'rtl:hidden')
    }
  }

  logout() {
    
    localStorage.removeItem('userDetails')
    this.configSettings.setIsUserLoggedIn(false)
    this.userService.setUser(false)
    this.configSettings.setUserTypes()
    // this.router.navigate(['/home'])
    this.router.navigate(['/home']).then(() => { window.location.reload() })
  }

  closeDropDowns(event: any) {
    event.stopPropagation()
    if (!this.profileDropdownEleRef.nativeElement.contains(event.target)) {
      this.open = false
    }
  }

}
