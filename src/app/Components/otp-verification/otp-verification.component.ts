import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { UserAuthenticationService } from 'src/app/Services/user-authentication.service';
import { UserService } from 'src/app/Services/User/user.service';
import { TranslateService } from '@ngx-translate/core';



@Component({
  selector: 'app-otp-verification',
  templateUrl: './otp-verification.component.html',
  styleUrls: ['./otp-verification.component.scss']
})
export class OtpVerificationComponent implements OnInit {

  constructor(
    private userAuthenticationService: UserAuthenticationService,
    private configSettings: configSettings,
    private toastr: ToastrService,
    private router: Router,
    private userService: UserService,
    private translateService: TranslateService,

  ) { }

  verified: boolean = false
  userDetails: any
  @Output() closePopup: EventEmitter<any> = new EventEmitter();
  ngOnInit(): void {
    this.userDetails = this.configSettings.getUserDetails()

    // this.getOtp(0)
  }

  getOtp(num: number) {
    const getParams = {
      is_resend: num,
      user_id: this.configSettings.getUserID()
    }

    this.userAuthenticationService.resendOtp(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            // console.log(response.body.data)
           
              this.translateService.get('OTP has been re-sent').subscribe(res => {
                this.toastr.success('', res);
              })
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


  closeOtp() {
    this.closePopup.emit();
    document.body.style.overflow = "auto";
  }

  verifyOTP(e: any) {
    let params = {
      user_id: this.userDetails.user_id,
      otp: e.otp
    }

    if (e.otp=== "") {
     
      this.translateService.get('Please enter the otp').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    else{
      this.configSettings.setShowLoader(true)
    this.userAuthenticationService.verifyOTP(params).subscribe({
      next: (response) => {
        if (response.status === 200) {
            this.userDetails.is_phone_verified = 1
            this.configSettings.setUserDetails(this.userDetails)
            this.verified = true
            this.configSettings.setShowLoader(false)
            this.toastr.success('', response.message)
        } else {
          this.toastr.error('', response.message)
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


    // .subscribe({
    //   next: (res) => {
    //     this.toastr.success('', res.message)
    //     this.verified = true
    //     this.userDetails['is_phone_verified'] = true
    //     this.configSettings.setUserDetails(this.userDetails)
    //     this.verified = true
    //   },     // nextHandler
    //   error: (err) => {
    //     this.toastr.error('', err.error.message)
    //   },    // errorHandler 
    // });

  }

  routeTo() {
    this.configSettings.setIsOTPVerified(true)
    this.userDetails?.user_types.forEach((e: any) => {
      console.log(e.type)
      if (e.type == 'AG' || e.type == 'AR') {
        this.router.navigate(['/registration/' + e.type])
        this.userService.setUser(true)
      } else if (e.type == 'UR' && e.type == 'AR') {
        this.router.navigate(['/home'])
        this.userService.setUser(true)
         }
      else {
        this.router.navigate(['/home'])
        this.userService.setUser(true)
      }
    });

  }

}
