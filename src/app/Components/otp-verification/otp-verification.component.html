<div class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="verified === false" class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-10 bg-white w-92 rounded-md">
    <div >
        <div class="flex justify-end" (click)="closeOtp()">
            <img class="block mx-auto h-full w-full" src="../../../assets/icons/close-gray.svg" alt="" class="w-4">
        </div>
        <div class="h-14">
            <img class="block mx-auto h-full w-full" src="../../../assets/icons/logo-blue.svg" alt="">
        </div>
        <div class="h-32 w-32 mx-auto">
            <img class="block mx-auto h-full w-full" src="../../../assets/images/boy-phone.png" alt="">
        </div>
        <p class="text-lg font-bold text-center">{{"Enter 4 Digits OTP code" | translate}}</p>
        <p class="text-sm text-center mb-2">{{"Enter the 4 digits code you recieved on your registered email" | translate}}</p>
        <form #otpForm="ngForm" (ngSubmit)="verifyOTP(otpForm.value)" >
            <div class="border flex justify-between items-center p-2 rounded-sm mb-4 relative">
                <input name="otpField" ngModel name="otp" class="outline-none text-sm w-full" size="4" maxlength="4" placeholder="_ _ _ _" type="text">
                <span
                    class="absolute rtl:left-2 rtl:right-[unset] right-2 text-[0.85rem]  text-primary hover:text-blue-900  text-ellipsis cursor-pointer" (click)="getOtp(1)" >{{"Resend OTP" | translate}}</span>
            </div>
            <button class="w-full bg-primary hover:bg-blue-900 text-white p-2 rounded-sm">{{"Verify & Proceed" | translate}}</button>
        </form>
    </div>
</div>
<div  *ngIf="verified === true"
    class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-10 bg-white w-[26rem] rounded-xl">
    <div class="">
        <div class="h-16 w-16 mx-auto">
            <img class="block mx-auto h-full w-full" src="../../../assets/images/finish.webp" alt="">
        </div>
        <p class="text-lg font-bold text-center text-primary">{{"Verification Successful" | translate}}</p>
        <p class="text-xs text-center mb-10">{{"Your account has been successfully created !!" | translate}}</p>
        <button class="w-full bg-primary hover:bg-blue-900 text-white p-2 rounded-sm" (click)="routeTo()">{{"Welcome" | translate}}</button>
    </div>
</div>