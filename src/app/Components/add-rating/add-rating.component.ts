import { Component, EventEmitter, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-add-rating',
  templateUrl: './add-rating.component.html',
  styleUrls: ['./add-rating.component.scss']
})
export class AddRatingComponent implements OnInit {

  constructor() { }

  ngOnInit(): void {
  }

  @Output() ratingVal: EventEmitter<any> = new EventEmitter<any>()


  activeNumber: any = 0

  hoverValue: any = 0;

  ratingValues(val: number) {
    if (val === this.activeNumber) {
      this.activeNumber = 0
    } else {
      this.activeNumber = val
      this.ratingVal.emit(this.activeNumber)
    }
  }

  // ratingValue1: any = 0;
  // ratingValue2: any = 0;
  // ratingValue3: any = 0;
  // ratingValue4: any = 0;
  // ratingValue5: any = 0;

  // ratingValueClick1: any = 0;
  // ratingValueClick2: any = 0;
  // ratingValueClick3: any = 0;
  // ratingValueClick4: any = 0;
  // ratingValueClick5: any = 0;
  // ratingValues(val: any) {
  //   if (val == 1) {
  //     this.ratingValueClick1++;

  //     // this.ratingValue = 0.5;

  //     if (this.ratingValueClick1 % 2 == 0) { this.ratingValue = 1 }

  //     this.ratingVal.emit(this.ratingValue)

  //   } else if (val == 2) {
  //     this.ratingValueClick2++;

  //     // this.ratingValue = 1.5;

  //     if (this.ratingValueClick2 % 2 == 0) { this.ratingValue = 2 }
  //     this.ratingVal.emit(this.ratingValue)


  //   } else if (val == 3) {
  //     this.ratingValueClick3++;

  //     // this.ratingValue = 2.5;

  //     if (this.ratingValueClick3 % 2 == 0) { this.ratingValue = 3 }
  //     this.ratingVal.emit(this.ratingValue)


  //   } else if (val == 4) {
  //     this.ratingValueClick4++;

  //     // this.ratingValue = 3.5;

  //     if (this.ratingValueClick4 % 2 == 0) { this.ratingValue = 4 }
  //     this.ratingVal.emit(this.ratingValue)


  //   } else if (val == 5) {
  //     this.ratingValueClick5++;

  //     // this.ratingValue = 4.5;

  //     if (this.ratingValueClick5 % 2 == 0) { this.ratingValue = 5 }
  //     this.ratingVal.emit(this.ratingValue)

  //   }
  // }


}
