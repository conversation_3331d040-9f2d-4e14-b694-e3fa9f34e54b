<!-- <div class="my-4" >
    <ul class="flex">
      <li (click)="ratingValues(1)" *ngIf="(ratingValue >= 1)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg"></li>
      <li (click)="ratingValues(1)" *ngIf="(ratingValue == 0)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-empty-star.svg"></li>
      <li (click)="ratingValues(1)" *ngIf="(ratingValue == 0.5)"><img class="w-7 h-7 mx-0.5"  src="assets/icons/review-half-star.svg"></li>

      <li (click)="ratingValues(2)" *ngIf="(ratingValue == 2 || ratingValue == 2.5 || ratingValue == 3 || ratingValue == 3.5 || ratingValue == 4 || ratingValue == 4.5 || ratingValue == 5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg"></li>
      <li (click)="ratingValues(2)" *ngIf="(ratingValue < 1.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-empty-star.svg"></li>
      <li (click)="ratingValues(2)" *ngIf="(ratingValue == 1.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-half-star.svg"></li>

      <li (click)="ratingValues(3)" *ngIf="(ratingValue == 3 || ratingValue == 3.5 || ratingValue == 4 || ratingValue == 4.5 || ratingValue == 5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg"></li>
      <li (click)="ratingValues(3)" *ngIf="(ratingValue < 2.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-empty-star.svg"></li>                
      <li (click)="ratingValues(3)" *ngIf="(ratingValue == 2.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-half-star.svg"></li>

      <li (click)="ratingValues(4)" *ngIf="(ratingValue == 4 || ratingValue == 4.5 || ratingValue == 5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg"></li>
      <li (click)="ratingValues(4)" *ngIf="(ratingValue < 3.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-empty-star.svg"></li>
      <li (click)="ratingValues(4)" *ngIf="(ratingValue == 3.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-half-star.svg"></li>

      <li (click)="ratingValues(5)" *ngIf="(ratingValue == 5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg"></li>
      <li (click)="ratingValues(5)" *ngIf="(ratingValue < 4.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-empty-star.svg"></li>
      <li (click)="ratingValues(5)" *ngIf="(ratingValue == 4.5)"><img class="w-7 h-7 mx-0.5" src="assets/icons/review-half-star.svg"></li>
    </ul>
  </div> -->
<div class="my-4 cursor-pointer">
  <ul class="flex">
    <li *ngIf="hoverValue < 1 && activeNumber < 1" (mouseover)="hoverValue = 1"><img class="w-7 h-7 mx-0.5"
        src="assets/icons/review-empty-star.svg"></li>
    <li *ngIf="hoverValue >= 1 || activeNumber>=1" (mouseleave)="hoverValue = 0" (click)="ratingValues(1)">
      <img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg">
    </li>

    <li *ngIf="hoverValue < 2 && activeNumber < 2" (mouseover)="hoverValue = 2">
      <img class="w-7 h-7 mx-0.5" src="assets/icons/review-empty-star.svg">
    </li>
    <li *ngIf="hoverValue >= 2 || activeNumber>=2" (mouseleave)="hoverValue = 0" (click)="ratingValues(2)">
      <img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg">
    </li>

    <li *ngIf="hoverValue < 3 && activeNumber < 3" (mouseover)="hoverValue = 3">
      <img class="w-7 h-7 mx-0.5" src="assets/icons/review-empty-star.svg">
    </li>
    <li *ngIf="hoverValue >= 3 || activeNumber>=3" (mouseleave)="hoverValue = 0" (click)="ratingValues(3)">
      <img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg">
    </li>


    <li *ngIf="hoverValue < 4 && activeNumber < 4" (mouseover)="hoverValue = 4"><img class="w-7 h-7 mx-0.5"
        src="assets/icons/review-empty-star.svg"></li>
    <li *ngIf="hoverValue >= 4 || activeNumber >= 4" (mouseleave)="hoverValue = 0" (click)="ratingValues(4)">
      <img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg">
    </li>


    <li *ngIf="hoverValue < 5 && activeNumber < 5" (mouseover)="hoverValue = 5"><img class="w-7 h-7 mx-0.5"
        src="assets/icons/review-empty-star.svg"></li>
    <li *ngIf="hoverValue >= 5 || activeNumber>=5" (mouseleave)="hoverValue = 0" (click)="ratingValues(5)">
      <img class="w-7 h-7 mx-0.5" src="assets/icons/review-full-star.svg">
    </li>
  </ul>
</div>