import { Component, Input, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { BlogCommentsService } from 'src/app/Services/Blogs/Blog-comments/blog-comments.service';
// import { BlogsService } from 'src/app/Services/Blogs/blogs.service';
import { TranslateService } from '@ngx-translate/core';


configSettings
@Component({
  selector: 'app-comments',
  templateUrl: './comments.component.html',
  styleUrls: ['./comments.component.scss']
})
export class CommentsComponent implements OnInit {

  @Input() blogDetailID: any
  @Input() mainComment: any
  isUserLoggedIn: boolean;
  isLoggedIn: boolean;

  constructor(

    private blogCommentsService: BlogCommentsService,
    private toastr: ToastrService,
    private configSettings: configSettings,
    private translateService: TranslateService,
  ) { }



  showInputField = false
  subComments: any
  ngOnInit(): void {
    this.subComments = this.mainComment?.childComments
    this.loginDetail()
  }
 loginDetail(){
    this.configSettings.getIsUserLoggedIn().subscribe(res => {
      this.isUserLoggedIn = res
      this.isLoggedIn = res    
    })
  }

  replyButton(data: any) {

    if ( this.isLoggedIn == false) {
      this.translateService.get('Please login first').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (data.replyComment === '') {
      // this.toastr.error('', 'Enter a reply to comment')
      this.translateService.get('Enter a reply to comment').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }

    const getParams = {}

    const posttParams = {
      blog_id: this.blogDetailID,
      parent_comment_id: this.mainComment.id,
      user_id: this.configSettings.getUserDetails().user_id,
      comment: data.replyComment
    }



    this.blogCommentsService.blogComments(getParams, posttParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.toastr.success('', response.body.message)
            this.blogCommentsService.setComments(response.body.data.comments)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  ismoreReply = true

  showReply() {
    this.ismoreReply = !this.ismoreReply
  }

}
