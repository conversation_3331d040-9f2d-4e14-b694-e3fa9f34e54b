<div class="flex gap-3 lg:gap-2 mt-4 w-full">
    <div class="w-12 lg:w-16">
        <img [src]="mainComment?.profile_img" class="w-full lg:w-12 lg:h-12 aspect-[1/1] rounded-full inline-block">
    </div>
    <div class="w-full mt-3">
        <p class="text-white text-base font-semibold inline-block">{{mainComment?.name}}<span
                class="text-[#56708E] ml-4 text-xs">{{mainComment?.created_at}}</span></p>
        <p class="text-white text-xs mt-3">{{mainComment?.comments}}</p>
        <p *ngIf="showInputField === false" class="text-accent text-base mt-2 cursor-pointer"
            (click)="showInputField = true">{{"Reply" | translate}}</p>
        <form *ngIf="showInputField === true" class="flex items-center mt-2 gap-3" #reply="ngForm"
            (ngSubmit)="replyButton(reply.value)">
            <input type="text" name="replyComment" class="w-full p-2 outline-none bg-lightGray" ngModel
                placeholder="{{'Enter your reply' | translate}}">
            <button class="text-accent text-base cursor-pointer block">{{"Reply" | translate}}</button>
        </form>
        
        <div *ngIf="ismoreReply == false">
            <div *ngFor="let mainComment of subComments">
                <app-comments *ngIf="subComments?.length > 0" [mainComment]="mainComment" [blogDetailID]="blogDetailID">
                </app-comments>
            </div>
        </div>

        <div *ngIf="subComments?.length > 0">
            <button *ngIf="ismoreReply == true" type="button" (click)="showReply()"
                class="px-8 rounded-full text-xs text-amber-400 py-3">
                ----{{"View More Reply" | translate}}
            </button>
            <button *ngIf="ismoreReply == false" type="button" (click)="showReply()"
                class="px-8 rounded-full text-xs text-amber-400 py-3">
                ----{{"Hide Reply" | translate}}
            </button>
        </div>

        
    </div>
</div>