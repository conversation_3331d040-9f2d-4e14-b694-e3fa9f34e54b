import { Component, Input, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { FavoritesService } from 'src/app/Services/Favorites/favorites.service';
import { Output, EventEmitter } from '@angular/core';
@Component({
  selector: 'app-favorite-card',
  templateUrl: './favorite-card.component.html',
  styleUrls: ['./favorite-card.component.scss']
})
export class FavoriteCardComponent implements OnInit {

  @Input() favorite: any
  userID: any
  is_fav: any
  @Output() updateFavs = new EventEmitter<any>();

  constructor(
    private configSettings: configSettings,
    private favoritesService: FavoritesService,
    private toastr: ToastrService
  ) { }

  ngOnInit(): void {
    this.userID = this.configSettings.getUserID()
    this.is_fav = this.favorite.is_favourite

    console.log(this.favorite)
  }


  toggleFav(num: number, event: any) {
    this.configSettings.setShowLoader(true)
    event.stopPropagation();
    const getParams = {}
    const postParams = {
      "user_id": this.userID,
      "type": "P",
      "type_id": this.favorite.id,
      "like": num
    }


    this.favoritesService.toggleFavorite(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.is_fav === 0 ? this.is_fav = 1 : this.is_fav = 0
            this.updateFavs.emit('')
          }
        }
      },
      error: (err) => {
        this.configSettings.setShowLoader(false)

        this.toastr.error('', err.error.message);
      }
    })
  }
}
