    <div [routerLink]="['/property-details/' + favorite.id]" class="h-72 w-full mx-auto relative cursor-pointer">
        <div class="bg-cover h-full w-full relative"
            [ngStyle]="{'background-image': 'url(' + favorite.thumbnail + ')'}">
            <!-- <img src="../../../assets/icons/red-favorite.png" class="h-7 w-7 absolute top-4 right-4" alt=""> -->
            <div class="h-8 w-8 absolute top-4 right-4 hover:cursor-pointer hover:scale-105">
                <img *ngIf="favorite.is_favourite === 0" src="../../../assets/icons/favorite.svg" (click)="toggleFav(1,$event)">
                <img *ngIf="favorite.is_favourite === 1" src="../../../assets/icons/favorite-red.svg" (click)="toggleFav(0,$event)">
            </div>
            <img [src]="favorite.logo"
                class="h-16 w-16 rounded-full absolute top-[50%] left-2" alt="">
            <div class="absolute bottom-2 left-2 right-2">
                <p class="font-bold">{{favorite.title}}</p>
                <div class="flex items-center gap-1 text-sm text-offwhite"><img
                        src="../../../assets/icons/location.png" class="h-3 w-3">{{favorite.state}}</div>
                <div class="text-end text-accent font-bold">{{favorite.price | number:'1.0'}} KD / {{"Month" | translate}}</div>
            </div>
        </div>
    </div>