import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-review-card',
  templateUrl: './review-card.component.html',
  styleUrls: ['./review-card.component.scss']
})
export class ReviewCardComponent implements OnInit {

  constructor() { }

  @Input() review :any

  
  stars: any = []
  halfStar: any = false
  emptyStars: any = []

  ngOnInit(): void {
    this.setRating()
  }



  setRating(){
    const rating = this.review.rating? this.review.rating : 0

    for (let i = 0; i < Math.floor(rating); i++) {
      this.stars.push('star')
    }


    if (!(parseInt(rating) == rating)) {
      this.halfStar = true
    }

    const emptyLength = 5 - (this.stars.length + ((this.halfStar) ? 1 : 0))

    for (let i = 0; i < emptyLength; i++) {
      this.emptyStars.push('empty')
    }

  }

}
