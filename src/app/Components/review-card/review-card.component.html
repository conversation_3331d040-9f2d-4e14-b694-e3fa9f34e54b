<div class=" border-b-2 border-gray-100 grid grid-cols-3 py-6" >
    <div class="col-span-2 mb-4">
        <div class="rounded-full inline-block">
            <img src="{{review?.profile_image}}" alt=""
                class="w-11 h-11 rounded-full">
        </div>
        <div class="inline-block px-4">
            <p class="font-semibold"> {{review?.name}} </p>
            <ul class="my-1">
                <li class="flex gap-2">
                    <img *ngFor="let item of stars" src="../../../assets/icons/review-full-star.svg"
                                class="w-3 h-3" alt="">
                                <img *ngIf="halfStar" src="../../../assets/icons/review-half-star.svg" class="w-3 h-3"
                                alt="">
                                <img *ngFor="let item of emptyStars" src="../../../assets/icons/review-empty-star.svg"
                                    class="w-3 h-3" alt="">
                </li>
                <!-- <li class="inline-block"><img src="../../../assets/icons/star-g-icon.png"
                        alt="" class="w-3"></li>
                <li class="inline-block"><img src="../../../assets/icons/star-g-icon.png"
                        alt=" " class="w-3"></li>
                <li class="inline-block"><img src="../../../assets/icons/star-g-icon.png"
                        alt="" class="w-3"></li>
                <li class="inline-block"><img src="../../../assets/icons/star-g-icon.png"
                        alt="" class="w-3"></li>
                <li class="inline-block"><img src="../../../assets/icons/star-w-icon.png"
                        alt="" class="w-3"></li> -->
            </ul>

        </div>
    </div>
    <div class=" col-span-3 lg:col-span-2">
        <h5 class="text-sm font-semibold pb-2 pt-1">{{review?.title}}t</h5>
        <p class="text-sm text-primary text-justify">
            {{review?.comments}}
        </p>

    </div>
</div>