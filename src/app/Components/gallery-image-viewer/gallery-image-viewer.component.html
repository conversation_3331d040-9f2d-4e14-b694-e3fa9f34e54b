<div class="bg-overlay fixed inset-0 z-[99]">
    <div *ngIf="this.galleryArr?.length > 0"
        class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white w-11/12 md:w-1/2 lg:w-2/3  xl:w-1/2  rounded-xl">
        <div>
            <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b">
                <h5 class="text-lg font-bold leading-normal ">
                    Gallery
                </h5>
                <button type="button" class="flex hover:cursor-pointer " (click)="closeGalleryFun()">
                    <img src="../../../assets/icons/close-icon.png" alt="" class="w-4 mr-1">
                    <span 
                        class="text-accent hover:text-accentDark duration-200 ease-in-out ">Close</span>
                </button>
            </div>
            <div class="h-full">
                <!-- carousal -->
                <div class="justify-center items-center relative container ">
    
                    <div class="h-full">
                        <owl-carousel-o [options]="galleryOptions" #gallery class="galleryCarousal ">
                            <!-- <ng-template carouselSlide>
                                <img src="../../../assets/images/temp-carousal-imgs/2.jpg" class=" object-cover" alt="">
                            </ng-template>
                            <ng-template carouselSlide>
                                <img src="../../../assets/images/temp-carousal-imgs/2.jpg" class=" object-cover" alt="">
                            </ng-template> -->
                            <ng-container *ngFor="let img of galleryArr">
                                <ng-template carouselSlide>
                                    <img class="object-cover " [src]="img">
                                </ng-template>
                            </ng-container>
                        </owl-carousel-o>
                    </div>
    
                    <p (click)="gallery.prev()" class="hover:cursor-pointer absolute top-1/2 left-2 hover:scale-105 z-10">
                        <img class="h-7 w-7" src="assets/icons/arrow-left.png">
                    </p>
                    <p (click)="gallery.next()" class="hover:cursor-pointer absolute top-1/2 right-2 hover:scale-105 z-10">
                        <img class="h-7 w-7" src="assets/icons/arrow-right.png">
                    </p>
                </div>
            </div>
        </div>
    </div>

    

</div>