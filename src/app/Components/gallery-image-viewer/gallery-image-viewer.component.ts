import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { ShareImagesService } from 'src/app/Services/ShareImages/share-images.service';

@Component({
  selector: 'app-gallery-image-viewer',
  templateUrl: './gallery-image-viewer.component.html',
  styleUrls: ['./gallery-image-viewer.component.scss']
})
export class GalleryImageViewerComponent implements OnInit {

  constructor(
    private shareImagesService: ShareImagesService
  ) { }

  @Output() closeGallery : EventEmitter<any> = new EventEmitter()

  ngOnInit(): void {
  }

  closeGalleryFun(){
    this.closeGallery.emit()
  }



  galleryArr: any = []

   // gallery carousal
   galleryOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    rtl: false,
    autoHeight: true,
    autoWidth: true,
    center: false,
    margin: 25,
    responsive: {
      0: {
        items: 1,
      },
      640: {
        items: 1
      },
      768: {
        items: 1
      },
      896: {
        items: 1
      },
      1024: {
        items: 1,
      },
      1280: {
        items: 1,
      },
      1536: {
        items: 1
      }
    },
    nav: false
  }

  setImage(value: any){
    
    this.galleryArr = value    

    
  }

}
