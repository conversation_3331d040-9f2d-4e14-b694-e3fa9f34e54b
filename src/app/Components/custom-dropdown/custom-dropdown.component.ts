import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-custom-dropdown',
  templateUrl: './custom-dropdown.component.html',
  styleUrls: ['./custom-dropdown.component.scss']
})
export class CustomDropdownComponent implements OnInit {

  constructor() { }
  @Input() placeholder: any
  @Input() list: any

  @Output() dropDownID = new EventEmitter<string>();

  open: boolean = false
  placeholderColor: any = 'text-grayPH'

  ngOnInit(): void {
    // console.log(this.list)
  }

  dropDown() {
    this.open = !this.open
  }

  option(e: any) {
    this.placeholder = e.target.innerText
    this.placeholderColor = 'text-black'
    this.dropDownID.emit(e.target.id)
  }

  clickedOutside() {
    this.open = false
  }
}
