<div class="drop-down cursor-pointer relative" (click)="dropDown()" clickedOutside (clickedOutside)="clickedOutside()">
    <div id="main" class="h-14 outline-none  px-4 py-4 rounded-md  placeholder:text-lg bg-white relative" [ngClass]="placeholderColor">{{placeholder}}
            <span class="absolute rtl:left-4 ltr:right-4 top-1/2 h-[0.9rem] w-[0.9rem] -translate-y-1/2"><img class="h-full w-full" src="../../../assets/icons/arrow-semi-down.svg"></span>
    </div>
    <div *ngIf="open" class="absolute mt-1 bg-white min-w-[10rem] w-full text-gray-500  shadow-md rounded-md overflow-hidden">
        <ul>
            <li [id]="item.id" *ngFor="let item of list " class="hover:bg-accent hover:text-gray-900 p-2" (click)="option($event)">{{item.name}}</li>
        </ul>
    </div>
</div>