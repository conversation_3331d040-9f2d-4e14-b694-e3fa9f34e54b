<div (click)="takeMeToBack()" class="bg-overlay fixed inset-0 z-[99]"></div>
<div
    class="z-[100] mx-auto fixed top-1/2 -translate-y-1/2  left-1/2 -translate-x-1/2 px-6 review-main  sm:px-10 py-5 bg-white w-[94%] sm:w-[29rem] rounded-md">
    <div>
        <div class="modal-header flex flex-shrink-0 items-center justify-between  rounded-t-md mb-3">
            <h5 class="text-base font-medium sm:text-xl sm:font-base leading-normal text-black">
                {{"Write a review for an architect" | translate}}
            </h5>
            <button type="button" (click)="takeMeToBack()"
                class="flex items-center gap-1 hover:cursor-pointer duration-200  rounded-full ">
                <img src="../../../assets/icons/close-icon.png" alt="" class="w-4">
                <span class="text-accent text-sm">{{"Close" | translate}}</span>
            </button>
        </div>

        <div class="flex gap-2 items-center">
            <img class="block h-14 rounded-full w-14" src="{{data?.company_logo}}" alt="">
            <span class="text-primary text-lg font-bold"> {{data?.name}} </span>
        </div>
        <div class="w-52 mx-auto">
            <img src="../../../assets/images/write-reviewbg-image.svg" class="w-full">
        </div>

        <div>
            <!-- <ul class="cursor-pointer">
                <li class="inline-block px-1" ><img src="../../../assets/icons/star-g-icon.png" alt="" class="w-6"></li> 
                <li class="inline-block px-1"><img src="../../../assets/icons/star-g-icon.png" alt=" " class="w-6"></li>
                <li class="inline-block px-1"><img src="../../../assets/icons/star-g-icon.png" alt="" class="w-6"></li>
                <li class="inline-block px-1"><img src="../../../assets/icons/star-w-icon.png" alt="" class="w-6"></li>
                <li class="inline-block px-1"><img src="../../../assets/icons/star-w-icon.png" alt="" class="w-6"></li>
            </ul> -->

            <app-add-rating (ratingVal)="getRatingVal($event)"></app-add-rating>
        </div>

        <form class="mt-5">
            <div class="border flex justify-between items-center p-2 rounded-sm mb-4 relative">
                <input [(ngModel)]="title" [ngModelOptions]="{standalone: true}"
                    class="outline-none text-sm w-full placeholder:text-xs" type="text"
                    placeholder="{{'Review Title' | translate}}">
            </div>
            <div class="border flex justify-between items-center p-2 rounded-sm mb-4 relative">
                <textarea [(ngModel)]="comments" [ngModelOptions]="{standalone: true}"
                    placeholder="{{'Write review here...' | translate}}" rows="5"
                    class="outline-none text-sm w-full placeholder:text-xs resize-none"></textarea>
            </div>
            <button
                class="w-5/6 block bg-primary hover:bg-blue-900 text-white p-2 mx-auto rounded-sm transition-all duration-150"
                (click)="postReviews()">{{"Sumbit" | translate}}</button>
        </form>

    </div>
</div>