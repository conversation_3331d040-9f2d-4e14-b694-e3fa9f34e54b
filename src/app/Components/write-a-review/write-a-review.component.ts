import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { ReviewService } from 'src/app/Services/Review/review.service';

@Component({
  selector: 'app-write-a-review',
  templateUrl: './write-a-review.component.html',
  styleUrls: ['./write-a-review.component.scss']
})
export class WriteAReviewComponent implements OnInit {

  constructor(
    private configSettings: configSettings,
    private router: Router,
    private translateService: TranslateService,
    private toastr: ToastrService,
    private reviewService: ReviewService
  ) {
    this.user = this.configSettings.getUserDetails();
    this.userId = this.user?.user_id

  }

  ngOnInit(): void {
  }

  user: any
  userId: any
  title: string = ''
  ratingCount: number = 0
  comments: string = ''

  @Input() data: any
  @Output() close: EventEmitter<any> = new EventEmitter<any>()
  @Output() reviewAdded: EventEmitter<any> = new EventEmitter<any>()

  takeMeToBack() {
    this.ratingCount = 0;
    this.comments = ''
    this, this.title = ''
    this.configSettings.setIsOTPVerified(true)
    this.close.emit()
    document.body.style.overflow = "auto";
    // this.location.back()
  }



  postReviews() {
    const getParams = {

    }
    if (this.title === "") {

      this.translateService.get('Title cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      
      return
    }

    
    if (this.comments === "") {
      
      this.translateService.get('Comment cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      
      return
    }
    


    const postParams = {
      user_id: this.userId,
      type: "A",
      type_id: this.data?.id,
      title: this.title,
      nick_name: "",
      ratings: this.ratingCount,
      comments: this.comments
    }

    this.reviewService.architectReviews(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.reviewAdded.emit()
            this.close.emit()
            this.toastr.success(response?.body?.message)
            document.body.style.overflow = "auto";

          }
        }
      },
      error: (err) => {
        this.toastr.error(err)
      }
    })


  }


  getRatingVal(event: any) {
    this.ratingCount = event
  }




}
