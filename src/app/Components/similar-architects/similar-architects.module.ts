import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SimilarArchitectsComponent } from './similar-architects.component';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SharedPipesModule } from 'src/app/Pipes/shared-pipes.module';
import { TranslateModule } from '@ngx-translate/core';



@NgModule({
  declarations: [SimilarArchitectsComponent],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    SharedPipesModule,
    TranslateModule
  ],
  exports: [SimilarArchitectsComponent]
})
export class SimilarArchitectsModule { }
