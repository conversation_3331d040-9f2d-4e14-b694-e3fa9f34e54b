<div class="">
    <div class="">
        <div class="col-span-12 lg:col-span-6 cursor-pointer" [routerLink]=" ['/architect-details',item?.id]">
            <div class=" bg-white p-5 lg:rounded-md">
                <div class="block md:block lg:grid xl:grid grid-cols-12 gap-4 lg:h-14 xl:h-16 2xl:h-20" >
                    <div class="col-span-6 sm:mb-5 ">
                        <div class="grid grid-cols-12 gap-4">
                            <div class="col-span-4">
                                <img src="{{item?.logo}}"
                                    class="sm:h-full sm:w-full h-16 w-16 aspect-[1/1] rounded-full object-cover" alt="">
                            </div>
                            <div class="col-span-8">
                                <p class="text-black text-sm font-semibold mt-2">{{item?.name}} <br>
                                    <span *ngIf="item?.rating > 0"
                                        class="bg-accent font-normal text-white text-sm px-1">
                                         &#9733; {{item?.rating | number : '1.0-1' }}</span>
                                </p>
                                <p class="text-slate-400 text-sm font-medium">{{item?.country}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-6 mt-4 md:mt-0">
                        <!-- <ul class="flex gap-3">
                            <li class="bg-[#2979cf]  inline-block overflow-hidden rounded"><a
                                    href="mailto:{{architectDetails?.email}}" class="block px-3 py-3 "><img
                                        class="w-5 h-5" src="../../../assets/icons/Message.svg" alt=""></a></li>
                            <li class="bg-[#002349]  inline-block overflow-hidden rounded"><a
                                    href="tel:{{architectDetails?.phone}}" class="block px-3 py-3 "><img
                                        class="w-5 h-5" src="../../../assets/icons/localphone.svg" alt=""></a>
                            </li>
                            <li class="bg-[#0ca512]  inline-block overflow-hidden rounded"><a
                                    href="{{architectDetails?.facebook}}" class="block px-3 py-3 "><img
                                        class="w-5 h-5" src="../../../assets/icons/whatsapp.svg" alt=""></a>
                            </li>
                            <li class="bg-accent  inline-block overflow-hidden rounded"><a
                                    href="{{architectDetails?.facebook}}" class="block px-3 py-3 "><img
                                        class="w-6 h-6" src="../../../assets/icons/chatbubbles.svg" alt=""></a>
                            </li>
                        </ul> -->

                        <ul class="flex gap-1 md:gap-3 lg:gap-3 xl:justify-end justify-start ">
                            <li (click)="$event.stopPropagation()" *ngIf="item?.email" class="bg-[#2979cf]  inline-block overflow-hidden rounded">
                                <a href="mailto:{{item?.email}}" class=""><img class="block p-1 md:p-1 lg:p-2"
                                        src="../../../assets/icons/Message.svg" alt=""></a>
                            </li>
                            <li (click)="$event.stopPropagation()" *ngIf="item?.phone" class="bg-[#002349]  inline-block overflow-hidden rounded"><a
                                    href="tel:{{item?.phone}}" class=""><img class="block p-1 md:p-1 lg:p-2"
                                        src="../../../assets/icons/localphone.svg" alt=""></a>
                            </li>
                            <li (click)="$event.stopPropagation()" *ngIf="item?.whatsapp" class="bg-[#0ca512]  inline-block overflow-hidden rounded"><a
                                    href="{{item?.whatsapp}}" class=""><img class="block p-1 md:p-1 lg:p-2"
                                        src="../../../assets/icons/whatsapp.svg" alt=""></a>
                            </li>
                            <li (click)="$event.stopPropagation()" [routerLink]="['/my-account/chats/' + item?.id ]" class="bg-accent  inline-block overflow-hidden rounded"><a
                                    class=""><img class="block p-1 md:p-1 lg:p-2"
                                        src="../../../assets/icons/chatbubbles.svg" alt=""></a>
                            </li>


                        </ul>

                    </div>

                </div>
                <div class="mt-3 cursor-pointer" [routerLink]="['/architect-details',item?.id]">
                    <p *ngIf="item?.project_delivered > 0"
                        class="text-xs pt-1 md:text-sm lg:text-sm font-semibold text-black"><span
                            class="text-slate-300  font-medium text-sm">{{"Delivered"| translate}} :</span>
                        {{item?.project_delivered}}
                        {{"Project"| translate}}</p>
                    <p *ngIf="item?.project_expertise?.length > 0"
                        class="text-xs pt-1 md:text-sm lg:text-sm font-medium text-black"><span
                            class="text-slate-300  font-medium">{{"Expertise"| translate}} :</span> {{
                        joinArray(item?.project_expertise)
                        | truncate:[charCount,'...']}}</p>
                    <p *ngIf="item?.project_types?.length > 0"
                        class="text-xs pt-1 md:text-sm lg:text-sm font-medium text-black"><span
                            class="text-slate-300  font-medium">{{"Projects Type"| translate}} :
                        </span> {{ joinArray(item?.project_types) | truncate:[charCount,'...'] }}</p>
                </div>
                <div>
                    <div class="mt-3">
                        <div class="grid grid-cols-12 gap-4">
                            <!-- commented for future use -->
                            <!-- (click)="getImages(item?.project_images,0)" -->
                            <div class="col-span-6 hover:cursor-pointer  ">
                                <img src="{{item?.project_images[0]}}" class="w-full h-full aspect-video" alt="">
                            </div>
                            <div class="col-span-6 hover:cursor-pointer  ">
                                <img src="{{item?.project_images[1]}}" class="w-full h-full aspect-video" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="mt-3" *ngIf="item?.project_images.length > 3">
                        <div class="grid grid-cols-12 gap-4">
                            <div class="col-span-4 hover:cursor-pointer  ">
                                <img src="{{item?.project_images[2]}}" class="w-full h-full aspect-video" alt="">
                            </div>
                            <div class="col-span-4 hover:cursor-pointer  ">
                                <img src="{{item?.project_images[3]}}" class="w-full h-full aspect-video" alt="">
                            </div>
                            <div class="col-span-4 hover:cursor-pointer  ">
                                <img src="{{item?.project_images[4]}}" class="w-full h-full aspect-video" alt="">
                            </div>
                        </div>
                    </div>

                </div>
                <div class="mt-3 text-center">
                    <a [routerLink]="['/architect-details',item?.id]"
                        class="text-neutral text-sm text-center font-semibold" href="">{{"View All Projects"|
                        translate}}</a>
                </div>
            </div>
        </div>

    </div>


</div>