import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ShareImagesService } from 'src/app/Services/ShareImages/share-images.service';

@Component({
  selector: 'app-similar-architects',
  templateUrl: './similar-architects.component.html',
  styleUrls: ['./similar-architects.component.scss']
})
export class SimilarArchitectsComponent implements OnInit {

  constructor(
    public router: Router,
    private route: ActivatedRoute,
    private shareImagesService: ShareImagesService
  ) { }

  isSmallDevice: boolean = false
  charCount : number = 0

  @Input() item: any
  @Input() page: any
  @Output()sendImageData : EventEmitter<any> = new EventEmitter<any>()

  ngOnInit(): void {
    this.isSmallDevice = window.innerWidth < 1024

    if (this.page != 'details') {
      this.isSmallDevice == true ? this.charCount = 35 : this.charCount = 70
    } else {
      this.isSmallDevice == true ? this.charCount = 35 : this.charCount = 48
    }
  }


  joinArray(value:Array<any>){
    let valArr:any = []
    value.forEach(e => {
      if (e != undefined || e != null) {
        valArr.push(e)
      }
    })
    return valArr.join(',')
  }

  getImages(value : any, index: number) {

    let imageObj = {
      value: value,
      index: index
    }

   this.shareImagesService.changeImage(imageObj)
    this.sendImageData.emit(imageObj)    
  }

}
