import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PostProjectFormComponent } from './post-project-form.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxDropzoneModule } from 'ngx-dropzone';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';


@NgModule({
  declarations: [PostProjectFormComponent],
  imports: [
    CommonModule,
    NgSelectModule,
    TranslateModule,
    NgxDropzoneModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  exports:[PostProjectFormComponent]
})
export class PostProjectFormModule { }
