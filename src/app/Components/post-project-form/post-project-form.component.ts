import { Component, ElementRef, OnInit, ViewChild, Output, EventEmitter } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { AddressService } from 'src/app/Services/Address/address.service';
import { ProjectsService } from 'src/app/Services/Projects/projects.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-post-project-form',
  templateUrl: './post-project-form.component.html',
  styleUrls: ['./post-project-form.component.scss']
})
export class PostProjectFormComponent implements OnInit {
  // currentPage: number = 2

  constructor(
    private projectsService: ProjectsService,
    private configSettings: configSettings,
    private toastr: ToastrService,
    private addressService: AddressService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private translateService: TranslateService,

  ) {
    this.configSettings.setShowLoader(true)
  }

  // VARIABLES

  // // PAGE 1 VARIABLES
  // userType: any
  // architectName: any
  // emailAddress: any
  // phoneNumber: any
  // whatsappNumber: any
  // facebook: any
  // twitter: any
  // instagram: any
  // youtube: any
  // website: any
  // aboutUsEn: any
  // aboutUsAr: any

  // // COMPANY LOGO 
  // @ViewChild('companyLogoBtn') companyLogoBtnRef: ElementRef
  // companyLogo: any

  // // COVER PHOTO
  // @ViewChild('coverPhotoBtn') coverPhotoBtnRef: ElementRef
  // coverPhoto: any

  // ///////////////////////////////////////////////////////////////

  // PAGE 2 VARIABLES
  projectNameEn: any
  projectNameAr: any
  projectDescEn: any
  projectDescAr: any
  aboutUsEn: any
  aboutUsAr: any
  establishmentYear: any

  // PROJECT PHOTOS
  projectPhotosArr: any = []
  @ViewChild('projectPhotosBtn') projectPhotosBtnRef: ElementRef

  // PROJECT PROFILE
  projectProfile: any = ''
  projectProfileArr: any = []
  @ViewChild('projectProfileBtn') projectProfileBtnRef: ElementRef

  // COUNTRY STATE AREA
  countries: any = []
  selectedCountryId: any = null

  allStates: any = []
  selectedStateId: any = ''

  allAreas: any = []
  selectedAreaId: any

  // PROJECT TYPES
  allProjectTypes: any = []
  selectedProjectTypes: any = [];

  // PROJECT PHOTOS
  // projectPhotos: any = []

  // PROJECT COVER PHOTO



  // EDITING SECTION
  is_edit: boolean = false
  editProjectID: any
  editCountry: any
  editState: any
  editArea: any
  editImages: any = []
  newImages: any = []

  editCoverPhoto: any


  @Output() projectParams = new EventEmitter<any>();
  maxPhotosCanPost: number;



  // /////////////////////////////////////////////////////////////////////////////////////////////////////

  ngOnInit(): void {

    if (localStorage.getItem('maxPhotos')) {
      this.maxPhotosCanPost = Number(localStorage.getItem('maxPhotos'))
    }


    this.getProjectTypes()
    this.activatedRoute.params.subscribe(params => {
      if (params['id']) {
        this.is_edit = true
        // this.configSettings.setShowLoader(true)
        this.editProjectID = params['id']
        this.getDetailsForEdit()
      }
    })
  }

  getProjectTypes() {
    this.projectsService.getProjectTypes({}).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allProjectTypes = response.body.data
            this.getCountries()

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }

  addProjectType(event: any) {
    this.selectedProjectTypes.push(event)
  }

  removeProjectType(event: any) {
    this.selectedProjectTypes.splice(
      this.selectedProjectTypes.indexOf(event.value),
      1
    );
  }

  getCountries() {
    const getParams = {}

    this.addressService.getCountry(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            let tempCountries = response.body.data // because im getting 3 different arrays of countries
            for (let i = 0; i < tempCountries.length; i++) {
              this.countries = [...tempCountries]
            }

            if (this.is_edit == true) {
              this.getStates(this.editCountry)
            }
            // this.propertyInformation.patchValue({
            //   country: 114
            // })
            // this.getStates(114)
            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  getStates(countryID: any) {
    this.propertyInformation.patchValue({
      state: null,
      area: null,
    })
    const getParams = {
      country_id: countryID

    }

    this.addressService.getState(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allStates = response.body.data
            if (this.is_edit == true) {
              this.getArea(this.editState)
              this.propertyInformation.patchValue({
                state: this.editState,
              })
              this.editState = null
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  getArea(stateID: any) {
    this.propertyInformation.patchValue({
      area: null,
    })

    const getParams = {
      state_id: stateID
    }

    this.addressService.getAreas(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {

            // console.log(response.body.data)
            this.allAreas = response.body.data
            this.configSettings.setShowLoader(false)

            if (this.is_edit == true) {
              this.propertyInformation.patchValue({
                area: this.editArea,
              })
              this.editArea = null
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  // open: boolean = false
  // dropDown() {
  //   this.open = !this.open
  // }

  // defaultPhone: any = '<span _ngcontent-ggp-c68=\"\" id=\"965\">965</span>'
  // phoneCode: any = '965'

  // option(e: any) {
  //   this.phoneCode = e.currentTarget.id
  //   this.defaultPhone = e.currentTarget.innerHTML
  // }



  // personalInformation = new FormGroup({
  //   userType: new FormControl(),
  //   architectName: new FormControl(''),
  //   emailAddress: new FormControl(''),
  //   phoneNumber: new FormControl(''),
  //   whatsappNumber: new FormControl(''),
  //   facebook: new FormControl(''),
  //   twitter: new FormControl(''),
  //   instagram: new FormControl(''),
  //   youtube: new FormControl(''),
  //   website: new FormControl(''),
  //   aboutUsEn: new FormControl(''),
  //   aboutUsAr: new FormControl(''),
  // })

  // getPersonalInfo() {
  //   this.userType = this.personalInformation.value.userType
  //   this.architectName = this.personalInformation.value.architectName
  //   this.emailAddress = this.personalInformation.value.emailAddress
  //   this.phoneNumber = this.personalInformation.value.phoneNumber
  //   this.whatsappNumber = this.personalInformation.value.whatsappNumber
  //   this.facebook = this.personalInformation.value.facebook
  //   this.twitter = this.personalInformation.value.twitter
  //   this.instagram = this.personalInformation.value.instagram
  //   this.youtube = this.personalInformation.value.youtube
  //   this.website = this.personalInformation.value.website
  //   this.aboutUsEn = this.personalInformation.value.aboutUsEn
  //   this.aboutUsAr = this.personalInformation.value.aboutUsAr

  //   this.currentPage = 2;
  //   window.scroll({
  //     top: 0,
  //     left: 0,
  //   });
  // }

  // ////////////////////////////////////////////////// START OF COMPANY IMAGES ///////////////////////////////////////////////

  // uploadCompanyImage(event: any) {
  //   if (event.target.files && event.target.files[0]) {
  //     const filesAmount = event.target.files.length;
  //     for (let i = 0; i < filesAmount; i++) {
  //       const reader = new FileReader();

  //       reader.onload = (event: any) => {
  //         this.companyLogo = event.target.result
  //         // this.companyLogo.patchValue({
  //         //   passportImage: event.target.result
  //         // });
  //         // console.log(this.images)
  //       }

  //       reader.readAsDataURL(event.target.files[i]);
  //       // console.log(event.target.files[i])
  //     }
  //   }
  // }


  // removeCompanyLogo() {
  //   this.companyLogo = ''
  //   // this.passportImage = ''
  //   // this.propertyInformation.patchValue({
  //   //   passportImage: ''
  //   // });
  // }

  // addCompanyLogo() {
  //   this.companyLogoBtnRef.nativeElement.click()
  // }

  // ////////////////////////////////////////////////// END OF COMPANY IMAGES ///////////////////////////////////////////////

  // ////////////////////////////////////////////////// START OF COVER PHOTOS ///////////////////////////////////////////////


  // uploadCoverPhoto(event: any) {
  //   if (event.target.files && event.target.files[0]) {
  //     const filesAmount = event.target.files.length;
  //     for (let i = 0; i < filesAmount; i++) {
  //       const reader = new FileReader();

  //       reader.onload = (event: any) => {
  //         this.coverPhoto = event.target.result
  //         console.log(this.coverPhoto)
  //         // this.companyLogo.patchValue({
  //         //   passportImage: event.target.result
  //         // });
  //         // console.log(this.images)
  //       }

  //       reader.readAsDataURL(event.target.files[i]);
  //       // console.log(event.target.files[i])
  //     }
  //   }
  // }


  // removeCoverPhoto() {
  //   this.coverPhoto = ''
  //   // this.passportImage = ''
  //   // this.propertyInformation.patchValue({
  //   //   passportImage: ''
  //   // });
  // }

  // addCoverPhoto() {
  //   this.coverPhotoBtnRef.nativeElement.click()
  // }

  ////////////////////////////////////////////////// END OF COVER PHOTOS ///////////////////////////////////////////////


  // Page 2

  propertyInformation = new FormGroup({
    projectNameEn: new FormControl(''),
    projectNameAr: new FormControl(''),
    projectDescEn: new FormControl(''),
    projectDescAr: new FormControl(''),
    country: new FormControl(),
    state: new FormControl(),
    area: new FormControl(),
    // establishmentYear: new FormControl(''),
    projectTypes: new FormControl([]),
    projectPhotos: new FormControl([]),
    projectProfile: new FormControl(''),
  })

  getPropertyInfo() {
    // console.log(this.propertyInformation.value)
    // // console.log(this.selectedProjectTypes)
    this.projectNameEn = this.propertyInformation.value.projectNameEn
    this.projectNameAr = this.propertyInformation.value.projectNameAr
    this.projectDescEn = this.propertyInformation.value.projectDescEn
    this.projectDescAr = this.propertyInformation.value.projectDescAr
    this.selectedCountryId = this.propertyInformation.value.country
    this.selectedStateId = this.propertyInformation.value.state
    this.selectedAreaId = this.propertyInformation.value.area
    // this.establishmentYear = this.propertyInformation.value.establishmentYear
    this.projectPhotosArr = this.propertyInformation.value.projectPhotos
    this.projectProfile = this.propertyInformation.value.projectProfile
    this.selectedProjectTypes = this.propertyInformation.value.projectTypes


    if (this.projectNameEn == '') {
      // this.toastr.warning('', 'Project name in English cannot be empty')
      this.translateService.get('Project name in english cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.projectNameAr == '') {
      // this.toastr.warning('', 'Project name in Arabic cannot be empty')
      this.translateService.get('Project name in arabic cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.projectDescEn == '') {
      // this.toastr.warning('', 'Project description in English cannot be empty')
      this.translateService.get('Project description in english cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.projectDescAr == '') {
      // this.toastr.warning('', 'Project description in Arabic cannot be empty')
      this.translateService.get('Project description in arabic cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.selectedProjectTypes.length == 0) {
      this.translateService.get('Please select atleast 1 project type').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.selectedCountryId == null) {
      this.translateService.get('Please select country').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.selectedStateId == null) {
      // this.toastr.warning('', 'Select State')
      this.translateService.get('Please select state').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.selectedAreaId == null) {
      // this.toastr.warning('', 'Select Area')
      this.translateService.get('Please select area').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.projectPhotosArr.length == 0) {
      this.translateService.get('Please select atleast 1 project image').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.projectProfile == '') {
      // this.toastr.warning('', 'Select cover image')
      this.translateService.get('Please select cover image').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }



    if (this.is_edit == true) {
      this.editProject()
    } else {
      this.postProject()
    }
  }

  ////////////////////////////////////////////////// START OF PROJECT PHOTOS ///////////////////////////////////////////////

  removeProjectImg(img: any) {
    if (this.is_edit == true) {
      this.configSettings.setShowLoader(true)
      if (img.media_id == '') {
        this.newImages.splice(this.newImages.indexOf(img), 1);
        this.projectPhotosArr = [...this.editImages, ...this.newImages]
        this.configSettings.setShowLoader(false)

      } else {

        const postParams = {
          media_id: img.media_id,
          project_id: this.editProjectID
        }

        this.projectsService.deleteMedia({}, postParams).subscribe({
          next: (response) => {
            if (response.status === 200) {
              if (response.body.status === 200) {

                this.projectPhotosArr = [...response.body.data, ...this.newImages]
                this.configSettings.setShowLoader(false)

              }
            }
          },
          error: (err) => {
            this.toastr.error('', err.error.message);
          }
        })
      }



    } else {
      this.projectPhotosArr.splice(this.projectPhotosArr.indexOf(img), 1);
    }
  }

  uploadProjectImgs(event: any) {
    if (this.projectPhotosArr.length < this.maxPhotosCanPost) {

      if (event.target.files && event.target.files[0]) {
        const filesAmount = event.target.files.length;


        for (let i = 0; i < filesAmount; i++) {
          const reader = new FileReader();
          if (this.projectPhotosArr.length + i === this.maxPhotosCanPost) {
            // console.log('test')
            // this.toastr.error('', `Cannot upload more than ${this.maxPhotosCanPost} photos as per the chosen plan`)

            this.translateService.get('Cannot upload more than #no photos as per the chosen plan').subscribe(res => {
              this.toastr.error('', res.replace('#no', this.maxPhotosCanPost));
            })

            break
          }


          reader.onload = (event: any) => {
            if (this.projectPhotosArr.length < this.maxPhotosCanPost) {

              if (this.is_edit == false) {

                this.projectPhotosArr.push({
                  id: '',
                  name: event.target.result
                });

                this.propertyInformation.patchValue({
                  projectPhotos: this.projectPhotosArr
                });

              }
              console.log(this.projectPhotosArr.length)

              if (this.is_edit == true) {
                this.newImages.push({
                  id: '',
                  name: event.target.result
                });

                this.projectPhotosArr = [...this.editImages, ...this.newImages]
                this.propertyInformation.patchValue({
                  projectPhotos: this.projectPhotosArr
                });
              }



            } else {
              this.translateService.get('Cannot upload more than #no photos as per the chosen plan').subscribe(res => {
                this.toastr.error('', res.replace('#no', this.maxPhotosCanPost));
              })
              return
            }
          }
          reader.readAsDataURL(event.target.files[i]);
        }
      }
    }

  }

  addProjectImgs() {
    this.projectPhotosBtnRef.nativeElement.click()
  }




  ////////////////////////////////////////////////// END OF PROJECT PHOTOS ///////////////////////////////////////////////

  ////////////////////////////////////////////////// START OF PROJECT PROFILE ///////////////////////////////////////////////

  uploadProjectProfile(event: any) {
    if (event.target.files && event.target.files[0]) {
      const filesAmount = event.target.files.length;
      for (let i = 0; i < filesAmount; i++) {
        const reader = new FileReader();

        reader.onload = (event: any) => {
          this.projectProfile = event.target.result
          this.propertyInformation.patchValue({
            projectProfile: event.target.result
          });
          // console.log(this.images)
        }

        reader.readAsDataURL(event.target.files[i]);
        // console.log(event.target.files[i])
      }
    }
  }


  removeProjectProfile() {
    this.configSettings.setShowLoader(true)

    if (this.is_edit == true) {
      const postParams = {
        project_id: this.editProjectID,
        is_cover_photo: "1"
      }

      this.projectsService.deleteMedia({}, postParams).subscribe({
        next: (response) => {
          if (response.status === 200) {
            if (response.body.status === 200) {
              this.configSettings.setShowLoader(false)
              this.projectProfile = ''
              this.configSettings.setShowLoader(false)
              this.propertyInformation.patchValue({
                projectProfile: ''
              });
            }
          }
        },
        //   error: (err) => {
        //     this.toastr.error('', err.error.message);
        // }
      })
    } else {
      this.projectProfile = ''
      this.configSettings.setShowLoader(false)
      this.propertyInformation.patchValue({
        projectProfile: ''
      });
      // this.passportImagesArr = ''
      // this.passportImage = ''
      // this.propertyInformation.patchValue({
      //   passportImage: ''
      // });
    }

    this.projectProfile = ''
    this.configSettings.setShowLoader(false)
    // this.passportImage = ''
    // this.propertyInformation.patchValue({
    //   passportImage: ''
    // });
  }

  addProjectProfile() {
    this.projectProfileBtnRef.nativeElement.click()
  }

  ////////////////////////////////////////////////// END OF PROJECT PROFILE ///////////////////////////////////////////////


  ////////////////////////////////////////////////// POST PROJECT ///////////////////////////////////////////////
  type: string = 'PR' //PR for project
  postProject() {

    const postProjectImages: any = []
    this.projectPhotosArr.forEach((el: any) => {
      if (el.id == '') {
        postProjectImages.push(el.name)
      }
    });

    const postParams = {
      user_id: this.configSettings.getUserID(),
      title_en: this.projectNameEn,
      title_ar: this.projectNameAr,
      desc_ar: this.projectDescAr,
      desc_en: this.projectDescEn,
      country_id: this.selectedCountryId,
      state_id: this.selectedStateId,
      area_id: this.selectedAreaId,
      project_types: this.selectedProjectTypes.join(),
      cover_photo: this.projectProfile,
      images: postProjectImages
    }

    this.projectParams.emit(postParams)

    // console.log(postParams)

    // this.projectsService.postProject({}, postParams).subscribe({
    //   next: (response) => {
    //     if (response.status === 200) {
    //       if (response.body.status === 200) {
    //         // console.log(response.body.data)
    //         this.configSettings.setShowLoader(false)
    //         this.router.navigate(['/my-account/my-projects'])
    //       }
    //     }
    //   },
    //   error: (err) => {
    //     this.toastr.error('', err.error.message);
    //   }
    // })
  }

  ////////////////////////////////////////////// EDIT PROJECT ////////////////////////////////////////////

  getDetailsForEdit() {
    const getParams = {
      project_id: this.editProjectID
    }

    this.projectsService.projectDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {

            this.editCountry = response.body.data.country_id
            this.editState = response.body.data.state_id
            this.editArea = response.body.data.area_id
            this.editImages = response.body.data.images
            this.projectPhotosArr = this.editImages
            this.projectProfile = response.body.data.cover_photo
            this.editCoverPhoto = this.projectProfile
            // const temp = response.body.data.project_types.split(",")
            // console.log(temp)
            this.propertyInformation.patchValue({
              projectNameEn: response.body.data.title_en,
              projectNameAr: response.body.data.title_ar,
              projectDescEn: response.body.data.desc_en,
              projectDescAr: response.body.data.desc_ar,
              country: response.body.data.country_id,
              state: response.body.data.state_id,
              // establishmentYear: ,
              projectTypes: response.body.data.project_types,
              projectPhotos: response.body.data.images,
              projectProfile: response.body.data.cover_photo,
            })
            // this.getStates(this.editCountry)
            // this.getArea(this.editState)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  editProject() {
    this.configSettings.setShowLoader(true)

    const postProjectImages: any = []
    this.projectPhotosArr.forEach((el: any) => {
      if (el.id == '') {
        postProjectImages.push(el.name)
      }
    });

    if (this.projectProfile !== '' && (this.editCoverPhoto === this.projectProfile)) {
      this.projectProfile = ''
    } else if (this.projectProfile == '') {
      // this.configSettings.setShowLoader(false)
      // this.translateService.get('Select cover image').subscribe(res => {
      //   this.toastr.error('', res);
      // })
      // return
    }

    const postParams = {
      user_id: this.configSettings.getUserID(),
      project_id: this.editProjectID,
      title_en: this.projectNameEn,
      title_ar: this.projectNameAr,
      desc_en: this.projectDescEn,
      desc_ar: this.projectDescAr,
      country_id: this.selectedCountryId,
      state_id: this.selectedStateId,
      area_id: this.selectedAreaId,
      project_types: this.selectedProjectTypes.join(),
      cover_photo: this.projectProfile,
      images: postProjectImages
    }

    console.log(postParams)

    this.projectsService.editProject({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.configSettings.setShowLoader(false)

            this.router.navigate(['/my-account/my-projects'])
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  routeTo() {
    // if (this.is_edit == true) {
      this.router.navigate(['/my-account/my-projects'])
    // } else {
    //   this.router.navigate(['/home'])
    // }
  }
}
