<div class="container  mx-auto px-4 sm:px-8  lg:px-24">
    <div class="container  mt-16 mb-7 mx-auto px-4 sm:px-8 md:px-10 lg:px-24 text-center text-white text-2xl font-bold">
        <h2>{{"POST PROJECT" | translate}}</h2>
    </div>
    <div class="rounded-t-lg overflow-hidden">
        <div
            class="container mx-auto px-6 sm:px-8 md:px-10 lg:px-24 bg-white custom-shadow relative z-10  border-accent border-b-2 cursor-default">
            <div class="flex justify-center text-lighterBlack py-4 ">
                <!-- <div class="flex justify-center items-center gap-4 text-lg font-bold ">
                    <span *ngIf="currentPage === 1"
                        class="w-8 h-8 bg-accent rounded-full flex justify-center items-center text-white">1</span>
                    <img *ngIf="currentPage !== 1" src="../../../assets/icons/property-page-done.svg" class="w-8 h-8">
                    <p>Personal Information</p>
                </div> -->
                <div class="flex justify-center items-center gap-4 text-2xl font-bold">
                    <!-- <span *ngIf="currentPage !== 3" [ngClass]="currentPage === 2 ? 'bg-accent' : 'bg-steelBlue'"
                        class="w-8 h-8 rounded-full flex justify-center items-center text-white">2</span> -->
                    <!-- <img *ngIf="currentPage === 3" src="../../../assets/icons/property-page-done.svg" class="w-8 h-8"> -->

                    <p>{{"Project Information" | translate}}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto px-6 sm:px-8 md:px-6 lg:px-24 bg-white pt-14 pb-24">

        <form [formGroup]="propertyInformation" (ngSubmit)="getPropertyInfo()">
            <div class="grid grid-cols-2 gap-x-12 lg:gap-x-20 xl:gap-x-32 gap-y-6 mb-12">
                <div class="col-span-full">
                    <p class="mb-3 text-primary">{{"Project Name" | translate}}<span class="text-red-600">*</span></p>
                    <div class=" flex flex-col md:flex-row gap-x-12 lg:gap-x-20 xl:gap-x-32 gap-y-6">
                        <input formControlName="projectNameEn" type="text" placeholder="English"
                            class="w-full bg-lightGray outline-none px-2 py-3">
                        <input formControlName="projectNameAr" type="text" placeholder="العربية"
                            class="w-full bg-lightGray outline-none px-2 py-3" dir="rtl">
                    </div>
                </div>
                <div class="col-span-full">
                    <p class="mb-3 text-primary">{{"Project Description" | translate}}<span
                            class="text-red-600">*</span></p>
                    <div class="flex flex-col md:flex-row gap-x-12 lg:gap-x-20 xl:gap-x-32 gap-y-6">
                        <textarea formControlName="projectDescEn"
                            class="border border-lightGray bg-lightGray outline-none w-full md:w-1/2 p-2 resize-none"
                            placeholder="English" name="" id="" cols="30" rows="10"></textarea>
                        <textarea formControlName="projectDescAr"
                            class="border border-lightGray bg-lightGray outline-none w-full md:w-1/2 p-2 resize-none"
                            dir="rtl" placeholder="العربية" name="" id="" cols="30" rows="10"></textarea>
                    </div>
                </div>
                <!-- <div class="col-span-full">
                    <p class=" text-xl text-primary font-bold mb-3">About Us</p>
                    <div class=" flex gap-32">
                        <textarea class="border border-lightGray bg-lightGray outline-none w-1/2 p-2 resize-none"
                            formControlName="aboutUsEn" placeholder="English" name="" id="" cols="30"
                            rows="10"></textarea>
                        <textarea class="border border-lightGray bg-lightGray outline-none w-1/2 p-2 resize-none"
                            formControlName="aboutUsAr" dir="rtl" placeholder="العربية" name="" id="" cols="30"
                            rows="10"></textarea>
                    </div>
                </div> -->

                <!-- <div class="mb-6 ">
                    <p class="mb-2  text-xl text-primary font-bold">{{"Establishment Year" | translate}}</p>
                    <input placeholder="Enter Year" formControlName="establishmentYear" type="text"
                        class="w-full bg-lightGray outline-none px-2 py-3">
                </div> -->

                <div class="h-full relative col-span-2 md:col-span-1">
                    <p class="mb-2 text-primary">{{"Project Types" | translate}}<span class="text-red-600">*</span></p>
                    <span
                        class="absolute  bottom-4 -translate-y-1/2 z-10 rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                        <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                    </span>
                    <ng-select formControlName="projectTypes" [multiple]="true" [closeOnSelect]="false"
                        (add)="addProjectType($event)" (remove)="removeProjectType($event)"
                        (clear)="selectedProjectTypes=[]" class="mt-3 text-black post-property-dropdown "
                        placeholder="{{'Choose Types' | translate}}">
                        <ng-option class="text-black" *ngFor="let projectType of allProjectTypes"
                            [value]="projectType.project_type_id">{{projectType.name}}
                        </ng-option>
                    </ng-select>
                </div>

                <div class=" relative col-span-2 md:col-span-1">
                    <p class="mb-2 text-primary">{{"Select Country" | translate}}<span class="text-red-600">*</span></p>
                    <span
                        class="absolute  bottom-4 -translate-y-1/2 z-[2] rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                        <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                    </span>
                    <ng-select [(ngModel)]="selectedCountryId" (change)="getStates(selectedCountryId)"
                        formControlName="country" class="post-property-dropdown"
                        placeholder="{{'Select Country' | translate}}">
                        <ng-option *ngFor="let country of countries" [value]="country.country_id"
                            [ngClass]="{'hidden': country.is_active=='No'}">
                            {{country.name |uppercase}}</ng-option>
                    </ng-select>
                </div>

                <div class=" relative col-span-2 md:col-span-1">
                    <p class="mb-2 text-primary">{{"Select State" | translate}}<span class="text-red-600">*</span></p>
                    <span
                        class="absolute  bottom-3 -translate-y-1/2 z-[2] rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                        <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                    </span>
                    <ng-select (change)="getArea($event)" formControlName="state" class="post-property-dropdown"
                        placeholder="{{'Select State' | translate}}">
                        <ng-option *ngFor="let state of allStates" [value]="state.state_id">
                            {{state.name}}</ng-option>
                    </ng-select>
                </div>

                <div class=" relative col-span-2 md:col-span-1">
                    <p class="mb-2 text-primary">{{"Select Area" | translate}}<span class="text-red-600">*</span></p>
                    <span
                        class="absolute  bottom-3 -translate-y-1/2 z-[2] rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                        <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                    </span>
                    <ng-select formControlName="area" class="post-property-dropdown"
                        placeholder="{{'Select Area' | translate}}">
                        <ng-option *ngFor="let area of allAreas" [value]="area.area_id">
                            {{area.name}}</ng-option>
                    </ng-select>
                </div>

                <div class="col-span-full">
                    <p class="mb-2 text-primary">{{"Add Project Photos" | translate}}<span class="text-red-600">*</span>
                    </p>
                    <div class="mb-3 text-sm sm:text-base"> {{'Number of Photos allowed for this plan:' | translate}} {{ + maxPhotosCanPost}}
                    </div>
                    <div class="flex gap-5">
                        <div *ngIf="projectPhotosArr?.length != 0"
                            class="border-2 border-[#c8c8c8] border-dashed  p-6 flex flex-wrap max-w-[50%] gap-3">
                            <div *ngFor='let url of projectPhotosArr' class="relative">
                                <img [src]="url.name" class="object-cover h-32 w-36 rounded-sm shadow-md">
                                <img (click)="removeProjectImg(url)" src="../../../assets/icons/remove-keyword.svg"
                                    class="h-4 w-4 absolute -top-2 -right-2 hover:cursor-pointer">
                            </div>

                        </div>

                        <div *ngIf="projectPhotosArr?.length !== maxPhotosCanPost" class="drag-here-img h-44 w-44 border border-[#969696] p-2 relative overflow-hidden ">
                            <div class="text-[.7rem] text-[#bdbdbd] absolute top-[45%] left-0 right-0 ">
                                <div class=" text-center mx-auto">{{"Drag image here" | translate}}</div>
                                <div class="relative text-center or"><span class="px-2 relative bg-white z-[3]">{{"or" |
                                        translate}}</span>
                                </div>
                            </div>
                            <input #projectPhotosBtn class="block h-full absolute inset-0 opacity-0 z-[1]" id="file"
                                multiple="" type="file" (change)="uploadProjectImgs($event)">
                            <div class="absolute bottom-4 z-[2]  text-center font-normal cursor-pointer text-[.65rem] right-0 left-0 mx-3 py-1 border-2 bg-gray-50 border-[#969696] text-primary rounded-md"
                                (click)="addProjectImgs()">{{"Select from device" | translate}}</div>
                              
                        </div>
                        <div *ngIf="projectPhotosArr?.length == maxPhotosCanPost" class="drag-here-img h-44 w-44 border border-[#969696] p-2 relative overflow-hidden mt-6">
                            <div class="text-[.7rem] text-[#bdbdbd] absolute top-[45%] left-0 right-0">
                                <div class=" text-center mx-auto">{{"Drag image here" | translate}}</div>
                                <div class="relative text-center or"><span class="px-2 relative bg-white z-[30]">{{"or" |
                                        translate}}</span>
                                </div>
                            </div>
                             <input #projectPhotosBtn class="block h-full absolute inset-0 opacity-0 z-[1]" id="file">
                                <div class="absolute bottom-4 z-[2]  text-center font-normal cursor-pointer text-[.65rem] right-0 left-0 mx-3 py-1 border-2 bg-gray-50 border-[#969696] text-primary rounded-md">{{"Select from device" | translate}}</div>
                                <div *ngIf="projectPhotosArr?.length == maxPhotosCanPost"
                                    class="block absolute inset-0  bg-gray-300 opacity-25 z-[30]"></div>
    
                        </div>
                    </div>
                    
                </div>
                <!-- ///////////////////////////////////////// -->
                <div class="col-span-full ">
                    <p class="mb-3 text-primary">{{"Add Cover Photo" | translate}}<span class="text-red-600">*</span>
                    </p>
                    <div class="flex gap-5">
                        <div *ngIf="projectProfile"
                            class="border-2 border-[#c8c8c8] border-dashed  p-6 flex flex-wrap max-w-[50%] gap-3">
                            <div class="relative">
                                <img [src]="projectProfile" class="object-cover h-32 w-36 rounded-sm shadow-md">
                                <img (click)="removeProjectProfile()" src="../../../assets/icons/remove-keyword.svg"
                                    class="h-4 w-4 absolute -top-1 -right-1 hover:cursor-pointer">
                            </div>
                        </div>
                        <div class="drag-here-img h-44 w-44 border border-[#969696] p-2 relative overflow-hidden">
                            <div class="text-[.7rem] text-[#bdbdbd] absolute top-[45%] left-0 right-0">
                                <div class=" text-center mx-auto">{{"Drag image here" | translate}}</div>
                                <div class="relative text-center or"><span class="px-2 relative bg-white z-[3]">{{"or" |
                                        translate}}</span>
                                </div>
                            </div>
                            <input #projectProfileBtn accept="image/*"
                                class="block h-full absolute inset-0 opacity-0 z-[1]" id="file" type="file"
                                (change)="uploadProjectProfile($event)">
                            <div class="absolute bottom-4 z-[2]  text-center font-normal cursor-pointer text-[.65rem] right-0 left-0 mx-3 py-1 border-2 bg-gray-50 border-[#969696] text-primary rounded-md"
                                (click)="addProjectProfile()">{{"Select from device" | translate}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-12">
                <button (click)="routeTo()" type="button"
                    class="border border-accent text-accent font-bold w-full md:w-1/2 rtl:mr-auto rtl:ml-[unset] ml-auto  py-3 text-center cursor-pointer hover:bg-accent hover:text-white transition duration-150">
                    {{"CANCEL" | translate}}</button>
                <button *ngIf="is_edit == false" type="submit"
                    class="border border-accent text-lighterBlack font-bold w-full md:w-1/2  rtl:ml-auto rtl:mr-[unset] mr-auto  py-3 bg-accent hover:text-white transition duration-150 ">{{"NEXT"
                    | translate}}</button>
                <button *ngIf="is_edit == true" type="submit"
                    class="border border-accent text-lighterBlack font-bold w-full md:w-1/2 rtl:ml-auto rtl:mr-[unset] mr-auto  py-3 bg-accent hover:text-white transition duration-150 ">{{"SAVE AND CONTINUE" | translate}}</button>
            </div>
        </form>
    </div>
</div>