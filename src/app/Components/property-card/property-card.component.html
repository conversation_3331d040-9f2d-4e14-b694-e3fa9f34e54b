<div class="h-64 lg:h-80 w-full cursor-pointer bg-gradient-to-t from-[#3d62b1]  to-[#eceaea]" [routerLink]="['/property-details',property?.id]" >
    <img class="w-full h-full  object-cover "  [src]="property?.thumbnail" alt="">
    <div class="from-neutral-300 h-0">
    <div class="p-2 block relative h-0 bottom-32 md:bottom-32 lg:bottom-40 xl:bottom-40 ">
        <img class="logo w-12 h-12 md:h-12 md:w-12 lg:h-14 lg:w-14 rounded-full m-0 mb-2 " [src]="property?.logo" alt="">
        <h3 class="text-xs md:text-sm lg:text-sm font-semibold mb-1 text-data text-white uppercase"> {{property?.title}}</h3>
            <div class="flex items-center gap-2 mb-2">
                <img src="../../../assets/icons/pin.png" class="pin w-2 lg:w-3">
                <p class="text-slate-300 text-xs lg:text-sm text-data ">{{property?.property_area}},{{property.state}},{{property.country}}</p>
            </div>
            <h5 class="text-accent text-xs lg:text-sm text-right font-semibold" *ngIf="property.property_for=='R'  " >{{property?.price}}{{'KD'|translate}} / {{property.rental_frequency}}</h5>
           
            <h5 class="text-accent text-xs lg:text-sm text-right font-semibold" *ngIf="property.property_for=='S' ">{{property?.price}}{{'KD'|translate}}</h5>


            <!-- <h5 class="text-accent text-xs lg:text-sm text-right font-semibold" *ngIf="property_for=='R' && property_for=='S' else first" ></h5>

            <ng-template #first> <h5 class="text-accent text-xs lg:text-sm text-right font-semibold" >{{property?.price}}KD / {{"Month"| translate}}</h5></ng-template> -->

            
           
    </div>
    </div>
    
</div>
