import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-property-card',
  templateUrl: './property-card.component.html',
  styleUrls: ['./property-card.component.scss']
})
export class PropertyCardComponent implements OnInit {

  thumbnail: string = ''
  logo: string = ''
  constructor(
    public router: Router,
    private route: ActivatedRoute,
  ) { }

  @Input() property: any
  @Input() property_for: any
 
 

  ngOnInit(): void {
  }

  

  ngAfterViewInit(): void{


  //  this.property?.images.some( (e:any) => {
  //   if(e['is_thumbnail'] =='1')  {

  //     // if thumbnail is 1 add that image 
  //     this.thumbnail = e.img
  //   } else {
  //     // else add       
  //     this.thumbnail = this.property?.images[0]?.img
  //   }
  // } )

    
   

   
    

    // this.property?.images.forEach((prop: any) => {
      
    //   if (prop?.is_thumbnail == 1) {
    //     this.thumbnail = prop?.img  
        
    //   }else if (prop?.is_thumbnail == 0 )  {
    //     this.thumbnail = this.property?.images[0]
    //     console.log( this.property?.images[0]?.img  ,' this.property?.images[0]?.img  ');
        
    //   } 
    // });



  }

  

}
