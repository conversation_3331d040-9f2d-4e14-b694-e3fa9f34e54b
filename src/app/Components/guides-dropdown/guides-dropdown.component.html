<div class="guides-dropdown drop-down cursor-pointer relative" (click)="dropDown()" clickedOutside (clickedOutside)="clickedOutside()">
    <div id="main" class=" outline-none   placeholder:text-lg  relative flex items-center gap-1"><span>{{"GUIDES" |
            translate}}</span>
        <img [ngClass]="(open === true)?'-rotate-180':''" class="transition duration-200 h-2 w-2 "
            src="../../../assets/icons/triangle-dropdown.svg">
        <!-- <span class="absolute rtl:left-4 ltr:right-4 top-1/2 h-[0.9rem] w-[0.9rem] -translate-y-1/2"><img class="h-full w-full" src="../../../assets/icons/arrow-semi-down.svg"></span> -->
    </div>
    <div *ngIf="open" 
        class="absolute right-0 mt-1 bg-white min-w-[10rem] w-full  shadow-md rounded-md overflow-hidden text-black py-2">
        <ul>
            <li class="hover:underline px-4 py-2">{{"Area Guides" | translate}}</li>
            <li class="hover:underline px-4 py-2">{{"Building Guides" | translate}}</li>
        </ul>
    </div>
</div>