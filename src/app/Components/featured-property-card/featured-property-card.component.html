<div [routerLink]="['/property-details', featuredProperty.id]"
    class="pb-4 bg- rounded-lg shadow-xl  overflow-hidden hover:cursor-pointer ease-in-out duration-200">
    <div class="mb-5">
        <!-- ="['/property-details',featuredProperty?.id]"-->
        <div class="relative "><img class="object-cover feature-image aspect-[4.90/4] h-full w-full"
                src="{{featuredProperty?.thumbnail}}">
            <p *ngIf="featuredProperty?.property_for === 'S'"
                class="absolute top-4 rtl:left-4 ltr:right-4 bg-neutral px-8 py-2 rounded-full">{{"For Sale" | translate}}</p>
            <p *ngIf="featuredProperty?.property_for === 'R'"
                class="absolute top-4 rtl:left-4 ltr:right-4 bg-neutral px-8 py-2 rounded-full">{{"For Rent" | translate}}</p>
            <div class="h-8 w-8 absolute bottom-4 rtl:left-4 ltr:right-4 hover:cursor-pointer hover:scale-105">
                <img *ngIf="is_fav === 0" src="../../../assets/icons/favorite.svg" (click)="toggleFav(1,$event)">
                <img *ngIf="is_fav === 1" src="../../../assets/icons/favorite-red.svg" (click)="toggleFav(0,$event)">
            </div>
        </div>
    </div>
    <div class="space-y-5 text-gray-200">
        <!-- <h2 class="text-3xl">{{title}}</h2> -->
        <h2 class="text-2xl font-bold h-8">{{featuredProperty?.title}}</h2>
        <div class="flex items-center gap-2 text-[#abbed4]">
            <img src="../../../assets/icons/map-filtered.svg" class="location-icon h-6 w-6 ">
            <!-- <p class="text-sm">{{"Ahmad Al Jaber Street, Sharq, Kuwait"}}</p> -->
            <p class="overflow-hidden whitespace-nowrap ellipsis">{{featuredProperty?.address}}</p>
        </div>
        <div
            class="text-sm flex items-center gap-4 border border-x-0 border-t-0 pb-5 mb-5 text border-b-borderColor text-[#abbed4]">
            <div class="flex items-center gap-2"><img src="../../../assets/icons/size-filtered.svg" class="h-6 w-6 ">
                <p class="break-keep">{{featuredProperty?.area_size | number:'1.0'}}{{"sq.ft" | translate}}</p>
            </div>
            <div *ngIf="featuredProperty?.no_of_beds>0" class="flex items-center gap-2"><img
                    src="../../../assets/icons/rooms.svg" class="h-6 w-6 "><span>{{featuredProperty?.no_of_beds}}</span>
            </div>
            <div *ngIf="featuredProperty?.no_of_bathrooms >0 " class="flex items-center gap-2"><img src="../../../assets/icons/bath-filtered.svg"
                    class="h-6 w-6 "><span>{{featuredProperty?.no_of_bathrooms}}</span>
            </div>
        </div>
        <!-- <div class="text-3xl">{{price}}KD / Month</div> -->
        <div class="text-2xl font-bold">{{featuredProperty?.price | number:'1.0'}}{{'KD'|translate}} <span *ngIf="featuredProperty?.property_for === 'R'">/ {{featuredProperty?.rental_frequency | translate}}</span></div>
    </div>
</div>