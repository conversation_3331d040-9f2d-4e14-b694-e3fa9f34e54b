import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { FavoritesService } from 'src/app/Services/Favorites/favorites.service';

@Component({
  selector: 'app-featured-property-card',
  templateUrl: './featured-property-card.component.html',
  styleUrls: ['./featured-property-card.component.scss']
})
export class FeaturedPropertyCardComponent implements OnInit {
  @Input() featuredProperty?: any
  isLoggedIn: any
  userDetails: any;
  is_fav: any
  constructor(

    private favoriteService: FavoritesService,
    private toastr: ToastrService,
    private configSettings: configSettings,
    private router: Router
  ) { }

  ngOnInit(): void {

    this.configSettings.getIsUserLoggedIn().subscribe(res => {
      this.isLoggedIn = res
    })

    this.userDetails = this.configSettings.getUserDetails()
    this.is_fav = this.featuredProperty.is_favourite
  }


  toggleFav(num: number, event: any) {

    if (this.isLoggedIn == false) {
      this.router.navigate(['/login'])
    }


    event.stopPropagation();
    const getParams = {}
    const postParams = {
      "user_id": this.userDetails.user_id,
      "type": "P",
      "type_id": this.featuredProperty.id,
      "like": num
    }


    this.favoriteService.toggleFavorite(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.is_fav === 0 ? this.is_fav = 1 : this.is_fav = 0
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }
}
