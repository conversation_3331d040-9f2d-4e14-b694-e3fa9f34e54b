import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FeatureMyPostComponent } from './feature-my-post.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';



@NgModule({
  declarations: [FeatureMyPostComponent],
  imports: [
    CommonModule,
    // FeatureMyPostRoutingModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule
  ], exports:[FeatureMyPostComponent]
})
export class FeatureMyPostModule { }
