import { Component, EventEmitter, Input, OnInit, Output, Renderer2 } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { FeaturedPlanListService } from 'src/app/Services/Featured-plan-list/featured-plan-list.service';
import { ProjectsService } from 'src/app/Services/Projects/projects.service';
import { PostPropertyService } from 'src/app/Services/Properties/post-property.service';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-feature-my-post',
  templateUrl: './feature-my-post.component.html',
  styleUrls: ['./feature-my-post.component.scss']
})
export class FeatureMyPostComponent implements OnInit {

  @Input() postPropertyParams: any
  @Input() postProjectParams: any

  @Output() showForm = new EventEmitter<any>();

  constructor(
    private renderer: Renderer2,
    private configSettings: configSettings,
    private featuredPlanListService: FeaturedPlanListService,
    private toastr: ToastrService,
    private postPropertyService: PostPropertyService,
    private router: Router,
    private projectsService: ProjectsService,
    private translateService: TranslateService,

  ) { }

  ngOnInit(): void {
    window.scrollTo({
      top: 100,
      left: 100,
      behavior: 'smooth'
    });
    // this.getPostPropertyDetails()




  }

  openFeatureModal() {
    // this.featuredPostPayment = true
    if (this.featuredPostPayment = true) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    this.getFeaturedPlansDetails()
  }
  closeFeatureModal() {
    
    this.featuredPostPayment = false;
    document.body.style.overflow = "auto";
  }


  // getPostPropertyDetails() {

  //   let data = localStorage.getItem('propertyDetails')
  //   if (data) {
  //     this.propertyDetails = JSON.parse(data);
  //   }
  // }


  selectedPostType: any = 'N'
  selectedPostTypeID: any
  propertyDetails: any

  featuredPostPayment: any = false
  plansList: any
  allPaymentMethods: any

  planChoosen: any
  choosenPaymode: any = ''

  // getPriceRange(e: any) {
  //   // console.log(this.planChoosen)
  // }

  choosePostType(e: any, type: string) {
    this.selectedPostType = type
    this.selectedPostTypeID = e.target.id

  }

  proceed() {
    this.configSettings.setShowLoader(true)

    let planid = ''
    let paymentMethod = ''
    let url = ''

    if (this.postPropertyParams) {
      this.postProperty(planid, paymentMethod, url)
      this.translateService.get('Your property has been submitted sucessfully').subscribe(res => {
        this.toastr.success('', res);
      })
    } else if (this.postProjectParams) {
      this.postProject(planid, paymentMethod, url)
      this.translateService.get('Your project has been submitted sucessfully').subscribe(res => {
        this.toastr.success('', res);
      })
      
    }
   
    


  }

  proceedAndPay() {
    
    document.body.style.overflow = "auto";
    if (this.planChoosen === undefined) {
      // this.toastr.error('', 'Select Featured Plan')
      this.translateService.get('Select Featured Plan').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if (this.choosenPaymode == '') {
      // this.toastr.error('', 'Select Payment method')
      this.translateService.get('Select Payment method').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    const confirmURL = window.location.origin + this.router.createUrlTree(['/confirmation-page']);
    
    this.configSettings.setShowLoader(true)
   

    if (this.postPropertyParams) {
      this.postProperty(this.planChoosen.id, this.choosenPaymode, confirmURL)
      
    } else if (this.postProjectParams) {
      this.postProject(this.planChoosen.id, this.choosenPaymode, confirmURL)
    }
  }
  // this.toastr.success('Your project has been submitted sucessfully');
  

  getPlan(plan: any) {
    // console.log(plan)
  }

  type: any

  getFeaturedPlansDetails() {
    if (this.postPropertyParams) {
      this.type = 'PT'
    } else if (this.postProjectParams) {
      this.type = 'PR'
    }

    const getParams = {
      type: this.type
    }

    this.featuredPlanListService.featuredPlans(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.plansList = response.body.data.plan
            this.allPaymentMethods = response.body.data.payment_methods
            

            this.configSettings.setShowLoader(false)
       

          }
          
        }
        // this.toastr.success('Your project has been submitted sucessfully');
      },
      error: (err) => {
        // this.toastr.error('', 'Something went wrong');
        this.translateService.get('Something went wrong').subscribe(res =>{
          this.toastr.error('', res);
        })
      }
    })
  }

  choosePaymentType(e: any) {
    Array.from(document.querySelectorAll('.payment-method')).forEach(function (el) {
      el.classList.remove('activeBorder');
    });
    this.renderer.addClass(e.currentTarget, 'activeBorder')
    this.choosenPaymode = e.currentTarget.id
  }


  postProperty(planID: any, paymentMethod: any, url: any) {
    const getParams = {}
    const postParams = {
      post_type: this.selectedPostType,
      featured_plan_id: planID,
      paymode: paymentMethod,
      redirect_url: url,

      ...this.postPropertyParams
    }


    this.postPropertyService.postProperties(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            if (response.body.data.payment_url) {
              window.location.href = response.body.data.payment_url;

            }
            else {
              this.router.navigate(['/my-account/my-property-listing'])
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  back() {
    this.showForm.emit(true)
  }

  postProject(planID: any, paymentMethod: any, url: any) {

    const postParams = {
      post_type: this.selectedPostType,
      featured_plan_id: planID,
      paymode: paymentMethod,
      redirect_url: url,

      ...this.postProjectParams
    }

    this.projectsService.postProject({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            if (response.body.data.payment_url) {
              this.configSettings.setShowLoader(true)
              window.location.href = response.body.data.payment_url;
            }
            else {
              this.router.navigate(['/my-account/my-projects'])
              this.configSettings.setShowLoader(false)
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

}
