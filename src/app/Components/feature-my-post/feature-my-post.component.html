<div class='container mx-auto px-4 sm:px-14 md:px-24 lg:px-40  xl:px-60 my-4 text-white py-20'>
    <div class="bg-white rounded-2xl px-6 sm:px-12 py-3 xl:px-4 2xl:px-12 min-h-[35rem]">
        <h5 class="text-base font-semibold my-8 text-black">{{"Select option to proceed" | translate}}</h5>


        <div class="flex gap-4 sm:gap-7 mb-16 sm:mb-24">
            <div id="N" [class.active]="selectedPostType=='N'" [class.card]="selectedPostType=='F'"
                (click)="choosePostType($event, 'N')"
                class="post-type active h-56 w-52  rounded-lg p-6 flex justify-between items-center flex-col hover:cursor-pointer">
                <div><img src="../../../assets/icons/normal-post-icon.svg" alt=""></div>
                <div class="flex flex-col items-center gap-1">
                    <p class="text-xs font-bold">{{"NORMAL POST" | translate}}</p>
                    <p class="text-[.7rem] text-white">{{"Post for Free" | translate}}</p>
                </div>
                <button *ngIf="selectedPostType == 'N'"
                    class="text-sm text-accent border border-accent w-24 py-1 rounded-md">{{"Selected" |
                    translate}}</button>
                <button *ngIf="selectedPostType != 'N'"
                    class="text-sm text-accent border border-accent w-24 py-1 rounded-md">{{"Select" |
                    translate}}</button>
            </div>
            <div id="F" [class.active]="selectedPostType=='F'" [class.card]="selectedPostType=='N'"
                (click)="choosePostType($event, 'F')"
                class="post-type card h-56 w-52 rounded-lg p-4  sm:p-6 flex justify-between items-center flex-col hover:cursor-pointer ">
                <div><img src="../../../assets/icons/featured-post-icon.svg" alt=""></div>
                <div class="flex flex-col items-center gap-1">
                    <p class="text-xs font-bold uppercase">{{"featured post" | translate}}</p>
                    <p class="text-[.7rem] text-white">{{"Paid Post" | translate}}</p>
                </div>
                <button *ngIf="selectedPostType == 'F'"
                    class="text-sm text-accent border border-accent w-24 py-1 rounded-md">{{"Selected" |
                    translate}}</button>
                <button *ngIf="selectedPostType != 'F'"
                    class="text-sm text-accent border border-accent w-24 py-1 rounded-md">{{"Select" |
                    translate}}</button>
            </div>
            <!-- <div (click)="choosePostType($event)" class="post-type active h-56 w-52  rounded-lg p-6 flex justify-between items-center flex-col hover:cursor-pointer">
                <div><img src="../../../assets/icons/normal-post-icon.svg" alt=""></div>
                <div class="flex flex-col items-center gap-1">
                    <p class="text-xs font-bold">NORMAL POST</p>
                    <p class="text-[.7rem] text-white">Post for Free</p>
                </div>
                <button class="text-sm text-accent border border-accent w-24 py-1 rounded-md">Selected</button>
            </div>
            <div (click)="choosePostType($event)" class="post-type card h-56 w-52 rounded-lg  p-6 flex justify-between items-center flex-col hover:cursor-pointer ">
                <div><img src="../../../assets/icons/featured-post-icon.svg" alt=""></div>
                <div class="flex flex-col items-center gap-1">
                    <p class="text-xs font-bold">FEATURED POST</p>
                    <p class="text-[.7rem] text-white">Paid Post</p>
                </div>
                <button class="text-sm text-accent border border-accent w-24 py-1 rounded-md">Select</button>
            </div> -->
        </div>


        <div class="flex flex-col-reverse sm:flex-row gap-7">

            <button (click)="back()" class="uppercase w-full sm:w-52 p-3 bg-accent">
                <span *ngIf="postPropertyParams">{{"Edit your Property" | translate}}</span>
                <span *ngIf="postProjectParams">{{"Edit your Project" | translate}}</span>
            </button>

            <button *ngIf="selectedPostType == 'N'" (click)="proceed()" class="uppercase w-full sm:w-52 p-3 bg-primary">
                {{"proceed" | translate}}
            </button>

            <button *ngIf="selectedPostType == 'F'" (click)="openFeatureModal()" class="uppercase w-full sm:w-60 p-3 bg-primary">
                {{"Choose a Featured Plan" | translate}}
            </button>
        </div>




        <div *ngIf="featuredPostPayment" (click)="closeFeatureModal()" class="bg-black opacity-40 fixed inset-0 z-[99]">
        </div>
        <div *ngIf="featuredPostPayment"
            class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2  bg-white text-black w-[96%] md:w-[28rem] rounded-xl">
            <div (click)="closeFeatureModal()" class="px-3 py-3 cursor-pointer text-xs flex items-center gap-2">
                <img _ngcontent-whc-c94="" class="rtl:-scale-x-100" src="../../../assets/icons/gray-left-arrow.png" alt="">
                <span>{{"Back" | translate}}</span>
            </div>
            <h2 class="capitalize text-xl text-center text-primary font-semibold mb-3">{{"featured post" | translate}}</h2>
            <p class="text-justify text-[.7rem] mb-7 px-11">
                {{"If you choose to post your property with the featured, the property will display on the top navigation in the visitors/user's app. Featured property can easy to navigate and more chance to connect with the buyers." | translate}}
            </p>
            <div class="px-20  mb-3">
                <label for="" class="uppercase text-[.7rem] ">{{"SELECT DURATION" | translate}}</label>
                <span class=" downarrow z-[2]">
                    <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                        class="bottom-1 rtl:left-2.5 rtl:right-[unset]"></span>
                <ng-select (change)="getPlan($event)" [(ngModel)]="planChoosen"
                    class="featured-post-dropdown placeholder:text-sm" placeholder="{{'Select'|translate}}">
                    <ng-option *ngFor="let plan of plansList" [value]="plan">
                        {{plan.days}} {{"Days" | translate}}</ng-option>
                </ng-select>
            </div>
            <div *ngIf="planChoosen"  class="px-24 mb-5 flex justify-between items-center text-primary font-bold tracking-tight">
                <p  class="text-[.7rem] ">{{"Price Structure" | translate}}:</p>
                <!-- <p *ngIf="!planChoosen">- - -</p> -->
                <p *ngIf="planChoosen">{{planChoosen.price | number:'1.0'}} {{'KD'|translate}}<span class="text-sm font-thin">
                        {{planChoosen.days}} {{"days" | translate}}</span></p>
            </div>

            <p class="mb-3 text-primary text-base text-center font-bold">{{"Select a payment method" | translate}}:</p>
            <div class="flex justify-center items-center gap-8 mb-5">
                <div [id]="paymentType.id" class="payment-method  border-2 relative"
                    *ngFor="let paymentType of allPaymentMethods" (click)="choosePaymentType($event)">
                    <img [ngClass]="choosenPaymode == paymentType.id ? 'activeCheck' : ''"
                        src="../../../assets/icons/yellow-tick.svg" class="payment-method hidden">
                    <img [src]="paymentType.img" class="w-16 h-10 lg:w-12 lg:h-8 cursor-pointer">
                </div>
            </div>
            <button (click)="proceedAndPay()" class="w-72 py-2 mx-auto mb-7  block bg-primary text-white uppercase">
                {{"proceed & pay" | translate}}
            </button>
        </div>
    </div>
</div>