.custom-shadow {
    box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.2);
    -webkit-box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0px 0px 11px 0px rgba(0, 0, 0, 0.2);
}

.resize-icon {
    resize: none;
}

.custom-checkmark {
    background-image: url(../../../assets/icons/blue-checkbox.svg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.custom-checkmark:hover {
    transform: scale(98%);
}

.custom-border {
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='4' stroke-dasharray='6%2c 14' stroke-dashoffset='43' stroke-linecap='square'/%3e%3c/svg%3e");
}

.testbg {
    background-color: rgba(255, 0, 0, 0);
    background-size: cover;
    z-index: 12;
}

.map {
    position: relative;
}

.map-center-overlay {
    margin: 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.drag-here-img {
    background-image: url(../../../assets/icons/empty-img.png);
    background-repeat: no-repeat;
    background-position: 50% 20%;
    background-size: 52px 37px;
}

.or::before {
    content: '';
    position: absolute;
    display: block;
    top: 50%;
    height: 3px;
    width: 30%;
    background: #a8aeb99d;
    left: 50%;
    z-index: 1;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
}

@media only screen and (max-width: 1020px){
    .mobileslider{
        overflow-x: scroll;
        width: 100%;
        border-radius: 3px;

        .mobilemenu{
            min-width: 350px;
            
        }

    }
}


@media only screen and (max-width: 767px){
    .mobileslider{
        overflow-x: scroll;
        width: 100%;
        border-radius: 3px;

        .mobilemenu{
            min-width: 305px;
            
        }

    }
}


@media only screen and (max-width: 424px){
    .mobileslider{
        overflow-x: scroll;
        width: 100%;
        border-radius: 3px;

        .mobilemenu{
            min-width: 370px;
            
        }

    }
}
.tab{
    border: 0px;
}

.tab.active{
    border: #E5B74B;
    border-bottom: 3px solid #E5B74B ;
}

.image-bg{
    filter: invert(0) sepia(0) saturate(0.1) hue-rotate(0deg) brightness(5);
}


@media screen and (max-width: 400px) {
    .amenities  {
        width: 72px;
    }
}

@media screen and (max-width: 376px) {
    .amenities  {
        width: 66px;
    }
}

@media screen and (max-width: 640px) {
    
    .bedroom div  {
        width: 20%;
    }
}

@media screen and (max-width: 500px) {
    
    .bedroom div  {
        width: 18%;
    }
}

@media screen and (max-width: 436px) {
    .bedroom{
        column-gap: 16px;
    }
    .bedroom div  {
        width: 27%;
    }
}
