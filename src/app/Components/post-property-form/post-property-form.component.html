<div class="container mx-auto px-4 md:px-10 lg:px-24">
    <div class="container mx-auto mt-7 lg:mt-16 mb-7 px-6  md:px-10 xl:px-24 text-center text-white text-2xl font-bold">
        <h2>{{"POST PROPERTY" | translate}}</h2>
    </div>
    <div class="rounded-lg overflow-hidden">
        <div class="container  bg-white custom-shadow relative z-10  cursor-default">
            <!-- big device -->
            <div class="">
                <div class=" lg:flex w-full text-lighterBlack ">
                    <div class="w-fll lg:w-1/3" [ngClass]="currentPage === 1? 'block' : 'hidden lg:block'">
                        <div [ngClass]="currentPage === 1 || currentPage ===  2 || currentPage ===  3 ? 'border-accent border-b-2' : 'border-b-0'"
                            class="flex w-full  justify-center items-center py-4 gap-2  md:gap-4 text-base xl:text-lg font-bold  ">

                            <span *ngIf="currentPage === 1"
                                class="w-8 h-8 bg-accent rounded-full flex justify-center items-center text-white">1</span>
                            <img *ngIf="currentPage !== 1" src="../../../assets/icons/property-page-done.svg"
                                class="w-8 h-8">
                            <p [ngClass]="currentPage === 1?'text-accent':'text-black  '">{{"Personal Information" |
                                translate}}
                            </p>
                        </div>
                    </div>
                    <div class="w-fll lg:w-1/3" [ngClass]="currentPage === 2? 'block' : 'hidden lg:block'">
                        <div [ngClass]="currentPage ===  2 || currentPage ===  3 ? 'border-accent border-b-2' : 'border-b-0'"
                            class="flex w-full  justify-center items-center  py-4  gap-2  md:gap-4 text-base xl:text-lg font-bold">

                            <span *ngIf="currentPage !== 3" [ngClass]="currentPage === 2 ? 'bg-accent' : 'bg-steelBlue'"
                                class="w-8 h-8 rounded-full flex justify-center items-center text-white">2</span>
                            <img *ngIf="currentPage === 3" src="../../../assets/icons/property-page-done.svg"
                                class="w-8 h-8">

                            <p [ngClass]="currentPage === 2?'text-accent':'text-black  '">{{"Property Information" |
                                translate}}
                            </p>
                        </div>
                    </div>
                    <div class="w-fll lg:w-1/3" [ngClass]="currentPage === 3? 'block' : 'hidden lg:block'">
                        <div [ngClass]="currentPage ===  3 ? 'border-accent border-b-2' : 'border-b-0'"
                            class="flex  justify-center items-center  py-4  gap-2  md:gap-4 text-base xl:text-lg font-bold">

                            <span [ngClass]="currentPage === 3 ? 'bg-accent' : 'bg-steelBlue'"
                                class="w-8 h-8 rounded-full flex justify-center items-center text-white">3</span>
                            <p [ngClass]="currentPage === 3?'text-accent':'text-black'">{{"Other Information" |
                                translate}}</p>
                        </div>
                    </div>
                </div>
            </div>



            <!-- <div class="mobileslider lg:grid grid-cols-3 rtl:md:gap-6 gap-2 lg:gap-0 text-lighterBlack self-start">
                <div [ngClass]="currentPage === 1 || currentPage ===  2 || currentPage ===  3 ? 'border-accent border-b-2' : 'border-b-0'"
                    class="flex justify-center items-center py-4 gap-2 md:gap-4 text-base xl:text-lg font-bold  ">
                    <span *ngIf="currentPage === 1"
                        class="w-8 h-8 bg-accent rounded-full flex justify-center items-center text-white">1</span>
                    <img *ngIf="currentPage !== 1" src="../../../assets/icons/property-page-done.svg" class="w-8 h-8">
                    <p [ngClass]="currentPage === 1?'text-accent':'text-black' ">{{"Personal Information" | translate}}</p>
                </div>
                <div [ngClass]="currentPage ===  2 || currentPage ===  3 ? 'border-accent border-b-2' : 'border-b-0'"
                    class="flex justify-center items-center  py-4  gap-2  md:gap-4 text-base xl:text-lg font-bold">
                    <span *ngIf="currentPage !== 3" [ngClass]="currentPage === 2 ? 'bg-accent' : 'bg-steelBlue'"
                        class="w-8 h-8 rounded-full flex justify-center items-center text-white">2</span>
                    <img *ngIf="currentPage === 3" src="../../../assets/icons/property-page-done.svg" class="w-8 h-8">

                    <p [ngClass]="currentPage === 2?'text-accent':'text-black' ">{{"Property Information" | translate}}</p>
                </div>
                <div [ngClass]="currentPage ===  3 ? 'border-accent border-b-2' : 'border-b-0'"
                    class="flex justify-center items-center  py-4  gap-2  md:gap-4 text-base xl:text-lg font-bold">
                    <span [ngClass]="currentPage === 3 ? 'bg-accent' : 'bg-steelBlue'"
                        class="w-8 h-8 rounded-full flex justify-center items-center text-white">3</span>
                    <p [ngClass]="currentPage === 3?'text-accent':'text-black' ">{{"Other Information" | translate}}</p>
                </div>
            </div> -->
        </div>

        <!-- #PAGE 1 CONTAINER -->
        <div [ngClass]="currentPage === 1 ? 'block' : 'hidden'"
            class="container px-6  md:px-10 xl:px-24 bg-white pt-14 pb-24">
            <form *ngIf="personalInformation" [formGroup]="personalInformation" (ngSubmit)="getPersonalInfo()">
                <!-- <form action=""> -->
                <div class="grid grid-cols-2 gap-x-12 lg:gap-x-20 xl:gap-x-32  mb-5 lg:mb-12">
                    <!-- left coloumn -->
                    <div class="col-span-2 md:col-span-1">
                        <!-- <div class="mb-6 relative">
                            <p class="mb-2 text-sm text-lighterBlack">{{"List Property As" | translate}}</p>
                            <ng-select formControlName="listPropertyAsID" class="post-property-dropdown"
                                placeholder="Select">
                                <ng-option [value]="'A'">{{"Agent" | translate}}</ng-option>
                                <ng-option [value]="'O'">{{"Owner" | translate}}</ng-option>
                            </ng-select>
                        </div> -->
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Contact Name" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <input type="text" class="w-full bg-lightGray outline-none px-2 py-3"
                                formControlName="ownerOrCompanyName">

                        </div>
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Contact Email Address" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <input type="email" class="w-full bg-lightGray outline-none px-2 py-3"
                                formControlName="emailAddress">
                            <div
                                *ngIf="personalInformation.get('emailAddress')?.invalid && personalInformation.get('emailAddress')?.touched">
                                <small
                                    *ngIf="(personalInformation.get('emailAddress')?.errors && 
                                    personalInformation.get('emailAddress')?.hasError('pattern')) || (personalInformation.get('emailAddress')?.invalid && personalInformation.get('emailAddress')?.touched)"
                                    class="text-red-700 "></small>
                            </div>
                        </div>
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Youtube link" | translate}}</p>
                            <input type="text" class="w-full bg-lightGray outline-none px-2 py-3"
                                formControlName="socialMedia">

                        </div>
                    </div>
                    <!-- right coloumn -->
                    <div class="col-span-2 md:col-span-1 mb-6">
                        <div class="flex gap-4 w-full">
                            <!-- <div class=" w-1/3  md:w-2/5 lg:w-1/3 2xl:w-1/4  mb-6 relative">
                                <p class="mb-2 text-sm text-lighterBlack">{{"Country Code" | translate}}</p>
                                <span class="absolute  bottom-4 z-10 rtl:left-3 rtl:right-[unset] right-3 opacity-70">
                                    <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                                </span>
                                <select formControlName="whatsappCode" name="" id=""
                                    class="w-full bg-lightGray outline-none px-2 py-3 appearance-none">

                                    <option [ngValue]="country.is_active" *ngFor="let country of countries"
                                        [ngClass]="{'hidden': country.is_active == 'No'}"> +{{country.phonecode}}
                                    </option>
                                </select>
                            </div> -->
                            <div class=" w-full mb-6">
                                <p class="mb-2 text-sm text-lighterBlack">{{"Whatsapp Number" | translate}}</p>

                                <input type="tel" class="w-full bg-lightGray outline-none px-2 py-3"
                                    pattern="[0-9]{8,12}" maxlength="12" minlength="8" formControlName="whatsappNumber">
                                <div
                                    *ngIf="personalInformation.get('whatsappNumber')?.invalid && personalInformation.get('whatsappNumber')?.touched">
                                    <small
                                        *ngIf="(personalInformation.get('whatsappNumber')?.errors && 
                                        personalInformation.get('whatsappNumber')?.hasError('pattern')) || (personalInformation.get('whatsappNumber')?.invalid && personalInformation.get('mobileNumber')?.touched)"
                                        class="text-red-700 "></small>
                                </div>

                            </div>
                        </div>
                        <div class="flex gap-4 w-full">
                            <!-- <div class=" w-1/3  md:w-2/5 lg:w-1/3 2xl:w-1/4  mb-6 relative">
                                <p class="mb-2 text-sm text-lighterBlack">{{"Country Code" | translate}}<span
                                        class="text-red-600">*</span></p>
                                <span class="absolute  bottom-4 z-10 rtl:left-3 rtl:right-[unset] right-3 opacity-70">
                                    <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                                </span>
                                <select formControlName="mobilePhoneCode" name="" id=""
                                    class="w-full bg-lightGray outline-none px-2 py-3 appearance-none">
                                    <option [ngValue]="country.is_active" *ngFor="let country of countries"
                                        [ngClass]="{'hidden': country.is_active == 'No'}"> +{{country.phonecode}}
                                    </option>
                                </select>
                            </div> -->

                            <div class=" w-full lg:mb-6">
                                <p class="mb-2 text-sm text-lighterBlack">{{"Mobile Number" | translate}}<span
                                        class="text-red-600">*</span></p>
                                <input type="tel" class="w-full bg-lightGray outline-none px-2 py-3"
                                    pattern="[0-9]{8,12}" maxlength="12" minlength="8" formControlName="mobileNumber">
                                <div
                                    *ngIf="personalInformation.get('mobileNumber')?.invalid && personalInformation.get('mobileNumber')?.touched">
                                    <small
                                        *ngIf="(personalInformation.get('mobileNumber')?.errors && 
                                        personalInformation.get('mobileNumber')?.hasError('pattern')) || (personalInformation.get('mobileNumber')?.invalid && personalInformation.get('mobileNumber')?.touched)"
                                        class="text-red-700 "></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-y-4 lg:gap-x-20 xl:gap-x-32">
                    <a *ngIf="is_edit === false" [routerLink]="['/home']"
                        class="border border-accent text-accent font-bold w-full rtl:mr-auto rtl:ml-[unset] ml-auto px-10 py-3 text-center cursor-pointer hover:bg-accent hover:text-white transition duration-150 ">
                        {{"CANCEL" | translate}}</a>
                    <a *ngIf="is_edit === true" [routerLink]="['/my-account/my-property-listing']"
                        class="border border-accent text-accent font-bold w-full rtl:mr-auto rtl:ml-[unset] ml-auto px-10 py-3 text-center cursor-pointer hover:bg-accent hover:text-white transition duration-150 ">
                        {{"CANCEL" | translate}}</a>
                    <button
                        class="border border-accent text-lighterBlack font-bold w-full rtl:ml-auto rtl:mr-[unset] mr-auto px-10 py-3 bg-accent hover:text-white transition duration-150 row-start-1 row-end-2 lg:row-start-auto lg:row-end-auto">{{"NEXT"
                        | translate}}</button>
                </div>
            </form>

        </div>

        <!-- #PAGE 2 CONTAINER [ngClass]="currentPage === 2 ? 'block' : 'hidden'" -->
        <div [ngClass]="currentPage === 2 ? 'block' : 'hidden'"
            class="container px-6  md:px-10 xl:px-24 bg-white pt-14 pb-24">
            <form *ngIf="propertyInformation" [formGroup]="propertyInformation" (ngSubmit)="getPropertyInformation()"
                enctype="multipart/form-data">
                <div class="grid grid-cols-2 gap-x-12 lg:gap-x-20 xl:gap-x-32 mb-7">
                    <!-- left coloumn -->
                    <div class="col-span-2 md:col-span-1">
                        <div class="mb-6 relative">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Property For" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <span class="absolute  bottom-4 z-10 rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                                <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                            </span>
                            <!-- <div (click)="propertyForDropdown = !propertyForDropdown"
                                class="w-full h-11 bg-lightGray relative flex items-center cursor-pointer">
                                <span class="px-3 text-sm cursor-default">{{propertyForPlaceholder}}</span>
                                <img src="../../../assets/icons/arrow-semi-down.svg"
                                    class="h-2 w-3 absolute top-1/2 -translate-y-1/2 right-3 opacity-70">
                            </div>
                            <div *ngIf="propertyForDropdown === true" class="absolute z-10 mt-1 bg-lightGray w-full">
                                <ul>
                                    <li id="R" class="px-3 py-2 hover:bg-gray-200 cursor-pointer" (click)="propertyForRS($event)">
                                        {{"Rent"}}
                                    </li>
                                    <li id="S" class="px-3 py-2 hover:bg-gray-200 cursor-pointer" (click)="propertyForRS($event)">
                                        {{"Re-Sale"}}
                                    </li>
                                </ul>
                            </div> -->

                            <ng-select [(ngModel)]="propertyRS" formControlName="propertyForID"
                                class="post-property-dropdown placeholder:text-sm"
                                placeholder="{{'Select' | translate}}"><span class="text-red-600">*</span>
                                <ng-option [value]="'R'">{{"Rent" | translate}}</ng-option>
                                <ng-option [value]="'S'">{{"Re-Sale" | translate}}</ng-option>
                            </ng-select>
                        </div>



                        <div class="mb-6 relative">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Property Type" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <span class="absolute  bottom-4 z-10 rtl:left-3 rtl:right-[unset] right-3 opacity-70">
                                <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                            </span>
                            <!-- <div (click)="propertyTypeDropdown = !propertyTypeDropdown"
                                class="w-full h-11 bg-lightGray relative flex items-center cursor-pointer">
                                <span class="px-3 text-sm cursor-default">{{propertyTypePlaceholder}}</span>
                                <img src="../../../assets/icons/arrow-semi-down.svg"
                                    class="h-2 w-3 absolute top-1/2 -translate-y-1/2 right-3 opacity-70">
                            </div> -->
                            <!-- <div *ngIf="propertyTypeDropdown === true" class="absolute z-10 mt-1 bg-lightGray w-full">
                                <ul>
                                    <li id="SF" class="px-3 py-2 hover:bg-gray-200 cursor-pointer text-sm"
                                        (click)="propertyTypeOptions($event)">
                                        {{"Semi Furnished"}}
                                    </li>
                                    <li id="FN" class="px-3 py-2 hover:bg-gray-200 cursor-pointer text-sm"
                                        (click)="propertyTypeOptions($event)">
                                        {{"Furnished"}}
                                    </li>
                                    <li id="UF" class="px-3 py-2 hover:bg-gray-200 cursor-pointer text-sm"
                                        (click)="propertyTypeOptions($event)">
                                        {{"Unfurnished"}}
                                    </li>
                                </ul>
                            </div> -->
                            <ng-select formControlName="propertyTypeID"
                                class="post-property-dropdown placeholder:text-sm"
                                placeholder="{{'Select' | translate}}">
                                <ng-option [value]="'SF'">{{"Semi Furnished" | translate}}</ng-option>
                                <ng-option [value]="'FN'">{{"Furnished" | translate}}</ng-option>
                                <ng-option [value]="'UF'">{{"Unfurnished" | translate}}</ng-option>
                            </ng-select>
                        </div>
                        <!-- Property Category -->
                        <p class="mb-2 text-sm text-lighterBlack">{{"Property Category" | translate}}<span
                                class="text-red-600">*</span></p>
                        <div class="flex flex-wrap gap-3 mb-6 text-primary">
                            <span id="{{category.id}}" *ngFor="let category of propertyCategories" class="category text-xs rounded-full border py-2 px-3 cursor-default hover:bg-primary hover:border-primary
                                 hover:text-white hover:cursor-pointer transform duration-150  "
                                (click)="selectCategory($event)">{{category.name}}
                            </span>
                        </div>
                        <!-- Rental Frequency -->
                        <div *ngIf="propertyRS === 'R'" class="mb-6 text-primary">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Rental Frequency" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <div class="flex flex-wrap gap-3">
                                <span id="Y" (click)="rentalFre($event)" class="RentalFrq text-xs rounded-full border py-2 px-3 cursor-default hover:bg-primary hover:border-primary
                                    hover:text-white hover:cursor-pointer transform duration-150">{{"Yearly" |
                                    translate}}</span>
                                <span id="M" (click)="rentalFre($event)" class="RentalFrq text-xs rounded-full border py-2 px-3 cursor-default hover:bg-primary hover:border-primary
                                 hover:text-white hover:cursor-pointer transform duration-150">{{"Monthly" |
                                    translate}}</span>
                                <span id="W" (click)="rentalFre($event)" class="RentalFrq text-xs rounded-full border py-2 px-3 cursor-default hover:bg-primary hover:border-primary
                                 hover:text-white hover:cursor-pointer transform duration-150">{{"Weekly" |
                                    translate}}</span>
                                <span id="D" (click)="rentalFre($event)" class="RentalFrq text-xs rounded-full border py-2 px-3 cursor-default hover:bg-primary hover:border-primary
                                 hover:text-white hover:cursor-pointer transform duration-150">{{"Daily" |
                                    translate}}</span>
                            </div>
                        </div>
                        <!-- Rent Price -->
                        <div class="mb-6">
                            <!-- <p *ngIf="propertyRS === 'R'" class="mb-2 text-sm text-lighterBlack">{{"Rent Price"}}</p>
                            <p *ngIf="propertyRS === 'S'" class="mb-2 text-sm text-lighterBlack">{{"Sale Price"}}</p> -->
                            <p *ngIf="propertyRS == null || propertyRS === 'S'" class="mb-2 text-sm text-lighterBlack">
                                {{"Price" | translate}}<span class="text-red-600">*</span></p>
                            <p *ngIf="propertyRS === 'R'" class="mb-2 text-sm text-lighterBlack">{{"Rent" | translate}}/
                                <span *ngIf="rentalFrqId =='D'">{{"Day"|translate}}</span>
                                <span *ngIf="rentalFrqId =='W'">{{"Week" |translate}}</span>
                                <span *ngIf="rentalFrqId =='M'">{{"Month" |translate}}</span>
                                <span *ngIf="rentalFrqId =='Y'">{{"Year" |translate}}</span>
                                <span class="text-red-600">*</span>
                            </p>
                            <input type="text" placeholder="{{'Enter Price' | translate}}"
                                class="w-full bg-lightGray outline-none px-2 py-3 placeholder:text-sm"
                                formControlName="rentPrice">
                        </div>
                        <!-- Bed Rooms -->
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Bed Rooms" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <div class="flex flex-wrap bedroom sm:grid grid-cols-4 gap-y-7 gap-x-7 sm:gap-x-10">
                                <div class="flex items-center gap-1 sm:gap-2 lg:gap-4">
                                    <div id="0" (click)="numberOfBedRooms($event)"
                                        class="num-of-beds h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"Studio" | translate}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="1" (click)="numberOfBedRooms($event)"
                                        class="num-of-beds h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer ">
                                    </div>
                                    <p class="text-sm">1{{"bhk" | translate}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="2" (click)="numberOfBedRooms($event)"
                                        class="num-of-beds h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">2{{"bhk" | translate}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="3" (click)="numberOfBedRooms($event)"
                                        class="num-of-beds h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">3{{"bhk" | translate}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="4" (click)="numberOfBedRooms($event)"
                                        class="num-of-beds h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">4{{"bhk" | translate}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="5" (click)="numberOfBedRooms($event)"
                                        class="num-of-beds h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">5{{"bhk" | translate}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="6" (click)="numberOfBedRooms($event)"
                                        class="num-of-beds h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">6{{"bhk" | translate}}</p>
                                </div>
                            </div>
                        </div>
                        <!-- Number Of Floor -->
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Number Of Floor" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <div class="grid grid-cols-4 gap-y-7 gap-x-10">
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="1" (click)="numberOfFloors($event)"
                                        class="num-of-floors h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"1"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="2" (click)="numberOfFloors($event)"
                                        class="num-of-floors h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer ">
                                    </div>
                                    <p class="text-sm">{{"2"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="3" (click)="numberOfFloors($event)"
                                        class="num-of-floors h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"3"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="4" (click)="numberOfFloors($event)"
                                        class="num-of-floors h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"4"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="5" (click)="numberOfFloors($event)"
                                        class="num-of-floors h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"5"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="6" (click)="numberOfFloors($event)"
                                        class="num-of-floors h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"6"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="7" (click)="numberOfFloors($event)"
                                        class="num-of-floors h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"7"}}</p>
                                </div>
                            </div>
                        </div>
                        <!-- Bath Rooms -->
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Bath Rooms" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <div class="grid grid-cols-4 gap-y-7 gap-x-10">
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="1" (click)="numberOfBathrooms($event)"
                                        class="num-of-rooms h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"1"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="2" (click)="numberOfBathrooms($event)"
                                        class="num-of-rooms h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer ">
                                    </div>
                                    <p class="text-sm">{{"2"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="3" (click)="numberOfBathrooms($event)"
                                        class="num-of-rooms h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"3"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="4" (click)="numberOfBathrooms($event)"
                                        class="num-of-rooms h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"4"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="5" (click)="numberOfBathrooms($event)"
                                        class="num-of-rooms h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"5"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="6" (click)="numberOfBathrooms($event)"
                                        class="num-of-rooms h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"6"}}</p>
                                </div>
                                <div class="flex items-center gap-2 lg:gap-4">
                                    <div id="7" (click)="numberOfBathrooms($event)"
                                        class="num-of-rooms h-7 w-7 min-w-[1.75rem] bg-lightGray  hover:cursor-pointer">
                                    </div>
                                    <p class="text-sm">{{"7"}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- right coloumn -->
                    <div class="col-span-2 md:col-span-1">
                        <div class="mb-6 flex w-full gap-4 max-sm:gap-8">
                            <div class="w-1/2">
                                <p class="mb-2 text-sm text-lighterBlack">{{"Land Size (Sq.Mtr)" | translate}}<span
                                        class="text-red-600">*</span></p>
                                <input type="text"
                                    class="w-full bg-lightGray outline-none px-2 placeholder:text-sm py-3"
                                    pattern="[0-9]{1,6}" maxlength="6" formControlName="landSize"
                                    placeholder="{{'Enter size' | translate }}">
                            </div>
                            <div class="w-1/2">
                                <p class="mb-2 text-sm text-lighterBlack">{{"House Size (Sq. Ft)" | translate}}<span
                                        class="text-red-600">*</span></p>
                                <input type="text"
                                    class="w-full bg-lightGray outline-none px-2 py-3 placeholder:text-sm"
                                    pattern="[0-9]{1,6}" maxlength="6" formControlName="houseSize"
                                    placeholder="{{'Enter size' | translate}}">
                                <div
                                    *ngIf="propertyInformation.get('houseSize')?.invalid && propertyInformation.get('houseSize')?.touched">
                                    <small
                                        *ngIf="(propertyInformation.get('houseSize')?.errors && 
                                    propertyInformation.get('houseSize')?.hasError('pattern')) || (propertyInformation.get('houseSize')?.invalid && personalInformation.get('houseSize')?.touched)"
                                        class="text-red-700 "></small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Keywords" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <div class="w-full h-11 bg-lightGray relative flex items-center">
                                <input #keywordsField type="text"
                                    class="w-full bg-lightGray outline-none px-4 rtl:pl-20 ltr:pr-16 py-3 placeholder:text-sm"
                                    placeholder="{{'Enter keywords' | translate}}" (keydown)="enterKeyWords($event)">
                                <span #addKeywordEl (click)="addKeyword()"
                                    class="cursor-pointer absolute top-1/2 -translate-y-1/2 rtl:left-3 rtl:right-[unset] right-3 flex justify-center items-center">
                                    <span class="text-sm ">{{'Add' | translate}}</span>
                                    <img src="../../../assets/icons/plus-keywords.svg" class="h-2 w-2 mx-1 ">
                                </span>
                            </div>
                        </div>

                        <!-- Keywords container -->
                        <div class="flex flex-wrap gap-3 mb-6">
                            <!-- keyword -->
                            <div *ngFor="let key of keywords"
                                class="rounded-full p-2 border-gray-200 border relative break-all">
                                <p class="text-sm">{{key}}</p>
                                <img (click)="removeKeyword(key)" src="../../../assets/icons/remove-keyword.svg"
                                    class="h-4 w-4 absolute -top-1 -right-1 hover:cursor-pointer">
                            </div>
                        </div>
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Property Title" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <input type="text" placeholder="In English" formControlName="propertyTitleEn"
                                class="w-full bg-lightGray outline-none px-2 py-3 mb-2 placeholder:text-sm ">
                            <input type="text" dir="rtl" placeholder="العربية" formControlName="propertyTitleAr"
                                class="w-full bg-lightGray outline-none px-2 py-3 placeholder:text-sm placeholder:text-right">
                        </div>
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Property Description" | translate}}<span
                                    class="text-red-600">*</span></p>
                            <textarea cols="30" rows="5" type="text" placeholder="In English"
                                formControlName="propertyDescEn"
                                class="w-full bg-lightGray outline-none px-2 py-3 mb-2 placeholder:text-sm resize-icon "></textarea>
                            <textarea cols="30" rows="5" type="text" dir="rtl" placeholder="العربية"
                                formControlName="propertyDescAr"
                                class="w-full bg-lightGray outline-none px-2 py-3 placeholder:text-sm resize-icon placeholder:text-right"></textarea>
                        </div>
                        <div class="mb-6">
                            <p class="mb-2 text-sm text-lighterBlack">{{"Establishment Year" | translate}}<span
                                    class="text-red-600">*</span></p>

                            <input type="text" pattern="[0-9]{1,4}" maxlength="4"
                                class="w-full bg-lightGray outline-none px-2 py-3 mb-2 placeholder:text-sm"
                                formControlName="establishmentYear" placeholder="{{'Enter Year' | translate}}">
                            <div
                                *ngIf="personalInformation.get('establishmentYear')?.invalid && personalInformation.get('establishmentYear')?.touched">
                                <small
                                    *ngIf="(personalInformation.get('establishmentYear')?.errors && 
                                    personalInformation.get('establishmentYear')?.hasError('pattern')) || (personalInformation.get('establishmentYear')?.invalid && personalInformation.get('establishmentYear')?.touched)"
                                    class="text-red-700 "></small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Amenities & Features -->
                <div class="mb-10">
                    <p class="mb-6 text-sm text-lighterBlack">{{"Amenities & Features" | translate}}<span
                            class="text-red-600">*</span></p>
                    <div class="flex flex-wrap justify-center lg:justify-start gap-10 gap-y-5">
                        <div [id]="feature.id" *ngFor="let feature of amenitiesAndFeatures" class="">
                            <div
                                class=" flex items-center justify-center flex-col gap-2 cursor-pointer relative amenities w-20">
                                <div [id]="feature.id" (click)="selectFeature(feature, $event)"
                                    class="selected-feature border-primary border p-3 rounded-full relative">
                                    <img [src]="feature.icon" class="h-7 w-7 image-bg pointer-events-none">
                                </div>
                                <span class="text-xs w-20 md:w-auto text-center">{{feature.name}}</span>
                                <!-- <div class="absolute inset-0 overlay z-10"></div> -->
                            </div>
                        </div>
                    </div>
                </div>



                <div class="mb-6">
                    <p class="mb-3 text-sm">{{"Add Photos Of Your Property" | translate}}<span
                            class="text-red-600">*</span></p>
                    <div class="mb-3 text-sm">{{'Number of Photos allowed for this plan:' | translate }} {{+
                        maxPhotosCanPost}}</div>
                    <div class="flex  gap-5">
                        <div *ngIf="images.length !== 0"
                            class="border-2 border-[#c8c8c8] border-dashed  p-6 flex flex-wrap max-w-[50%] gap-3">
                            <div *ngFor='let url of images' class="relative">
                                <img [src]="url.name" class=" h-32  rounded-sm shadow-md">
                                <img (click)="removeImage(url)" src="../../../assets/icons/remove-keyword.svg"
                                    class="h-4 w-4 absolute -top-2 -right-2 hover:cursor-pointer">
                            </div>
                        </div>
                        <div [ngClass]="images.length == maxPhotosCanPost?'pointer-events-none':'pointer-events-auto'"
                            class="drag-here-img h-44 w-44 border border-[#969696] p-2 relative overflow-hidden ">
                            <div class="text-[.7rem] text-[#bdbdbd] absolute top-[45%] left-0 right-0">
                                <div class=" text-center mx-auto">{{"Upload property image" | translate}}</div>
                                <div class="relative text-center or"><span class="px-2 relative bg-white z-[3]">{{"or" |
                                        translate}}</span></div>
                            </div>
                            <input #propertyPhotos formControlName="file"
                                class="block h-full absolute inset-0 opacity-0 z-[1]" id="file" type="file" multiple=""
                                (change)="onFileChange($event)">
                            <div class="absolute bottom-4 z-[2]  text-center font-normal cursor-pointer text-[.65rem] right-0 left-0 mx-3 py-1 border-2 bg-gray-50 border-[#969696] text-primary rounded-md"
                                (click)="addPropertyPhotos()">{{"Select from device" | translate}}</div>
                            <div *ngIf="images.length == maxPhotosCanPost"
                                class="block absolute inset-0  bg-gray-300 opacity-25 z-[30]"></div>
                        </div>
                    </div>
                </div>

                <!-- 
                <div class="mb-6">
                    <p class="mb-3 text-sm">{{"Civil Id/Passport Copy Of Owner" | translate}}
                    </p>
                    <div class="flex gap-5">
                        <div *ngIf="passportImagesArr"
                            class=" border-2 border-[#c8c8c8] border-dashed p-6 flex flex-wrap max-w-[77%] gap-3">
                            <div class="relative">
                                <img [src]="passportImagesArr" class=" object-cover h-32 w-32">
                                <img (click)="removePasswortImage()" src="../../../assets/icons/remove-keyword.svg"
                                    class="h-4 w-4 absolute -top-1 -right-1 hover:cursor-pointer">
                            </div>
                        </div>
                        <div class="drag-here-img h-44 w-44 border border-[#969696] p-2 relative overflow-hidden">
                            <div class="text-[.7rem] text-[#bdbdbd] absolute top-[45%] left-0 right-0">
                                <div class=" text-center mx-auto">{{"Drag your document here" | translate}}</div>
                                <div class="relative text-center or"><span class="px-2 relative bg-white z-[3]">{{"or" |
                                        translate}}</span></div>
                            </div>
                            <input #passportPhotos formControlName="file"
                                class="block h-full absolute inset-0 opacity-0 z-[1]" id="file" type="file"
                                (change)="passportImages($event)">
                            <div class="absolute bottom-4 z-[2]  text-center font-normal cursor-pointer text-[.65rem] right-0 left-0 mx-3 py-1 border-2 bg-gray-50 border-[#969696] text-primary rounded-md"
                                (click)="addPassportPhotos()">{{"Select from device" | translate}}</div>
                            <div *ngIf="passportImagesArr != ''"
                                class="block absolute inset-0  bg-gray-300 opacity-25 z-[30]"></div>
                        </div>
                    </div>
                </div> -->






                <!-- Back and next pages -->
                <div class="grid grid-cols-2 gap-12">
                    <div (click)="page1()"
                        class="border border-accent text-accent font-bold w-full md:w-1/2 rtl:mr-auto rtl:ml-[unset]  max-sm:px-5  ml-auto px-10 py-3 text-center cursor-pointer">
                        {{"BACK" | translate}}</div>
                    <button type="submit"
                        class="border border-accent text-lighterBlack font-bold w-full md:w-1/2 rtl:ml-auto rtl:mr-[unset]   mr-auto px-10 py-3 bg-accent">{{"NEXT"
                        | translate}}</button>
                </div>
            </form>
        </div>



        <!-- #PAGE 3 CONTAINER -->
        <div [ngClass]="currentPage === 3 ? 'block' : 'hidden'"
            class="container px-6  md:px-10 xl:px-24 bg-white pt-14 pb-24">
            <form *ngIf="otherInformation" [formGroup]="otherInformation" (ngSubmit)="getOtherInformation()">

                <div class="grid grid-cols-2 gap-x-12 lg:gap-x-20 xl:gap-x-32 mb-12">

                    <!-- Left column -->
                    <div class="mb-6 col-span-2 md:col-span-1 relative">
                        <p class="mb-2 text-sm text-lighterBlack">{{"Select Country" | translate}}<span
                                class="text-red-600">*</span></p>
                        <span class="absolute  bottom-4 z-[3] rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                            <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                        </span>
                        <!-- <div (click)="selectCountryDropdown = !selectCountryDropdown"
                        class="w-full h-11 bg-lightGray relative flex items-center cursor-pointer">
                        <span class="px-3 text-sm ">{{selectCountryDropdownPlaceholder}}</span>
                        <img src="../../../assets/icons/arrow-semi-down.svg"
                            class="h-2 w-3 absolute top-1/2 -translate-y-1/2 right-3 opacity-70">
                    </div>
                    <div *ngIf="selectCountryDropdown === true"
                        class="absolute z-10 mt-1 bg-lightGray w-full h-60 overflow-scroll">
                        <ul>
                            <li *ngFor="let country of countries"
                                class="px-3 py-2 hover:bg-gray-200 cursor-pointer text-sm"
                                (click)="selectCountry(country)">
                                {{country.niceName}}
                            </li>
                        </ul>
                    </div> -->
                        <ng-select (change)="getStates(selectedCountryId)" [(ngModel)]="selectedCountryId"
                            formControlName="countryID" class="post-property-dropdown placeholder:text-sm"
                            placeholder="{{'Select' | translate}}">

                            <ng-option *ngFor="let country of countries" [value]="country.country_id">
                                {{country.name}}</ng-option>
                        </ng-select>
                    </div>

                    <!-- Right column -->
                    <div class="mb-6 col-span-2 md:col-span-1 relative cursor-pointer">
                        <p class="mb-2 text-sm text-lighterBlack">{{"Select State" | translate}}<span
                                class="text-red-600">*</span></p>
                        <span class="absolute  bottom-4 z-[3] rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                            <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                        </span>
                        <ng-select (change)="getAreas(selectedStateId)" [(ngModel)]="selectedStateId"
                            formControlName="stateID" class="post-property-dropdown placeholder:text-sm"
                            placeholder="{{'Select State' | translate}}">
                            <ng-option *ngFor="let state of allStates" [value]="state.state_id">
                                {{state.name}}</ng-option>
                        </ng-select>
                    </div>

                    <!-- <div class="mb-6 col-span-2 md:col-span-1 relative cursor-pointer">
                        <p class="mb-2 text-sm text-lighterBlack">{{"Select Area" | translate}}<span
                                class="text-red-600">*</span></p>
                        <span class="absolute  bottom-4 z-[3] rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                            <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                        </span>
                        <ng-select formControlName="areaID" class="post-property-dropdown" placeholder="Select">
                            <ng-option *ngFor="let area of allAreas" [value]="area.area_id">
                                {{area.name}}</ng-option>
                        </ng-select>
                    </div> -->
                </div>


                <!-- Map -->
                <div class=" mb-12 w-full">
                    <p class="mb-2 text-sm text-lighterBlack">{{"Map Location" | translate}}<span
                            class="text-red-600">*</span></p>
                    <div class="map h-[34rem]">
                        <app-map class="h-full" [type]="'modal'" [lat]="editLat" [lng]="editLng"
                            (centerLatlng)="getLatlng($event)"></app-map>
                        <div class="map-center-overlay">
                            <img src="../../../assets/icons/location-pointer.svg" width="50" height="50">
                        </div>
                    </div>
                    <!-- <p>{{latlng}}</p> -->
                </div>

                <!-- Address Fields -->
                <div class="grid grid-cols-2  mt gap-x-12">
                    <!-- left column -->
                    <div class="mb-6 col-span-2 md:col-span-1 relative cursor-pointer">
                        <p class="mb-2 text-sm text-lighterBlack">{{"Select Area" | translate}}<span
                                class="text-red-600">*</span></p>
                        <span class="absolute  bottom-4 z-[3] rtl:left-3 rtl:right-[unset]  right-3 opacity-70">
                            <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                        </span>
                        <ng-select formControlName="areaID" class="post-property-dropdown placeholder:text-sm"
                            placeholder="{{'Select Area' | translate}}">
                            <ng-option *ngFor="let area of allAreas" [value]="area.area_id">
                                {{area.name}}</ng-option>
                        </ng-select>
                    </div>

                    <!-- Land mark and payment method -->

                    <!-- left column -->
                    <div class="mb-6 col-span-2 md:col-span-1">
                        <p class="mb-2 text-sm text-lighterBlack">{{"Land Mark" | translate}}<span
                                class="text-red-600">*</span></p>
                        <input type="text" formControlName="landMark"
                            class="w-full bg-lightGray outline-none px-2 py-3">
                    </div>
                    <!-- right column
                    <div class="flex items-center gap-8">
                        <img src="../../../assets/icons/knet.png" class="payment-method w-16 h-10 border-2 "
                            (click)="paymentMethod($event)">
                        <img src="../../../assets/icons/visa.png" class="payment-method w-16 h-10 border-2 "
                            (click)="paymentMethod($event)">
                    </div> -->


                    <div class="mb-6 col-span-2 md:col-span-1">
                        <p class="mb-2 text-sm text-lighterBlack">{{"Address line in english" | translate}}<span
                                class="text-red-600">*</span></p>
                        <input type="text" formControlName="addressLine1"
                            class="w-full bg-lightGray outline-none px-2 py-3">
                    </div>
                    <!-- right column -->
                    <div class="col-span-2 md:col-span-1 mb-12">
                        <p class="mb-2 text-sm  text-lighterBlack">{{"Address line in arabic" | translate}}<span
                                class="text-red-600">*</span></p>
                        <input type="text" formControlName="addressLine2"
                            class="w-full bg-lightGray outline-none px-2 py-3" dir="rtl">
                    </div>
                </div>



                <!-- Back and next pages -->
                <div *ngIf="is_edit === false" class="grid grid-cols-2 gap-12">
                    <div (click)="page2()"
                        class="border border-accent text-accent font-bold w-full md:w-1/2 rtl:mr-auto rtl:ml-[unset] ml-auto px-10 py-3 cursor-pointer text-center">
                        {{"BACK" | translate}}</div>
                    <button
                        class="border border-accent text-lighterBlack font-bold w-full md:w-1/2 rtl:ml-auto rtl:mr-[unset] mr-auto px-10 py-3 bg-accent">{{"NEXT"
                        | translate}}</button>
                </div>
                <div *ngIf="is_edit === true" class="flex flex-col-reverse md:flex-row justify-center gap-y-6 gap-x-12">
                    <div (click)="page2()"
                        class="border border-accent text-accent font-bold w-full sm:w-3/4 md:w-1/2 lg:w-2/5 xl:w-1/3 2xl:w-1/4  px-10 py-3 cursor-pointer text-center">
                        {{"BACK" | translate}}</div>
                    <button *ngIf="is_edit === true"
                        class="border border-accent text-lighterBlack font-bold w-full sm:w-3/4 md:w-1/2 lg:w-2/5 xl:w-1/3 2xl:w-1/4 px-10 py-3 bg-accent">{{"Update
                        property details" | translate}}</button>
                </div>

            </form>
        </div>
    </div>




</div>