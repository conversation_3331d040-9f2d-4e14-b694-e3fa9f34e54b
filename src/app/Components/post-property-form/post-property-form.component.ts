import { Component, ElementRef, OnInit, Renderer2, ViewChild, Output, EventEmitter, AfterViewChecked } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { AddressService } from 'src/app/Services/Address/address.service';
import { EditPropertyService } from 'src/app/Services/Properties/edit-property.service';
import { PostPropertyService } from 'src/app/Services/Properties/post-property.service';
import { PropertyCategoryService } from 'src/app/Services/Properties/property-category.service';
import { TranslateService } from '@ngx-translate/core';
configSettings

@Component({
  selector: 'app-post-property-form',
  templateUrl: './post-property-form.component.html',
  styleUrls: ['./post-property-form.component.scss'],
})
export class PostPropertyFormComponent implements OnInit, AfterViewChecked {
  // Variables
  currentPage = 1;
  propertyCategories: any
  amenitiesAndFeatures: any
  countries: any = []
  userDetails: any

  @ViewChild('propertyPhotos') propertyPhotosRef: ElementRef
  @ViewChild('passportPhotos') passportPhotosRef: ElementRef




  // edit variables
  is_edit: boolean = false
  editPropertyID: any
  editLat: any
  editLng: any
  newImages: any = []
  editImages: any = []

  loadCustomInputs: boolean = false
  editAllAmentitesRes: any
  editRentalFreqArr: any
  editPropertyCategoryID: any

  @Output() propertyParams = new EventEmitter<any>();
  @Output() showForm = new EventEmitter<any>();

  maxPhotosCanPost: number

  constructor(
    private toastr: ToastrService,
    private addressService: AddressService,
    private postPropertyService: PostPropertyService,
    private renderer: Renderer2,
    private propertyCategoryService: PropertyCategoryService,
    private configSettings: configSettings,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private editPropertyService: EditPropertyService,
    private translateService: TranslateService,
  ) { }

  ngAfterViewChecked(): void {
    if (this.loadCustomInputs === true) {
      Array.from(document.querySelectorAll('.selected-feature')).forEach((el: any) => {
        for (let i = 0; i < this.editAllAmentitesRes.length; i++) {
          if (Number(el.id) == this.editAllAmentitesRes[i]) {
            this.renderer.addClass(el, 'bg-primary')
          }
        }
      });

      if (this.propertyRS == 'R') {
        Array.from(document.querySelectorAll('.RentalFrq')).forEach((el: any) => {
          if (el.id == this.editRentalFreqArr) {
            this.renderer.removeClass(el, 'text-primary')
            this.renderer.addClass(el, 'bg-primary')
            this.renderer.addClass(el, 'text-white')
            this.renderer.addClass(el, 'border-primary')
            this.rentalFrqId = el.id
          }
        });
      }

      Array.from(document.querySelectorAll('.category')).forEach((el: any) => {
        if (Number(el.id) == this.editPropertyCategoryID) { //property_category
          this.renderer.removeClass(el, 'text-primary')
          this.renderer.addClass(el, 'bg-primary')
          this.renderer.addClass(el, 'text-white')
          this.renderer.addClass(el, 'border-primary')
          this.categoryId = el.id
        }
      });
    }

  }

  ngOnInit(): void {

    this.userDetails = this.configSettings.getUserDetails()

    if (localStorage.getItem('maxPhotos')) {
      this.maxPhotosCanPost = Number(localStorage.getItem('maxPhotos'))
    }



    const getParams = {}

    this.addressService.getCountry(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            let tempCountries = response.body.data // because im getting 3 different arrays of countries
            for (let i = 0; i < tempCountries.length; i++) {
              this.countries = [...tempCountries]
            }
            if (this.is_edit != true) {
              this.getStates(114)
            }

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

    this.propertyCategoryService.getPropertyCategory(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.propertyCategories = response.body.data
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

    this.postPropertyService.getAmenitiesAndFeatures(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.amenitiesAndFeatures = response.body.data
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

    // ///////// edit property ////////////
    this.activatedRoute.params.subscribe(params => {
      if (params['id']) {
        this.is_edit = true
        // this.configSettings.setShowLoader(true)
        this.editPropertyID = params['id']
        this.getDetailsForEdit()
      }
    })
  }

  page1() {
    this.currentPage = 1;
    window.scroll({
      top: 0,
      left: 0,
      // behavior: 'smooth',
    });
  }



  // LIST PROPERTY AS
  listPorpertPlaceholder = 'Select';
  listPropertyAsDropdown = false;

  listPropertyAsId: any = ''
  ownerOrCompanyName: any = ''
  emailAddress: any = ''
  whatsappNumber: any = ''
  mobileNumber: any = ''
  socialMedia: any = ''
  whatsappCode: any = ''
  mobilePhoneCode: any = ''

  // option(e: any) {
  //   this.listPropertyAsId = e.target.id
  //   this.listPorpertPlaceholder = e.target.innerText;
  //   this.listPropertyAsDropdown = false;
  // }

  // listPropertyAsType(e: any) {
  //   this.listPropertyAsId = e.target.id
  //   this.listPorpertPlaceholder = e.target.innerText;
  //   this.listPropertyAsDropdown = false;
  // }



  personalInformation = new FormGroup({
    listPropertyAsID: new FormControl(),
    ownerOrCompanyName: new FormControl('', Validators.required),
    emailAddress: new FormControl('', Validators.compose([Validators.required, Validators.pattern("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,4}$")])),
    whatsappNumber: new FormControl('', [Validators.required, Validators.pattern("^[0-9]{8,12}$"), Validators.minLength(8), Validators.maxLength(12)]),
    whatsappCode: new FormControl(''),
    mobileNumber: new FormControl('', [Validators.required, Validators.pattern("^[0-9]{8,12}$"), Validators.minLength(8), Validators.maxLength(12)]),
    mobilePhoneCode: new FormControl(''),
    socialMedia: new FormControl(''),
  })


  getPersonalInfo() {
    this.ownerOrCompanyName = this.personalInformation.value.ownerOrCompanyName
    this.emailAddress = this.personalInformation.value.emailAddress
    this.whatsappNumber = this.personalInformation.value.whatsappNumber
    this.mobileNumber = this.personalInformation.value.mobileNumber
    this.socialMedia = this.personalInformation.value.socialMedia
    this.whatsappCode = this.personalInformation.value.whatsappCode
    this.mobilePhoneCode = this.personalInformation.value.mobilePhoneCode
    // console.log(this.listPropertyAsId, this.ownerOrCompanyName, this.emailAddress, this.whatsappNumber, this.mobileNumber, this.socialMedia)


    // if (this.ownerOrCompanyName == '' || this.emailAddress == '' || this.whatsappNumber == '' || this.mobileNumber == '') {
    //   this.toastr.error('', 'Fields cannot be empty')
    //   return
    // }

    // if (!registerForm.phone.match(/^-?(0|[1-9]\d*)?$/)) {
    //   this.toastr.error('', 'Special characters are not allowed in phone number');
    //   // this.configService.toggleLoading(false);
    //   return
    // }


    if (this.ownerOrCompanyName == '') {
      // this.toastr.error('', 'Contact name cannot be empty')
      this.translateService.get('Contact name cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.emailAddress == '') {
      // this.toastr.error('', 'Email cannot be empty')
      this.translateService.get('Email cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (this.personalInformation.get('emailAddress')?.invalid) {
      // this.toastr.error('', 'Please enter valid email')
      this.translateService.get('Please enter valid email').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    // if (this.whatsappCode == '') {
    //   this.toastr.error('', 'Whatsapp phone code cannot be empty')
    //   return
    // }

    // if (this.whatsappNumber == '') {
    //   this.toastr.error('', 'Whatsapp number cannot be empty')
    //   return
    // }

    // if (!this.whatsappNumber.match(/^-?(0|[1-9]\d*)?$/)) {
    //   this.toastr.error('', 'Only numbers are allowed in Whatsapp phone number')
    //   return
    // }

    // if (this.mobilePhoneCode == '') {
    //   // this.toastr.error('', 'Mobile phone code cannot be empty')
    //   this.translateService.get('Mobile phone code cannot be empty').subscribe(res =>{
    //     this.toastr.error('', res);
    //   })
    //   return
    // }

    if (this.mobileNumber == '') {
      // this.toastr.error('', 'Mobile number cannot be empty')
      this.translateService.get('Mobile number cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (!this.mobileNumber.match(/^-?(0|[1-9]\d*)?$/)) {
      // this.toastr.error('', 'Only numbers are allowed in Mobile phone number')
      this.translateService.get('Only numbers are allowed in mobile phone number').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }



    this.currentPage = 2;
  }



  // ---------------------------------------------------------------------------------------------------------------------

  // PAGE 2
  page2() {
    this.currentPage = 2;
    window.scroll({
      top: 0,
      left: 0,
    });
  }

  // PROPERTY FOR
  propertyRS: any
  propertyForDropdown: any = false
  propertyForPlaceholder: any = 'Select'

  // propertyForRS(event: any) {
  //   this.propertyRS = event.target.id
  // }


  // PROPERTY TYPE DROPDOWN
  propertyTypePlaceholder = 'Select';
  propertyTypeDropdown = false;
  propertyTypeId: any = ''

  togglePropertyType() {
    this.propertyTypeDropdown = !this.propertyTypeDropdown
  }

  // propertyTypeOptions(e: any) {  //done
  //   this.propertyTypePlaceholder = e.target.innerText;
  //   this.propertyTypeDropdown = false;
  //   this.propertyTypeId = e.target.id
  // }

  // SELECT CATEGORY
  categoryId: any = 0

  selectCategory(event: any) { //done
    Array.from(document.querySelectorAll('.category')).forEach(function (el) {
      el.classList.remove('bg-primary');
      el.classList.remove('border-primary');
      el.classList.add('text-primary')
    });
    this.renderer.removeClass(event.target, 'text-primary')
    this.renderer.addClass(event.target, 'bg-primary')
    this.renderer.addClass(event.target, 'text-white')
    this.renderer.addClass(event.target, 'border-primary')
    this.categoryId = event.target.id
  }

  // RENTAL FREQUENCY
  rentalFrqId: any = ''

  rentalFre(event: any) { //done
    Array.from(document.querySelectorAll('.RentalFrq')).forEach(function (el) {
      el.classList.remove('bg-primary');
      el.classList.add('text-primary')
      el.classList.remove('border-primary');
    });
    this.renderer.removeClass(event.target, 'text-primary')
    this.renderer.addClass(event.target, 'bg-primary')
    this.renderer.addClass(event.target, 'text-white')
    this.renderer.addClass(event.target, 'border-primary')
    this.rentalFrqId = event.target.id
  }


  // BED ROOMS

  numOfBeds: any = ''
  numberOfBedRooms(event: any) {
    Array.from(document.querySelectorAll('.num-of-beds')).forEach(function (el) {
      el.classList.remove('custom-checkmark')
    });
    this.renderer.addClass(event.target, 'custom-checkmark')
    this.numOfBeds = event.target.id
  }

  numOfFloors: any = ''
  numberOfFloors(event: any) {
    Array.from(document.querySelectorAll('.num-of-floors')).forEach(function (el) {
      el.classList.remove('custom-checkmark')
    });
    this.renderer.addClass(event.target, 'custom-checkmark')
    this.numOfFloors = event.target.id
  }

  numOfBathrooms: any = ''
  numberOfBathrooms(event: any) {
    Array.from(document.querySelectorAll('.num-of-rooms')).forEach(function (el) {
      el.classList.remove('custom-checkmark')
    });
    this.renderer.addClass(event.target, 'custom-checkmark')
    this.numOfBathrooms = event.target.id
  }






  keywords: any = [];
  @ViewChild('keywordsField') keywordsFieldRef: any;
  @ViewChild('addKeywordEl') addKeywordElRef: any;
  addKeyword() {
    if (this.keywordsFieldRef.nativeElement.value === '') {
      // this.toastr.error('', 'Keywords cannot be empty');
      this.translateService.get('Keywords cannot be empty').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    this.keywords.push(this.keywordsFieldRef.nativeElement.value);
    this.keywordsFieldRef.nativeElement.value = '';
  }

  removeKeyword(event: any) {
    this.keywords.splice(this.keywords.indexOf(event), 1);
  }

  enterKeyWords(e: any) {
    if (e.key === "Enter") {
      this.addKeywordElRef.nativeElement.click()
      e.preventDefault();
    }
  }

  propertyInformation = new FormGroup({
    propertyForID: new FormControl(),
    propertyTypeID: new FormControl(),
    rentPrice: new FormControl(''),
    houseSize: new FormControl('', Validators.compose([Validators.required, Validators.pattern("^[0-9]{1,6}$"), Validators.maxLength(6)])),
    landSize: new FormControl('', Validators.compose([Validators.required, Validators.pattern("^[0-9]{1,6}$"), Validators.maxLength(6)])),
    propertyTitleEn: new FormControl(''),
    propertyTitleAr: new FormControl(''),
    propertyDescEn: new FormControl(''),
    propertyDescAr: new FormControl(''),
    establishmentYear: new FormControl('', Validators.compose([Validators.required, Validators.pattern("^[0-9]{4}$"), Validators.maxLength(4)])),
    file: new FormControl(''),
    propertyImages: new FormControl(''),
    // passportImage: new FormControl(''),
  })

  propertyForID: any = ''
  rentPrice: any = ''
  houseSize: any = ''
  landSize: any = ''
  propertyTitleEn: any = ''
  propertyTitleAr: any = ''
  propertyDescEn: any = ''
  propertyDescAr: any = ''
  establishmentYear: any = ''
  selectedKeywords: any = ''
  propertyImages: any
  // passportImage: any
  propertyImagesStr: any
  passportImageStr: any


  getPropertyInformation() {

    this.selectedKeywords = this.keywords.join()

    this.rentPrice = this.propertyInformation.value.rentPrice
    this.houseSize = this.propertyInformation.value.houseSize
    this.landSize = this.propertyInformation.value.landSize
    this.propertyTitleEn = this.propertyInformation.value.propertyTitleEn
    this.propertyTitleAr = this.propertyInformation.value.propertyTitleAr
    this.propertyDescEn = this.propertyInformation.value.propertyDescEn
    this.propertyDescAr = this.propertyInformation.value.propertyDescAr
    this.establishmentYear = this.propertyInformation.value.establishmentYear
    this.propertyImages = this.propertyInformation.value.propertyImages
    // this.passportImage = this.propertyInformation.value.passportImage
    this.propertyForID = this.propertyInformation.value.propertyForID
    this.propertyTypeId = this.propertyInformation.value.propertyTypeID


    if (this.propertyForID == undefined) {
      // this.toastr.error('', "Property for cannot be empty")
      this.translateService.get('Please select property for').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.propertyTypeId == undefined) {
      // this.toastr.error('', "Please select a property type")
      this.translateService.get('Please select a property type').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.categoryId == '') {
      // this.toastr.error('', "Please select a property category")
      this.translateService.get('Please select a property category').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.rentalFrqId == '' && this.propertyForID == 'R') {
      // this.toastr.error('', "Please select rental frequency")
      this.translateService.get('Please select rental frequency').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.rentPrice == '' && this.propertyForID == 'R') {
      // this.toastr.error('', "Please select the rent price")
      this.translateService.get('Please enter the rent price').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.rentPrice == '' && this.propertyForID == 'S') {
      // this.toastr.error('', "Please select the rent price")
      this.translateService.get('Please enter the re-sell price').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.numOfBeds == '') {
      // this.toastr.error('', "Please select number of beds in the property")
      this.translateService.get('Please select number of bed rooms in the property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (this.numOfFloors == '') {
      // this.toastr.error('', "Please select number of floors in the property")
      this.translateService.get('Please select number of floors in the property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.numOfBathrooms == '') {
      // this.toastr.error('', "Please select number of bathrooms in the property")
      this.translateService.get('Please select number of bathrooms in the property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.landSize == '') {
      // this.toastr.error('', "Please enter land size")
      this.translateService.get('Please enter land size').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.houseSize == '') {
      // this.toastr.error('', "Please enter house size")
      this.translateService.get('Please enter house size').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.propertyInformation.get('houseSize')?.invalid) {
      // this.toastr.error('', "Please enter valid house size")
      this.translateService.get('Please enter valid house size').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (this.propertyInformation.get('landSize')?.invalid) {
      // this.toastr.error('', "Please enter valid land size")
      this.translateService.get('Please enter valid land size').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.selectedKeywords == '') {
      // this.toastr.error('', "Please enter keywords for your property")
      this.translateService.get('Please enter keywords for your property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.propertyTitleEn == '') {
      // this.toastr.error('', "Please enter property title")
      this.translateService.get('Please enter property title (English)').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (this.propertyTitleAr == '') {
      // this.toastr.error('', "Please enter property title")
      this.translateService.get('Please enter property title (Arabic)').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.propertyDescEn == '') {
      // this.toastr.error('', "Please enter property description")
      this.translateService.get('Please enter property description (English)').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (this.propertyDescAr == '') {
      // this.toastr.error('', "Please enter property description")
      this.translateService.get('Please enter property description (Arabic)').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.establishmentYear == '') {
      // this.toastr.error('', "Please enter establishment year of the property")
      this.translateService.get('Please enter establishment year of the property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (this.propertyInformation.get('establishmentYear')?.invalid) {
      // this.toastr.error('', "Please enter establishment year of the property")
      this.translateService.get('Please enter valid establishment year of the property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.selectedFeatures.length == 0) {
      // this.toastr.error('', "Select features of your property")
      this.translateService.get('Select features of your property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.propertyImages.length == 0) {
      // this.toastr.error('', "Upload atleast 1 image of the property")
      this.translateService.get('Upload atleast 1 image of the property').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    // if (this.passportImage.length == 0) {
    //   // this.toastr.error('', "Upload atleast 1 image of the property")
    //   this.translateService.get('Upload atleast 1 image of the civil id or passport').subscribe(res => {
    //     this.toastr.error('', res);
    //   })
    //   return
    // }



    window.scroll({
      top: 0,
      left: 0,
    });





    this.currentPage = 3;
  }


  images: any = []

  passportImagesArr: any = ''

  onFileChange(event: any) {
    if (this.images.length < this.maxPhotosCanPost) {
      if (event.target.files && event.target.files[0]) {
        const filesAmount = event.target.files.length;

        for (let i = 0; i <= filesAmount; i++) {
          const reader = new FileReader();
          if (this.images.length + i === this.maxPhotosCanPost) {
            // console.log('test')
            // this.toastr.error('', `Cannot upload more than ${this.maxPhotosCanPost} photos as per the chosen plan`)

            this.translateService.get('Cannot upload more than #no photos as per the chosen plan').subscribe(res => {
              this.toastr.error('', res.replace('#no', this.maxPhotosCanPost));
            })

            break
          }
          reader.onload = (event: any) => {
            if (this.images.length < this.maxPhotosCanPost) {

              if (this.is_edit == false) {
                this.images.push({
                  id: '',
                  name: event.target.result
                });


                this.propertyInformation.patchValue({
                  propertyImages: this.images
                });
              }


              if (this.is_edit == true) {
                this.newImages.push({
                  id: '',
                  name: event.target.result
                });

                this.images = [...this.editImages, ...this.newImages]
                this.propertyInformation.patchValue({
                  propertyImages: this.images
                });
              }

            } else {
              // this.toastr.warning('', `Cannot upload more than ${this.maxPhotosCanPost} images for this plan`)
              this.translateService.get(`Cannot upload more than #no photos as per the chosen plan`).subscribe(res => {
                res = res.replace('#no', `${this.maxPhotosCanPost}`)
                this.toastr.warning('', res);
              })
              return
            }
          }
          reader.readAsDataURL(event.target.files[i]);

        }






      }

    } else {
      this.translateService.get(`Cannot upload more than #no photos as per the chosen plan`).subscribe(res => {
        res = res.replace('#no', `${this.maxPhotosCanPost}`)
        this.toastr.warning('', res);
      })
      return
    }
  }

  addPropertyPhotos() {
    this.propertyPhotosRef.nativeElement.click()

  }

  removeImage(img: any) {
    if (this.is_edit == true) {
      this.configSettings.setShowLoader(true)
      if (img.id == '') {
        this.newImages.splice(this.newImages.indexOf(img), 1);
        this.images = [...this.editImages, ...this.newImages]
        this.configSettings.setShowLoader(false)

      } else {

        const postParams = {
          media_id: img.id,
          property_id: this.editPropertyID
        }

        this.editPropertyService.deleteMedia({}, postParams).subscribe({
          next: (response) => {
            if (response.status === 200) {
              if (response.body.status === 200) {

                this.images = [...response.body.data, ...this.newImages]
                this.configSettings.setShowLoader(false)

              }
            }
          },
          error: (err) => {
            this.toastr.error('', err.error.message);
          }
        })
      }



    } else {
      this.images.splice(this.images.indexOf(img), 1);
    }
  }

  // removePasswortImage() {
  //   if (this.is_edit == true) {
  //     const postParams = {
  //       property_id: this.editPropertyID,
  //       is_proof_image: 1
  //     }

  //     this.editPropertyService.deleteMedia({}, postParams).subscribe({
  //       next: (response) => {
  //         if (response.status === 200) {
  //           if (response.body.status === 200) {
  //             this.configSettings.setShowLoader(false)
  //             this.passportImagesArr = ''
  //             this.passportImage = ''
  //             this.propertyInformation.patchValue({
  //               passportImage: ''
  //             });
  //           }
  //         }
  //       },
  //       error: (err) => {
  //         this.toastr.error('', err.error.message);
  //       }
  //     })
  //   } else {
  //     this.passportImagesArr = ''
  //     this.passportImage = ''
  //     this.propertyInformation.patchValue({
  //       passportImage: ''
  //     });
  //   }
  // }

  // passportImages(event: any) {
  //   if (event.target.files && event.target.files[0]) {
  //     const filesAmount = event.target.files.length;
  //     for (let i = 0; i < filesAmount; i++) {
  //       const reader = new FileReader();

  //       reader.onload = (event: any) => {
  //         this.passportImagesArr = event.target.result
  //         this.propertyInformation.patchValue({
  //           passportImage: event.target.result
  //         });
  //       }

  //       reader.readAsDataURL(event.target.files[i]);
  //     }
  //   }
  // }


  addPassportPhotos() {
    this.passportPhotosRef.nativeElement.click()
  }

  selectedFeatures: any = []

  selectFeature(feature: any, event: any) {

    let found = false
    let foundID

    const hasClass = event.target.classList.contains('bg-primary');
    // console.log(this.selectFeature,'is mobile');
    if (hasClass) {
      this.renderer.removeClass(event.target, 'bg-primary');
    } else {
      this.renderer.addClass(event.target, 'bg-primary');
    }

    if (this.selectedFeatures.length == 0) {
      this.selectedFeatures.push(feature.id)
    } else {
      for (let i = 0; i < this.selectedFeatures.length; i++) {
        if (feature.id == this.selectedFeatures[i]) {
          found = true
          foundID = this.selectedFeatures.indexOf(feature.id)

        }
      }
      if (found == true) {
        this.selectedFeatures.splice(this.selectedFeatures.indexOf(feature.id), 1)
      } else {
        this.selectedFeatures.push(feature.id)
      }
    }
  }

  // ---------------------------------------------------------------------------------------------------------------------

  // Page 3
  page3() {
    this.currentPage = 3;
    window.scroll({
      top: 0,
      left: 0,
    });

  }

  selectCountryDropdown = false
  selectCountryDropdownPlaceholder = "Select"
  selectedCountryId: any = 114


  allStates: any = []
  selectedStateId: any = ''
  getStates(countryID: any) {

    this.otherInformation.patchValue({
      stateID: null,
    })

    const getParams = {
      country_id: countryID

    }

    this.addressService.getState(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allStates = response.body.data

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  allAreas: any = []
  selectedAreaId: any = ''

  getAreas(stateID: number) {


    const getParams = {
      state_id: stateID
    }

    this.addressService.getAreas(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allAreas = response.body.data

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  // selectCountry(country: any) {
  //   this.selectCountryDropdownPlaceholder = country.niceName
  //   this.selectCountryDropdown = false
  //   this.selectedCountryId = country.country_id
  //   this.selectLocationPlaceholder = "Select"
  //   this.selectedStateId = ''
  //   this.getStates(this.selectedCountryId)
  // }


  selectLocationDropdown = false
  selectLocationPlaceholder = "Select"

  // getSelectedState(state: any) {
  //   this.selectedStateId = state.state_id
  //   this.selectLocationPlaceholder = state.name
  //   this.selectLocationDropdown = false

  // }

  addressLine1: any = ''
  addressLine2: any = ''
  landMark: any = ''


  otherInformation = new FormGroup({

    countryID: new FormControl(),
    stateID: new FormControl(),
    areaID: new FormControl(),
    addressLine1: new FormControl(),
    addressLine2: new FormControl(),
    landMark: new FormControl(),
  })


  paymentMethod(event: any) {
    Array.from(document.querySelectorAll('.payment-method')).forEach(function (el) {
      el.classList.remove('border-accent');
    });
    this.renderer.addClass(event.target, 'border-accent')
  }

  getOtherInformation() {

    this.selectedCountryId = this.otherInformation.value.countryID
    this.selectedStateId = this.otherInformation.value.stateID
    this.selectedAreaId = this.otherInformation.value.areaID
    this.addressLine1 = this.otherInformation.value.addressLine1
    this.addressLine2 = this.otherInformation.value.addressLine2
    this.landMark = this.otherInformation.value.landMark



    if (this.selectedCountryId == undefined) {
      // this.toastr.error('', 'Contact name cannot be empty')
      this.translateService.get('Please select country').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }


    if (this.selectedStateId == undefined) {
      // this.toastr.error('', 'Contact name cannot be empty')
      this.translateService.get('Please select state').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (this.selectedAreaId == undefined) {
      // this.toastr.error('', 'Contact name cannot be empty')
      this.translateService.get('Please select area').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.landMark == undefined) {
      // this.toastr.error('', 'Contact name cannot be empty')
      this.translateService.get('Please enter landmark').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }


    if (this.addressLine1 == undefined) {
      // this.toastr.error('', 'Contact name cannot be empty')
      this.translateService.get('Address line in english').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (this.addressLine2 == undefined) {
      // this.toastr.error('', 'Contact name cannot be empty')
      this.translateService.get('Address line in arabic').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }


    if (this.is_edit === false) {

      this.savePropertyDetails()

    }

    if (this.is_edit === true) {

      this.editProperty()
    }
  }


  // lat = 29.32113504834722;
  // lng = 47.99349617438888;

  // lat: any = 29.32113504834722;
  // lng: any = 47.99349617438888;
  lat: any;
  lng: any;

  latlng: any
  getLatlng(event: any) {
    this.lat = event.lat
    this.lng = event.lng
    this.latlng = `${this.lat}, ${this.lng}`
  }

  savePropertyDetails() {
    const postPropertyImages: any = []
    this.propertyImages.forEach((el: any) => {
      if (el.id == '') {
        postPropertyImages.push(el.name)
      }
    });

    const postParams = {
      "list_property_as": "",
      "property_for": this.propertyForID,
      "whatsapp_number": this.whatsappNumber,
      "contact_name": this.ownerOrCompanyName,
      "mobile_number": this.mobileNumber,
      "email": this.emailAddress,
      "social_media_link": this.socialMedia,
      "property_category": this.categoryId,
      "property_type": this.propertyTypeId,
      "keywords": this.selectedKeywords,
      "rental_frequency": this.rentalFrqId,
      "property_title_en": this.propertyTitleEn,
      "property_title_ar": this.propertyTitleAr,
      "property_desc_en": this.propertyDescEn,
      "property_desc_ar": this.propertyDescAr,
      "established_year": this.establishmentYear,
      "features": this.selectedFeatures.join(),
      "rent_price": this.rentPrice,
      "bath_rooms": this.numOfBathrooms,
      "state_id": this.selectedStateId,
      "area_id": this.selectedAreaId,
      "house_size": this.houseSize,
      "land_size": this.landSize,
      "bed_rooms": this.numOfBeds,
      "no_of_floor": this.numOfFloors,
      "country_id": this.selectedCountryId,
      "address_line_1": this.addressLine1,
      "address_line_2": this.addressLine2,
      "landmark": this.landMark,
      "user_id": this.userDetails.user_id,
      "property_images": postPropertyImages,
      // "passport_image": this.passportImage,
      "latlon": this.latlng,
      // "paymode": "K", // not being processed from backend, come back later to it
      "amenities": "",
      "logo": "",
    }


    console.log(postParams)

    this.propertyParams.emit(postParams);
    // this.showForm.emit(false)

    // localStorage.setItem('propertyDetails', JSON.stringify(postParams));

    // this.router.navigate(['/feature-my-post'])


    // this.postPropertyService.postProperties(getParams, postParams).subscribe({
    //   next: (response) => {
    //     if (response.status === 200) {
    //       if (response.body.status === 200) {
    //         this.router.navigate(['/my-account/my-property-listing'])
    //       }
    //     }
    //   },
    //   error: (err) => {
    //     this.toastr.error('', err.error.message);
    //   }
    // })

  }





  ////////////////////////////////////////////// EDIT PROPERTY //////////////////////////////////////////////////////////////


  getDetailsForEdit() {
    this.configSettings.setShowLoader(true)
    const getParams = {
      id: this.editPropertyID
    }
    this.editPropertyService.getDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.configSettings.setShowLoader(false)

            this.personalInformation.patchValue({
              ownerOrCompanyName: response.body.data.contact_name,
              emailAddress: response.body.data.contact_email,
              mobileNumber: response.body.data.contact_phone,
              whatsappNumber: response.body.data.contact_whatsapp,
              socialMedia: response.body.data.social_media_link,
            })

            if (response.body.data?.keywords) {
              this.keywords = response.body.data?.keywords.split(",")
            }

            response.body.data.latlon

            this.editLat = parseFloat(response.body.data.latlon?.split(',')[0])
            this.editLng = parseFloat(response.body.data.latlon?.split(',')[1])
            this.editImages = response.body.data.media
            this.images = this.editImages
            this.passportImagesArr = response.body.data.proof_front

            this.propertyInformation.patchValue({
              propertyForID: response.body.data.property_for,
              propertyTypeID: response.body.data.property_type,
              rentPrice: response.body.data.price,
              houseSize: response.body.data.house_size.slice(0, -4),// remove when issue  solve from backend
              landSize: response.body.data.land_size.slice(0, -4),// temporarily assign
              propertyTitleEn: response.body.data.title_en,
              propertyTitleAr: response.body.data.title_ar,
              propertyDescEn: response.body.data.desc_en,
              propertyDescAr: response.body.data.desc_ar,
              establishmentYear: response.body.data.build_year,
              propertyImages: this.images,
            })

            this.getStates(response.body.data.country_id)
            this.getAreas(response.body.data.state_id)

            this.otherInformation.patchValue({
              countryID: response.body.data.country_id,
              stateID: response.body.data.state_id,
              areaID: response.body.data.area_id,
              addressLine1: response.body.data.address_line_1,
              addressLine2: response.body.data.address_line_2,
              landMark: response.body.data.landmark,
            })
            // response.body.data.media.forEach((el: any) => {
            // })
            this.editPropertyCategoryID = response.body.data.property_category.id
            // setTimeout(() => {
            //   Array.from(document.querySelectorAll('.category')).forEach((el: any) => {
            //     if (Number(el.id) == response.body.data.property_category.id) { //property_category
            //       this.renderer.removeClass(el, 'text-primary')
            //       this.renderer.addClass(el, 'bg-primary')
            //       this.renderer.addClass(el, 'text-white')
            //       this.renderer.addClass(el, 'border-primary')
            //       this.categoryId = el.id
            //     }
            //   });
            // }, 1000);

            this.propertyRS = response.body.data.property_for
            this.editRentalFreqArr = response.body.data.rental_frequency
            // setTimeout(() => {
            //   if (this.propertyRS == 'R') {
            //     Array.from(document.querySelectorAll('.RentalFrq')).forEach((el: any) => {
            //       if (el.id == response.body.data.rental_frequency) {
            //         this.renderer.removeClass(el, 'text-primary')
            //         this.renderer.addClass(el, 'bg-primary')
            //         this.renderer.addClass(el, 'text-white')
            //         this.renderer.addClass(el, 'border-primary')
            //         this.rentalFrqId = el.id
            //       }
            //     });
            //   }
            // }, 1000);



            Array.from(document.querySelectorAll('.num-of-beds')).forEach((el: any) => {
              if (Number(el.id) == response.body.data.no_of_bed_rooms) {
                this.renderer.addClass(el, 'custom-checkmark')
                this.numOfBeds = el.id
              }
            });

            Array.from(document.querySelectorAll('.num-of-floors')).forEach((el: any) => {
              if (Number(el.id) == response.body.data.no_of_floors) {
                this.renderer.addClass(el, 'custom-checkmark')
                this.numOfFloors = el.id
              }
            });

            Array.from(document.querySelectorAll('.num-of-rooms')).forEach((el: any) => {
              if (Number(el.id) == response.body.data.no_of_bathrooms) {
                this.renderer.addClass(el, 'custom-checkmark')
                this.numOfBathrooms = el.id
              }
            });


            this.editAllAmentitesRes = response.body.data.all_amenity_ids

            // setTimeout(() => {

            //   Array.from(document.querySelectorAll('.selected-feature')).forEach((el: any) => {
            //     for (let i = 0; i < response.body.data.all_amenity_ids.length; i++) {
            //       if (Number(el.id) == response.body.data.all_amenity_ids[i]) {
            //         this.renderer.addClass(el, 'bg-primary')
            //       }
            //     }
            //   });
            // }, 1000);
            this.selectedFeatures = response.body.data.all_amenity_ids
            this.loadCustomInputs = true



            // no_of_bathrooms no_of_bed_rooms

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  editProperty() {

    const postPropertyImages: any = []
    this.propertyImages.forEach((el: any) => {
      if (el.id == '') {
        postPropertyImages.push(el.name)
      }
    });

    const postParams = {
      "property_id": this.editPropertyID,
      "list_property_as": "",
      "property_for": this.propertyForID,
      "whatsapp_number": this.whatsappNumber,
      "contact_name": this.ownerOrCompanyName,
      "mobile_number": this.mobileNumber,
      "email": this.emailAddress,
      "social_media_link": this.socialMedia,
      "property_category": this.categoryId,
      "property_type": this.propertyTypeId,
      "keywords": this.selectedKeywords,
      "rental_frequency": this.rentalFrqId,
      "property_title_en": this.propertyTitleEn,
      "property_title_ar": this.propertyTitleAr,
      "property_desc_en": this.propertyDescEn,
      "property_desc_ar": this.propertyDescAr,
      "established_year": this.establishmentYear,
      "features": this.selectedFeatures.join(),
      "rent_price": this.rentPrice,
      "bath_rooms": this.numOfBathrooms,
      "state_id": this.selectedStateId,
      "area_id": this.selectedAreaId,
      "house_size": this.houseSize,
      "land_size": this.landSize,
      "bed_rooms": this.numOfBeds,
      "no_of_floor": this.numOfFloors,
      "country_id": this.selectedCountryId,
      "address_line_1": this.addressLine1,
      "address_line_2": this.addressLine2,
      "landmark": this.landMark,
      "user_id": this.userDetails.user_id,
      "property_images": postPropertyImages,
      // "passport_image": this.passportImage,
      "latlon": this.latlng,
    }


    this.editPropertyService.editProperty({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.router.navigate(['/my-account/my-property-listing'])
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }
}

