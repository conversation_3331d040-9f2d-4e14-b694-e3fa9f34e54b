import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PostPropertyFormComponent } from './post-property-form.component';
import { ClickedOutsideDirective } from 'src/app/Directives/clicked-outside/clicked-outside.directive';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MapModule } from 'src/app/Components/map/map.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule } from '@angular/router';


@NgModule({
  declarations: [PostPropertyFormComponent ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MapModule,
    FormsModule,
    NgSelectModule,
    TranslateModule,
    RouterModule,
  ], exports:[PostPropertyFormComponent]
})
export class PostPropertyFormModule { }
