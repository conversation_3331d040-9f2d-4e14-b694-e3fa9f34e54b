import { Component, OnInit, ViewChild, ElementRef, NgZone, Input, EventEmitter, Output } from "@angular/core";
import { MouseEvent, MapsAPILoader } from '@agm/core';
import { configSettings } from "src/app/Config/config.settings";
import { Router } from "@angular/router";
import { max } from "rxjs";
configSettings
@Component({
  selector: 'app-map',
  templateUrl: './map.component.html',
  styleUrls: ['./map.component.scss']
})
export class MapComponent implements OnInit {

  constructor(
    private mapsAPILoader: MapsAPILoader,
    private ngZone: NgZone,
    private configSettings: configSettings,
    private router: Router,

  ) { }

  @Input() mapDetails: any
  @Input() type: string = ''
  @Output() centerLatlng = new EventEmitter<string>();
  @Input() iconUrlWidth: any
  @Input() iconUrlHeight: any
  @Input() fitBounds = false


  // lat = 29.32113504834722;
  // lng = 47.99349617438888;

  // area guides
  @Input() showSearch: any = true
  @Input() lat = 29.32113504834722;
  @Input() lng = 47.99349617438888;
  @Input() zoom = 12

  @Input() latlongArr: any

  iconUrl: any = {
    url: 'assets/icons/location-pointer.svg',
    scaledSize: {
      width: 50,
      height: 50
    }
  }

  @ViewChild('search', { static: false })
  public searchElementRef: ElementRef;

  allMapData: any = [];
  private geoCoder: any

  maxLat: any
  minLat: any

  maxLong: any
  minLong: any

  latArray: any = []
  longArray: any = []


  ngOnInit(): void {
    if (!this.lat && !this.lng) {
      this.lat = 29.32113504834722
      this.lng = 47.99349617438888

    }
    this.configSettings.getMapDetails().subscribe(res => {
      this.allMapData = []
      this.latArray = []
      this.longArray = []
      this.mapDetails = res
      this.mapDetails.forEach((el: any) => {

        if (!Number.isNaN(parseFloat(el.latlon?.split(',')[0]))) {
          this.latArray.push(parseFloat(el.latlon?.split(',')[0]))
          this.longArray.push(parseFloat(el.latlon?.split(',')[1]))
        }

      });
      this.maxLat = Math.max(...this.latArray)
      this.minLat = Math.min(...this.latArray)
      this.maxLong = Math.max(...this.longArray)
      this.minLong = Math.min(...this.longArray)
      this.getAllPropertiesInMap()
    })



    //load Places Autocomplete
    this.mapsAPILoader.load().then(() => {
      // this.setCurrentLocation();
      this.geoCoder = new google.maps.Geocoder;

      let autocomplete = new google.maps.places.Autocomplete(this.searchElementRef?.nativeElement, {
        // componentRestrictions: { country: 'KW' },
        // fields: ['address_components', 'geometry', 'icon', 'name'],
        strictBounds: false,
        // types: ['establishment'],
      });
      autocomplete.addListener("place_changed", () => {
        this.ngZone.run(() => {
          //get the place result
          let place: google.maps.places.PlaceResult = autocomplete.getPlace();

          //verify result
          if (place.geometry === undefined || place.geometry === null) {
            return;
          }

          //set latitude, longitude and zoom
          this.lat = place.geometry.location.lat();
          this.lng = place.geometry.location.lng();
          this.zoom = 18;
        });
      });
    });
  }


  centerChanged(e: any) {
    // console.log('middle', e)
    this.centerLatlng.emit(e)
  }


  async getAllPropertiesInMap() {
    if (this.mapDetails != undefined && this.mapDetails != null && this.mapDetails.length != 0) {
      await this.mapDetails.forEach((el: any) => {

        if (el.latlon) {
          this.allMapData.push({
            latLong: {
              id: el.id,
              thumbnail: el.thumbnail,
              title: el.title,
              area: el.area,
              property_type: el.property_type,
              lat: parseFloat(el.latlon?.split(',')[0]),
              lng: parseFloat(el.latlon?.split(',')[1]),
            },
          });

        }
        // else {

        //   // this.allMapData.push({
        //   //   latLong: {
        //   //     lat: 29.32113504834722,
        //   //     lng: 47.99349617438888,
        //   //   },
        //   // });
        // }




        // name: this.mapDetails.name,
        // id: this.mapDetails.id,
        // image: this.mapDetails.image,
        // clinicName:this.mapDetails.clinicName,
        // areaName: this.mapDetails.areaName,
        // stateName:this.mapDetails.stateName,
        // countryName:this.mapDetails.countryName
      });
      // console.log(this.allMapData)
    }

  }

  mapReady(map: any) {
    map.addListener('dragend', () => {
      //console.log(this.centerLatitude, this.centerLongitude)
      // do something with centerLatitude/centerLongitude
    });
  }


  markerDragEnd(event: any) {
    this.lat = event.coords.lat
    this.lng = event.coords.lng
  }



  openedWindow: number = 0; // alternative: array of numbers

  openWindow(id: any) {
    this.openedWindow = id; // alternative: push to array of numbers
  }

  isInfoWindowOpen(id: any) {
    return this.openedWindow == id; // alternative: check if id is in array
  }


  closeWindow() {
    this.openedWindow = 0
  }

  routeToPropertyDetails(id: any) {
    this.router.navigate(['/property-details/' + id])
    // [routerLink]="['/property-details', m?.latLong?.id]"
    // console.log('test')
  }
}
