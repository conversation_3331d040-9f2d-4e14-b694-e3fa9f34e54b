<div class="form-group">
  <input *ngIf="showSearch" type="text" class="form-control" (keydown.enter)="$event.preventDefault()"
    class="w-full outline-none border border-gray-200 p-2 " placeholder="{{'Enter Address' | translate}}" autocorrect="on"
    autocapitalize="off" spellcheck="off" type="text" #search>
</div>
<agm-map [fitBounds]="fitBounds" [keyboardShortcuts]="false" [disableDefaultUI]="true" [ngClass]="type == 'modal' ? 'modal-styles':'map-styles'" [latitude]="lat" [longitude]="lng" [zoom]="zoom" (centerChange)="centerChanged($event)">
  <agm-marker [agmFitBounds]="true" *ngFor="let m of allMapData" [latitude]="m?.latLong?.lat" [longitude]="m?.latLong?.lng" (mouseOver)="openWindow(m?.latLong?.id)" 
  (mouseOut)="closeWindow()"
    [iconUrl]="iconUrl" [markerDraggable]="false">
    <agm-info-window 
    [isOpen]="isInfoWindowOpen(m?.latLong?.id)"
    [latitude]="m?.latLong?.lat" 
    [longitude]="m?.latLong?.lng">
    <div class="flex gap-2 w-[17rem] cursor-pointer" (click)="routeToPropertyDetails(m?.latLong?.id)">
      <!-- {{m?.latLong?.id}} -->
      <img [src]="m?.latLong?.thumbnail" class="w-28 h-28 object-cover">
      <div class="flex flex-col gap-1">
        <p class="text-base tracking-tight font-bold text-primary">{{m?.latLong?.title}}</p>
        <p class="text-sm tracking-tight  ">{{m?.latLong?.area}}</p>
        <p class="text-sm tracking-tight  " *ngIf="m?.latLong?.property_type =='SF'">{{"SemiFurnished" | translate}}</p>
        <p class="text-sm tracking-tight  " *ngIf="m?.latLong?.property_type =='FN'">{{"Fully Furnished" | translate}}</p>
        <p class="text-sm tracking-tight  " *ngIf="m?.latLong?.property_type =='UF'">{{"UnFurnished" | translate}}</p>
        <p class="text-xs tracking-tight mb-3">2 BHK {{"Apartment" | translate}}</p>
        <div class="flex gap-2">
          <p class="text-[10px] text-stone-500 flex gap-1 tracking-tight">
            <img _ngcontent-ksw-c106="" src="../../../assets/icons/rooms.svg" class="h-3 w-3">
            <span> 2 {{"Bed" | translate}}</span></p>
          <p class="text-[10px] text-stone-500 flex gap-1 tracking-tight">
            <img _ngcontent-ksw-c106="" src="../../../assets/icons/bath-filtered.svg" class="h-3 w-3">
            <span> 2 {{"Bath" | translate}}</span></p>
          <p class="text-[10px] text-stone-500   tracking-tight">750.00 {{"Sq.ft" | translate}}</p>
        </div>
      </div>
    </div>
  </agm-info-window>
  </agm-marker>
  <!-- <agm-marker [latitude]="lat" [longitude]="lng" [markerDraggable]="false" 
      [iconUrl]="iconUrl"></agm-marker> -->
</agm-map>
<!-- (mouseOut)="closeWindow()" -->