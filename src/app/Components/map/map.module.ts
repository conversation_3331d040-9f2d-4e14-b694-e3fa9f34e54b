import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AgmCoreModule } from '@agm/core';
import { MapRoutingModule } from './map-routing.module';
import { MapComponent } from './map.component';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [MapComponent],
  imports: [
    CommonModule,
    TranslateModule,
    MapRoutingModule,
    AgmCoreModule.forRoot({
      apiKey: 'AIzaSyDLZ1XCs4bqpBEUofvA_iB0rPA1DwIfImI',
      libraries: ['places']
    })
  ],exports:[MapComponent]
})
export class MapModule { }
