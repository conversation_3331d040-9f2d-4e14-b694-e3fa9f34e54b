import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ForgotPasswordChangedComponent } from './forgot-password-changed.component';

describe('ForgotPasswordChangedComponent', () => {
  let component: ForgotPasswordChangedComponent;
  let fixture: ComponentFixture<ForgotPasswordChangedComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ForgotPasswordChangedComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ForgotPasswordChangedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
