<div class="bg-overlay fixed inset-0 z-[99]"></div>
<div class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-10 bg-white w-92 rounded">
    <div>
        <div class="h-14">
            <img class="block mx-auto h-full w-full" src="../../../assets/icons/logo-blue.svg" alt="">
        </div>
        <div class="h-32 w-3/4 mx-auto mb-5">
            <img class="block mx-auto h-full w-full object-cover object-top"
                src="../../../assets/images/passwordsave.svg" alt="">
        </div>
        <p class="text-base font-bold text-center">{{"Forgot your password ?"}}</p>
        <p class="text-xs py-4 text-center mb-2">{{"Enter your registeration email id or mobile number to reset the password"}}
        </p>
        <form #otpForm="ngForm">
            <div class="border border-gray-400 flex justify-between items-center p-2 rounded-sm mb-4 relative">
                <input name="resetField" ngModel class="outline-none text-xs w-full"
                    placeholder="Email Address" type="text">
            </div>
            <div class="grid grid-flow-row grid-cols-12 items-center text-xs gap-5">
                <button class="w-full border border-gray-400 hover:bg-gray-100 text-gray-600 hover:text-gray-700 duration-150 py-2 px-1 rounded-sm col-span-5 " (click)="closeModel()">{{"Cancel"}}</button>
                <button class="w-full bg-primary border border-primary hover:bg-blue-900 duration-150 text-white p-2 rounded-sm col-span-7 ">{{"Send recovery link"}}</button>
            </div>
        </form>
    </div>
</div>
<!-- <div
    class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-10 bg-white w-[26rem] rounded-xl">
    <div class="">
        <div class="h-16 w-16 mx-auto">
            <img class="block mx-auto h-full w-full" src="../../../assets/images/finish.webp" alt="">
        </div>
        <p class="text-lg font-bold text-center text-primary">{{"Verification Successful"}}</p>
        <p class="text-xs text-center mb-10">{{"Your account has been succeefully created !!"}}</p>
        <button class="w-full bg-primary hover:bg-blue-900 text-white p-2 rounded-sm" >{{"Take Me To Home"}}</button>
    </div>
</div> -->