import { Component, OnInit } from '@angular/core';
import { configSettings } from 'src/app/Config/config.settings';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent implements OnInit {

  constructor(
    private configSettings: configSettings,

  ) { }

  ngOnInit(): void {

  }

  closeModel() {
    this.configSettings.setResetPassword(false)
  }

}
