import { Component, OnInit } from '@angular/core';
import { configSettings } from './Config/config.settings';
import { TranslateService } from "@ngx-translate/core";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'bander';
  isOTPVerfied: any = true
  resetPassword: any = false
  lang?: any
  direction?: any

  showLoader: boolean = false;

  constructor(
    private configSettings: configSettings,
    private translate: TranslateService,

  ) {
    this.lang = this.configSettings.getLang();
    if (this.lang) {
      translate.use(this.lang);
      if (this.lang === 'en') {
        this.direction = 'ltr'
      } else {
        this.direction = 'rtl'
      }
    } else {
      this.configSettings.setLang('en');
      translate.use('en');
      this.direction = 'ltr'
    }
  }
  ngOnInit(): void {
    this.configSettings.getIsOTPVerified().subscribe(res => {
      // this.isOTPVerfied = res
      // for body scroll
      if(this.isOTPVerfied = res){
        document.body.style.overflow = "auto";
      }else{
        document.body.style.overflow = "hidden";
      }

    })

    this.configSettings.getResetPassword().subscribe(res => {
      this.resetPassword = res
    })
    
    this.configSettings.getShowLoader().subscribe(res => {
      this.showLoader = res
    })
  }

}
