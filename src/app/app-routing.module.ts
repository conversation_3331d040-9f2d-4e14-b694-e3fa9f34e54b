import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './Pages/home/<USER>';
import { LoginComponent } from './Pages/login/login.component';
import { BlogsComponent } from './Pages/blogs/blogs.component';
// import { FavoritesComponent } from './Pages/favorites/favorites.component';
import { ChoosePlanComponent } from './Pages/choose-plan/choose-plan.component';
import { SubscriptionBuyComponent } from './Pages/subscription-buy/subscription-buy.component';
import { PropertyDetailsComponent } from './Pages/property-details/property-details.component';
import { PropertyListingComponent } from './Pages/property-listing/property-listing.component';
import { ArchitectListingComponent } from './Pages/architect-listing/architect-listing.component';
import { AgentProfileComponent } from './Pages/agent-profile/agent-profile.component';
import { ArchitectDetailsComponent } from './Pages/architect-details/architect-details.component';
import { WriteAReviewComponent } from './Components/write-a-review/write-a-review.component';
import { AreaGuidesComponent } from './Pages/area-guides/area-guides.component';
import { BuildingGuidesComponent } from './Pages/building-guides/building-guides.component';
import { BlogDetailsComponent } from './Pages/blog-details/blog-details.component';
import { BuildingDetailsComponent } from './Pages/building-details/building-details.component';

const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  {
    path: 'agent-profile/:id',
    loadChildren: () => import('./Pages/agent-profile/agent-profile.module')
      .then(m => m.AgentProfileModule)
  },
  {
    path: 'agent-details',
    loadChildren: () => import('./Pages/agent-details/agent-details.module')
      .then(m => m.AgentDetailsModule)
  },
  {
    path: 'architect-details',
    loadChildren: () => import('./Pages/architect-details/architect-details.module')
      .then(m => m.ArchitectDetailsModule)
  },
  {
    path: 'architect-listing',
    loadChildren: () => import('./Pages/architect-listing/architect-listing.module')
      .then(m => m.ArchitectListingModule)
  },
  {
    path: 'area-guides',
    loadChildren: () => import('./Pages/area-guides/area-guides.module')
      .then(m => m.AreaGuidesModule)
  },
  {
    path: 'area-guides/:id',
    loadChildren: () => import('./Pages/area-guides/area-guides.module')
      .then(m => m.AreaGuidesModule)
  },
  {
    path: 'blog-details/:id',
    loadChildren: () => import('./Pages/blog-details/blog-details.module')
      .then(m => m.BlogDetailsModule)
  },
  {
    path: 'blogs',
    loadChildren: () => import('./Pages/blogs/blogs.module')
      .then(m => m.BlogsModule)
  },
  {
    path: 'building-details/:id',
    loadChildren: () => import('./Pages/building-details/building-details.module')
      .then(m => m.BuildingDetailsModule)
  },
  {
    path: 'building-guides',
    loadChildren: () => import('./Pages/building-guides/building-guides.module')
      .then(m => m.BuildingGuidesModule)
  },
  {
    path: 'choose-plan/:type',
    loadChildren: () => import('./Pages/choose-plan/choose-plan.module')
      .then(m => m.ChoosePlanModule)
  },
  // {
  //   path: 'favorites',
  //   loadChildren: () => import('./Pages/favorites/favorites.module')
  //     .then(m => m.FavoritesModule)
  // },
  {
    path: 'subscription-buy',
    loadChildren: () => import('./Pages/subscription-buy/subscription-buy.module')
      .then(m => m.SubscriptionBuyModule)
  },
  {
    path: 'home',
    loadChildren: () => import('./Pages/home/<USER>')
      .then(m => m.HomeModule)
  },
  {
    path: 'login',
    loadChildren: () => import('./Pages/login/login.module')
      .then(m => m.LoginModule)
  },

  { path: 'write-a-review', component: WriteAReviewComponent },


  {
    path: 'architect-details/:id',
    loadChildren: () => import('./Pages/architect-details/architect-details.module')
      .then(m => m.ArchitectDetailsModule)
  },

  {
    path: 'faq',
    loadChildren: () => import('./Pages/faq/faq.module')
      .then(m => m.FaqModule)
  },

  {
    path: 'my-account',
    loadChildren: () => import('./Pages/my-account/my-account.module')
      .then(m => m.MyAccountModule)
  },

  {
    path: 'faq-details/:id',
    loadChildren: () => import('./Pages/faq-details/faq-details.module')
      .then(m => m.FaqDetailsModule)
  },

  {
    path: 'choose-plan',
    loadChildren: () => import('./Pages/choose-plan/choose-plan.module')
      .then(m => m.ChoosePlanModule)
  },

  {
    path: 'property-listing',
    loadChildren: () => import('./Pages/property-listing/property-listing.module')
      .then(m => m.PropertyListingModule)
  },
  {
    path: 'property-listing/:propertyFor/:locationID/:categoryType/:min/:max',
    loadChildren: () => import('./Pages/property-listing/property-listing.module')
      .then(m => m.PropertyListingModule)
  },
  {
    path: 'property-listing/:type',
    loadChildren: () => import('./Pages/property-listing/property-listing.module')
      .then(m => m.PropertyListingModule)
  },

  {
    path: 'property-details/:id',
    loadChildren: () => import('./Pages/property-details/property-details.module')
      .then(m => m.PropertyDetailsModule)
  },
  {
    path: 'project-details/:id',
    loadChildren: () => import('./Pages/project-details/project-details.module')
      .then(m => m.ProjectDetailsModule)
  },

  {
    path: 'post-project',
    loadChildren: () => import('./Pages/post-project/post-project.module')
      .then(m => m.PostProjectModule)
  },
  
  {
    path: 'post-project/edit/:id',
    loadChildren: () => import('./Pages/post-project/post-project.module')
      .then(m => m.PostProjectModule)
  },

  {
    path: 'post-property/edit/:id',
    loadChildren: () => import('./Pages/post-property/post-property.module')
      .then(m => m.PostPropertyModule)
  },

  {
    path: 'post-property',
    loadChildren: () => import('./Pages/post-property/post-property.module')
      .then(m => m.PostPropertyModule)
  },

  {
    path: 'registration/:type',
    loadChildren: () => import('./Pages/registration/registration.module')
      .then(m => m.RegistrationModule)
  },
  {
    path: 'confirmation-page',
    loadChildren: () => import('./Pages/confirmation-page/confirmation-page.module')
      .then(m => m.ConfirmationPageModule)
  },
  // {
  //   path: 'feature-my-post',
  //   loadChildren: () => import('./Pages/feature-my-post/feature-my-post.module')
  //     .then(m => m.FeatureMyPostModule)
  // },

  {
    path: ':type', loadChildren: () => import('./Pages/cms/cms.module')
      .then(m => m.CmsModule)
  },




];

@NgModule({
  imports: [RouterModule.forRoot(routes, { scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled', })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
