import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { HomeService } from 'src/app/Services/home.service';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent implements OnInit {
  appStoreLink = 'https://www.apple.com/';
  playStoreLink  = 'https://play.google.com/';

  
  whatsAppNumber:any;
  constructor(

    private homeService: HomeService,
    private toastr: ToastrService,
    private router: Router,
    private configSettings: configSettings,
    private route: ActivatedRoute,



  ) { }

  ngOnInit(): void {
    this.footer()
    
  }
  
 
  

  footerMenus: any

  footer() {

    const getParams = {}
    const posttParams = {}

    this.homeService.footer(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.footerMenus = response.body.data
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }
    

  routeTo(value: string) {
    this.configSettings.setMenuType(value)
    this.router.navigate(['/property-listing']);
  }

  // routeToRS(value: string) {
  //   this.router.navigate(['/property-listing'], { queryParams: { propertyFor: value }, relativeTo: this.route });
  // }

  userDetails: any = this.configSettings.getUserDetails()
}
    


