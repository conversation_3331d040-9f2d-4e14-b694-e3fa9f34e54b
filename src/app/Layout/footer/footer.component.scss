.footer {
  @apply bg-gradient-to-r text-white bg-primary  tracking-tight;
  &-container {
    @apply container mx-auto;
  }
  &-sitemap {
    @apply grid grid-cols-1 gap-5 md:grid-cols-2 lg:grid-cols-3;
    &-header {
      @apply text-3xl mb-5 capitalize tracking-wide text-gray-200 font-normal;
    }
    &-link {
      @apply leading-6  hover:text-accent duration-200 text-white opacity-90;
      &-list {
        @apply  grid  grid-rows-5 grid-flow-col mb-5;
      }
    }
  }
  &-horizontal-nav {
    @apply flex justify-center  flex-wrap;
    &-link {
      @apply uppercase  hover:text-accent duration-200;
    }
  }
}


@media only screen and (max-width: 767px){
  .footer-sitemap-header {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}
}