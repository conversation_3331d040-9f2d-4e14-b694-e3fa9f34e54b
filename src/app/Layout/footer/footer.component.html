<footer *ngIf="footerMenus" class="footer text ">
    <div *ngIf="footerMenus?.length === 0" class="pt-28 footer-container text-center rtl:md:text-right md:text-left  px-12 sm:px-14 md:px-24 pb-16">
        <div class="footer-sitemap">
            <div *ngFor="let footerMenu of footerMenus">
                <h2 class="footer-sitemap-header rtl:2xl:w-11/12">
                    {{footerMenu.name}}
                </h2>
                <ul class="footer-sitemap-link-list text-sm text-[#a0adbb]">
                    <li class="pb-2 " (click)="routeTo(link.id)" *ngFor="let link of footerMenu.children"><a
                            class="footer-sitemap-link cursor-pointer">{{link.name}}</a></li>

                </ul>
            </div>
        </div>
    </div>
    <!-- for top border -->
    <div class="border-[#ffffff5b] border border-x-0 border-b-0 text-sm text-white ">
        <div class="footer-container py-16">
            <div class="flex flex-wrap justify-around px-[5.5rem] gap-5 mb-12">
                <a [routerLink]="['/home']" routerLinkActive="router-link-active"
                    class=" footer-horizontal-nav-link ">{{'Home' | translate}}</a>
                <a class=" footer-horizontal-nav-link cursor-pointer" [routerLink]="'property-listing'"
                    [queryParams]="{propertyFor: 'S'}">{{'RESALE' |
                    translate}}</a>
                <a class=" footer-horizontal-nav-link cursor-pointer" [routerLink]="'property-listing'" [queryParams]="{propertyFor: 'R'}">{{'Rent' |
                    translate}}</a>
                <a *ngIf="userDetails" [routerLink]="['my-account/saved-searches']"
                    class=" footer-horizontal-nav-link">{{'saved searches' |
                    translate}}</a>
                <a [routerLink]="['/faq']" routerLinkActive="router-link-active"
                    class=" footer-horizontal-nav-link">{{'faq' | translate}}</a>
                <a [routerLink]="['/about-us']" class=" footer-horizontal-nav-link">{{'about us' | translate}}</a>
                <a [routerLink]="['/terms-and-condition']" class=" footer-horizontal-nav-link">{{'terms & conditions' |
                    translate}}</a>
                <a [routerLink]="['/privacy-policy']" class=" footer-horizontal-nav-link">{{'privacy policy' |
                    translate}}</a>
            </div>
            <div class=" footer-horizontal-nav items-center gap-5 mb-9">
                <a target="_blank" href="https://wa.me/{{footerMenus.settings.support_phone}}" class=" footer-horizontal-nav-link"><img
                        class="w-6 h-6" src="../../../assets/icons/whatsapp-icon-white.png" alt=""></a>
                <a href="https://{{footerMenus.settings.fb_url}}" target="_blank" class=" footer-horizontal-nav-link"><img class="w-6 h-6"
                        src="../../../assets/icons/facebook-icon.svg" alt=""></a>
                <a href="https://{{footerMenus.settings.instagram_url }}" target="_blank" class=" footer-horizontal-nav-link"><img
                        class="w-6 h-6" src="../../../assets/icons/instagram-icon.svg" alt=""></a>
                <a href="https://{{footerMenus.settings.twitter_url }}" target="_blank" class=" footer-horizontal-nav-link"><img class="w-6 h-6"
                        src="../../../assets/icons/twitter-icon.svg" alt=""></a>
            </div>
            <div [dir]="'ltr'" class="footer-horizontal-nav items-center gap-3">
                <a href="tel:{{footerMenus.settings.support_phone}}" class=" footer-horizontal-nav-link"><img src="../../../assets/icons/phone-icon.svg"
                        class="h-8 w-8" alt=""></a>
                <a href="tel:{{footerMenus.settings.support_phone}}"
                    class=" leading-6 hover:text-accent duration-200 text-white opacity-90 text-2xl font-normal tracking-[.75px]">{{footerMenus.settings.support_phone}}</a>
            </div>
        </div>
    </div>
    <div class="border border-[#ffffff5b] border-x-0 border-b-0 ">
        <div class=" py-12 mx-auto container sm:px-14 md:px-24">
            <div class="grid grid-cols-1 lg:grid-cols-3  items-center gap-5 ">
                <div class="row-start-3 lg:row-start-1">
                    <p
                        class="text-center lg:text-left text-xs sm:text-sm text-[#bfc8d1] opacity-80 font-normal cursor-default">
                        {{'Copyright ©2023 Bandar Real Estate Brokerage Co. All rights reserved' | translate}}.
                    </p>
                </div>
                <div class=" flex items-center justify-center row-start-1">
                    
                    <a href="{{playStoreLink}}" class="d-inline-block " target="_blank">
                        <img src="../../../assets/icons/google-play.png" class="w-36 h-[3.5rem] cursor-pointer">
                    </a>
                    <a href="{{appStoreLink}}" class="d-inline-block" target="_blank">
                        <img src="../../../assets/icons/apple-store.svg" class="w-36 h-[3.5rem] cursor-pointer">
                    </a>
                </div>
                <div class="row-start-2 lg:row-start-1">
                    <p
                        class="flex items-center justify-center xl:flex xl:items-center xl:justify-end text-xs sm:text-sm text-[#bfc8d1] opacity-80 font-normal cursor-default text-right rtl:text-left lg:text-right hover:cursor-pointer hover:text-accent duration-150 transform">
                        <a target="_blank" href="https://izarsolutions.com/">{{'Site By Izar Solutions'|translate}}</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

</footer>