import { Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { CheckUserSubscriptionsService } from 'src/app/Services/Check-user-subscriptions/check-user-subscriptions.service';
import { UserService } from 'src/app/Services/User/user.service';
import { Subscription } from 'rxjs';
import { NotificationService } from 'src/app/Services/Notifications/notification.service';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  host: {
    '(document:click)': 'closeDropDowns($event)',
  },
})
export class HeaderComponent implements OnInit {

  lang?= this.configSettings.getLang();
  @ViewChild('menu') menu: ElementRef<HTMLInputElement> | undefined;
  @ViewChild('guideDropdown') guideDropdownRef: ElementRef;
  @ViewChild('resourcesDropdown') resourcesDropdownRef: ElementRef;
  @ViewChild('notificationBox') notificationBoxRef: ElementRef;

  constructor(
    private configSettings: configSettings,
    private toastr: ToastrService,
    public router: Router,
    private route: ActivatedRoute,
    private renderer: Renderer2,
    private userService: UserService,
    private checkUserSubscriptionsService: CheckUserSubscriptionsService,
    private notificationService: NotificationService,
    private translateService: TranslateService,
  ) { }
  // firstName:string= this.configSettings.getUserDetails().first_name
  // lastName:string= this.configSettings.getUserDetails().last_name
  // profilePImg:string= this.configSettings.getUserDetails().profile_img

  isUserLoggedIn?: any
  userDetails: any
  isLoggedIn: any = false
  userTypes: any = []

  postPropertyBtn: boolean = false
  postProjectBtn: boolean = false
  $userTypeObserver!: Subscription;
  notificationList: any
  notificationIcon: any;

  userID: any

  firstName: string
  lastName: string
  profileImg: string

  isOwner: any = false
  isArchitect: any = false
  isAgent: any = false

  ngOnInit(): void {
    this.userID = this.configSettings.getUserID()
    this.configSettings.getUserTypes().subscribe(res => {
      this.userDetails = this.configSettings.getUserDetails()
      this.firstName = this.userDetails?.first_name
      this.lastName = this.userDetails?.last_name
      this.profileImg = this.userDetails?.profile_img
      if (this.userDetails) {
        this.userDetails?.user_types.forEach((el: any) => {

          this.userTypes = this.userDetails.user_types

          //@bob this has been added as per request up to u want to keep in the flow or remove it 
          if (el.id == 3 || el.id == 1) {
            this.postPropertyBtn = true
          }
          if (el.id == 4) {
            this.postProjectBtn = true
            // this.postPropertyBtn = false
          }
          // this.userTypes.push(el.name)
        })
      } else {
        this.postPropertyBtn = false
        this.postProjectBtn = false


        // this.userTypes = []
      }

  


    })

    this.userService.isUserLoggedIn.subscribe((res: any) => {
      this.isLoggedIn = res
    })

    this.userDetails != undefined ? this.isLoggedIn = true : this.isLoggedIn = false


    this.configSettings.getIsUserLoggedIn().subscribe(res => {
      this.isUserLoggedIn = res
      this.isLoggedIn = res      
    })
    this.isUserLogged()

    this.configSettings.tiggerNotificationAPi()

    this.configSettings.callNotificationAPi().subscribe(res => {
      this.getNotifications()
    })
if(this.userDetails){
  this.userDetails.user_types.forEach((el: any) => {
    this.userTypes.push(el.name)
  })
}
    

    this.isOwner = this.userTypes.includes("Owner")
    this.isArchitect = this.userTypes.includes("Architect")
    this.isAgent = this.userTypes.includes("Agent")
  }

  getNotifications() {
    if (this.isLoggedIn) {
       const getParams = {
      user_id: this.configSettings.getUserID(),
      per_page: 9999
    }

    this.notificationService.getNotificationsApi(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.notificationList = response.body.data.notification_list
            this.configSettings.setNotificationCount(response.body.data.unreadCount)
            // let a =response?.data.notification_list.length
            // if (a>0) {
            //   return this.notificationIcon;false
            // }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }
}

  isUserLogged() {
    if (this.isUserLoggedIn) {
      if (this.configSettings.getUserDetails().is_phone_verified === false) {
        this.configSettings.setIsOTPVerified(false)
      }
    }
  }

  changeLanguage() {
    this.configSettings.setLang((this.lang === 'ar') ? 'en' : 'ar');
    window.location.reload();
  }

  showOverlay: boolean = false

  openMenu() {
    if (this.lang == 'en') {
      this.renderer.removeClass(this.menu?.nativeElement, '-translate-x-full')
    } else {
      this.renderer.removeClass(this.menu?.nativeElement, 'rtl:translate-x-full')
      this.renderer.removeClass(this.menu?.nativeElement, '-translate-x-full')
      this.renderer.removeClass(this.menu?.nativeElement, 'rtl:hidden')
    }
    this.showOverlay = true
    document.body.style.overflow = "hidden"
  }

  closeMenu() {
    if (this.lang == 'en') {
      this.renderer.addClass(this.menu?.nativeElement, '-translate-x-full')
    } else {
      this.renderer.addClass(this.menu?.nativeElement, 'rtl:translate-x-full')
      this.renderer.addClass(this.menu?.nativeElement, 'rtl:hidden')
    }
    this.showOverlay = false
    document.body.style.overflow = "auto"
  }

  isGuideDropdownOpen: boolean = false
  dropDown() {
    this.isGuideDropdownOpen = !this.isGuideDropdownOpen
  }


  routeTo(value: string) {
    this.router.navigate(['/property-listing'], { queryParams: { propertyFor: value }, relativeTo: this.route });
  }


  post(postType: string, userTypeID: number) {
    // let userType = this.configSettings.getUserType();
    // let userTypeId: number = 0
    // if (userType == 'UR') {
    //   userTypeId = 1;
    // }
    // else if (userType == 'AG') {
    //   userTypeId = 3;
    // }
    // else if (userType == 'AR') {
    //   userTypeId = 4;
    // }
    this.configSettings.setShowLoader(true);
    if (this.isUserLoggedIn === true) {

      const getParams = {
        id: this.configSettings.getUserID(),
        user_type_id: userTypeID
      }

      this.checkUserSubscriptionsService.checkSubscriptionValidity(getParams).subscribe({
        next: (response) => {
          if (response.status === 200) {
            if (response.body.status === 200) {
              this.configSettings.setShowLoader(false);

              if (postType == "PT") {
                if (response.body.data.remaining_count < 1) {

                  if (response.body.data.is_new_user == true) {
                    // this.toastr.warning('', 'Buy a plan to proceed')
                    this.translateService.get('Buy a plan to proceed').subscribe(res => {
                      this.toastr.warning('', res);
                    })
                  } else {
                    // this.toastr.warning('', 'Your plan is expired')
                    this.translateService.get('Your plan is expired').subscribe(res => {
                      this.toastr.warning('', res);
                    })
                  }

                  if (userTypeID === 1) {
                    this.router.navigate(['/choose-plan/UR'])
                  } else if (userTypeID === 3) {
                    this.router.navigate(['/choose-plan/AG'])
                  }
                } else {
                  localStorage.setItem('maxPhotos', JSON.stringify(response.body.data.max_photos_per_post));
                  this.router.navigate(['/post-property'])
                }
              }

              if (postType == "PR") {
                if (response.body.data.remaining_projects < 1) {


                  if (response.body.data.is_new_user == true) {
                    // this.toastr.warning('', 'Buy a plan to proceed')
                    this.translateService.get('Buy a plan to proceed').subscribe(res => {
                      this.toastr.warning('', res);
                    })
                  } else {
                    // this.toastr.warning('', 'Your plan is expired')
                    this.translateService.get('Buy a plan for Post Project').subscribe(res => {
                      this.toastr.warning('', res);
                    })
                  }


                  this.router.navigate(['/choose-plan/AR'])


                } else {
                  localStorage.setItem('maxPhotos', JSON.stringify(response.body.data.max_photos_per_post));
                  this.router.navigate(['/post-project'])
                }
              }


            } else {
              this.router.navigate(['/choose-plan/UR'])
              this.configSettings.setShowLoader(false);

            }
          }
        },
        error: (err) => {
          this.toastr.error('', err.error.message);
          this.configSettings.setShowLoader(false);

        }
      })

    } else {
      this.router.navigate(['/login'])
      this.configSettings.setShowLoader(false);
    }
  }

  isNotificationBoxOpen: boolean = false
  toggleNotificationBox() {
    this.isNotificationBoxOpen = !this.isNotificationBoxOpen
  }

  isResourcesDropdownOpen: boolean = false
  closeDropDowns(event: any) {
    event.stopPropagation()
    if (!this.guideDropdownRef.nativeElement.contains(event.target)) {
      this.isGuideDropdownOpen = false
    }
    if (!this.resourcesDropdownRef.nativeElement.contains(event.target)) {
      this.isResourcesDropdownOpen = false
    }
    if (!this.notificationBoxRef?.nativeElement.contains(event.target)) {
      this.isNotificationBoxOpen = false
    }
  }

  markAsSeen(notificationID: number, type: string) {

    let getParams = {
      user_id: this.userID,
      // notification_id: notificationID
    }

    if (type === 'one') {
      Object.assign(getParams, { notification_id: notificationID })
    }

    if (type === 'all') {
      Object.assign(getParams, { mark_all: 1 })
    }

    this.notificationService.markAsSeen(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.getNotifications()
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  logout() {

    localStorage.removeItem('userDetails')
    this.configSettings.setIsUserLoggedIn(false)
    this.userService.setUser(false)
    this.configSettings.setUserTypes()
    // this.router.navigate(['/home'])
    this.router.navigate(['/home']).then(() => { window.location.reload() })
    this.closeMenu();
  }
}

