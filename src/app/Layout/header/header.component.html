<header class=" bg-primary text-gray-100 text-sm border-b border-borderColor ">
    <div class="border-b border-borderColor">
        <div class=" container mx-auto sm:px-14 md:px-24">
            <div id="sub-nav" class="hidden lg:flex lg:justify-end py-4 gap-2 ">
                <div *ngIf="lang === 'en'" class="flex gap-1 cursor-pointer" (click)="changeLanguage()">
                    <img src="../../../assets/icons/translate-icon.svg" class="w-5 h-5" alt="">
                    <button class="bg-none outline-none border-none shadow-none ">{{"العربية"}}</button>
                </div>
                <div *ngIf="lang === 'ar'" class="flex gap-1 cursor-pointer" (click)="changeLanguage()">
                    <img src="../../../assets/icons/translate-icon.svg" class="w-5 h-5" alt="">
                    <button class="bg-none outline-none border-none shadow-none ">{{"English"}}</button>
                </div>
            </div>
        </div>
    </div>
    <div class="container mx-auto px-4 sm:px-14 md:px-24">

        <div
            class="flex flex-wrap justify-between  xl:justify-start items-center py-3 gap-2 md:gap-4 lg:gap-[0.8rem] xl:gap-8">
            <div class="lg:hidden  cursor-pointer" (click)="openMenu()">
                <img src="../../../assets/icons/menu.png" class=" w-7">
            </div>
            <div class="text-2xl xl:flex-grow  cursor-pointer">
                <img class="w-28" src="assets/images/logov3.png" alt="" [routerLink]="['/home']"
                    routerLinkActive="active">
            </div>
            <ul class="lg:flex hidden gap-2 lg:gap-3 rtl:gap-4 xl:gap-6">
                <li class="cursor-pointer" [routerLink]="'property-listing'" [queryParams]="{propertyFor: 'S'}"
                    [routerLinkActive]="['active']">
                    <!-- routes to property listing for now -->
                    <a class="hover:text-accent duration-150 rtl:text-base lg:text-[13px] xl:text-base uppercase">{{"RESALE"
                        | translate}}</a>
                </li>
                <li [routerLink]="'property-listing'" [queryParams]="{propertyFor: 'R'}" [routerLinkActive]="['active']"
                    class="cursor-pointer" routerLinkActive="active">
                    <!-- routes to property listing for now -->
                    <a class="hover:text-accent duration-150 rtl:text-base lg:text-[13px] xl:text-base uppercase">{{"Rent"
                        | translate}}</a>
                </li>
                <li *ngIf="userDetails" [routerLink]="['/my-account/saved-searches/']" routerLinkActive="active"
                    class="cursor-pointer">
                    <a class="hover:text-accent rtl:text-base text-[13px] duration-150 xl:text-base uppercase">
                        {{"Saved Searches" | translate}}</a>
                </li>
                <!-- <li class="cursor-pointer">
                    <a class="hover:text-accent duration-150">{{"RESOURCES"}}</a>
                </li> -->
                <li [routerLink]="['/blogs']" routerLinkActive="active" class="cursor-pointer">
                    <a class="hover:text-accent duration-150 rtl:text-base lg:text-[13px] xl:text-base uppercase">{{"Blogs"
                        | translate}}</a>
                </li>
                <li class="cursor-pointer">
                    <div #guideDropdown class="guides-dropdown drop-down cursor-pointer relative" (click)="dropDown()">
                        <div id="main" class=" outline-none   placeholder:text-lg  relative flex items-center gap-1">
                            <span
                                class="uppercase rtl:text-base hover:text-accent lg:text-[13px] xl:text-base">{{"Guides"
                                | translate}}</span>
                            <img [ngClass]="(isGuideDropdownOpen === true)?'-rotate-180':''"
                                class="transition duration-200 h-2 w-2 "
                                src="../../../assets/icons/triangle-dropdown.svg">
                        </div>
                        <div *ngIf="isGuideDropdownOpen"
                            class="absolute right-0 mt-1 bg-white min-w-[10rem] w-full  shadow-md rounded-md overflow-hidden text-black py-2">
                            <ul>
                                <li [routerLink]="['/area-guides']" [routerLinkActive]="['active']"
                                    class="hover:underline  px-4 py-2">{{"Area Guides" |
                                    translate}}</li>
                                <li [routerLink]="['/building-guides']" [routerLinkActive]="['active']"
                                    class="hover:underline px-4 py-2">
                                    {{"Building Guides" | translate}}</li>
                                <!-- <li [routerLink]="['/my-account/my-architect-profile']" class="hover:underline  px-4 py-2"> {{"Find-Architect"}}</li> -->
                            </ul>
                        </div>

                    </div>
                </li>
                <li class="cursor-pointer">
                    <div #resourcesDropdown class="guides-dropdown drop-down cursor-pointer relative"
                        (click)="isResourcesDropdownOpen = !isResourcesDropdownOpen">
                        <div id="main" class=" outline-none   placeholder:text-lg  relative flex items-center gap-1">
                            <span
                                class="uppercase rtl:text-base hover:text-accent lg:text-[13px] xl:text-base">{{"Resources"
                                | translate}}</span>
                            <img [ngClass]="(isResourcesDropdownOpen === true)?'-rotate-180':''"
                                class="transition duration-200 h-2 w-2 "
                                src="../../../assets/icons/triangle-dropdown.svg">
                        </div>
                        <div *ngIf="isResourcesDropdownOpen"
                            class="absolute right-0 mt-1 bg-white rtl:min-w-[11rem] min-w-[10rem] w-full  shadow-md rounded-md overflow-hidden text-black py-2">
                            <ul>
                                <li [routerLink]="['/architect-listing']" routerLinkActive="active"
                                    [queryParams]="{page: '1'}" class="hover:underline   px-4 py-2">{{"Architects" |
                                    translate}}</li>
                                <!-- <li class="hover:underline px-4 py-2">
                                    {{"Agents" | translate}}</li> -->
                            </ul>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="lg:flex items-center hidden gap-2 rtl:gap-3 lg:gap-[0.45rem] xl:gap-3">
                <!-- *ngIf="userTypes.length > 1"  -->
                <button *ngIf="postPropertyBtn == true && userTypes.length === 1" (click)="post('PT',1)"
                    class="text-accent duration-150 border-accent hover:bg-accent hover:text-white  border py-1 xl:py-2 px-4  lg:px-2 xl:px-4">
                    {{"Post Property" | translate}}
                </button>
                <button *ngIf="postPropertyBtn == true && userTypes.length > 1" (click)="post('PT',3)"
                    class="text-accent duration-150 border-accent hover:bg-accent hover:text-white  border py-1 xl:py-2 px-4 lg:px-2 xl:px-4">
                    {{"Post Property" | translate}}
                </button>
                <button *ngIf="postProjectBtn == true" (click)="post('PR',4)"
                    class="text-accent duration-150 border-accent hover:bg-accent hover:text-white  border py-1 xl:py-2 px-4 lg:px-2 xl:px-4">
                    {{"Post Project" | translate}}
                </button>
                <!-- Notification button -->
                <div *ngIf="isLoggedIn" #notificationBox (click)="isNotificationBoxOpen = !isNotificationBoxOpen"
                    class=" p-1 lg:p-0 xl:p-1 relative">
                    <img src="../../../assets/icons/bell.svg" class="w-6 aspect-square cursor-pointer">
                    <span *ngIf="notificationList" class="flex items-center justify-center absolute lg:top-[3px] xl:top-2 rtl:lg:left-[3px] rtl:xl:left-2 rtl:right-[unset] lg:right-[3px] xl:right-2 bg-accent border border-white
                     w-2 h-2 text-white rounded-full text-xs"></span>

                    <span *ngIf="!notificationList"
                        class="flex items-center justify-center absolute lg:top-[3px] xl:top-2 rtl:lg:left-[3px] rtl:xl:left-2 rtl:right-[unset] lg:right-[3px] xl:right-2 text-xs"></span>
                    <!-- [ngClass]="isNotificationBoxOpen == true ? 'block' : 'hidden'" -->


                    <div (click)="$event.stopPropagation()" *ngIf="isNotificationBoxOpen"
                        class="absolute h-94 w-72 rtl:left-0 rtl:right-[unset] right-0  rounded-md mt-3 py-4 flex flex-col items-stretch border-2 border-accent bg-white shadow-lg">
                        <div class="flex justify-between items-center sticky top-0 mb-2 px-4">
                            <h1 class="text-black text-xs font-bold">{{"Notifications" | translate}}</h1>
                            <span class="text-red-600 text-[.7rem] tracking-tighter cursor-pointer hover:text-red-700"
                                (click)="markAsSeen(0, 'all')">
                                {{"Mark all as read" | translate}}</span>
                        </div>
                        <div class="flex-grow overflow-y-scroll no-scrollbar ">
                            <div *ngFor="let notification of notificationList"
                                class="text-black flex justify-between items-center mb-1 py-3 px-4 odd:bg-[#fcf8ed] even:bg-[#fcf3db]">
                                <div class="flex items-start  gap-2">
                                    <img src="../../../assets/icons/bell-blue.svg"
                                        class="w-5 aspect-square border rounded-full">
                                    <div class="flex flex-col gap-1">
                                        <h1 class="text-xs text-primary font-semibold">{{notification.title}}</h1>
                                        <p class="text-[.7rem] leading-3 text-mediumGray">{{notification.message}}
                                        </p>
                                    </div>
                                </div>
                                <img (click)="markAsSeen(notification.id, 'one')" src="../../../assets/icons/tick.svg"
                                    title="mark as seen" class="cursor-pointer w-5 aspect-square">
                            </div>
                            <div *ngIf="!notificationList" class="text-black flex justify-center items-center h-full">
                                {{"You have no notifications" | translate}}
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <button *ngIf="isLoggedIn === false" [routerLink]="['/login']"
                        class="hover:text-accent duration-150 flex items-center hover:border-accent border py-2 px-4 lg:px-2.5 xl:px-4">
                        <img src="../../../assets/icons/avatar.png" class="h-7 w-7">
                        <span>{{"LOGIN" | translate}}</span>
                    </button>
                    <div *ngIf="isLoggedIn" class="h-full w-32 lg:w-24 xl:w-32">
                        <app-profile-dropdown></app-profile-dropdown>
                    </div>
                </div>
            </div>
            <div id="sub-nav" class="lg:hidden flex lg:justify-end py-4 gap-2">
                <img src="../../../assets/icons/translate-icon.svg" class="w-5 h-5" alt="">
                <button *ngIf="lang === 'en'" class="bg-none outline-none border-none shadow-none "
                    (click)="changeLanguage()">{{"العربية"}}</button>
                <button *ngIf="lang === 'ar'" class="bg-none outline-none border-none shadow-none "
                    (click)="changeLanguage()">{{"English"}}</button>
            </div>
        </div>
    </div>

    <!-- Mobile side menu -->
    <!--  -->
    <div (click)="closeMenu()" *ngIf="showOverlay" class="fixed inset-0 w-full h-full z-[150]"></div>
    <div #menu clickedOutside
        class="rtl:hidden  lg:hidden w-96 h-vh100 bg-primary absolute top-0 bottom-0 flex flex-col items-stretch rtl:right-0 rtl:left-[unset] ltr:right-[unset] left-0 z-[151] -translate-x-full rtl:translate-x-full  transform-gpu duration-200"
        (click)="closeMenu()">
        <div class="basis-[73px] flex-grow-0 flex-shrink-0 flex items-center justify-between px-9 shadow-lg"
            (click)="closeMenu()">
            <img class="w-28" [routerLink]="['/home']" src="assets/images/logov3.png" alt="">
            <img src="../../../assets/icons/close-button.svg" class="h-5 w-5 cursor-pointer">
        </div>
        <div class="flex-grow h-[120%] overflow-y-scroll pb-20">
            <div *ngIf="isLoggedIn === false"
                class="h-24 bg-primary bg-opacity-0 text-black   mx-9 flex items-center gap-6 font-normal">
                <!-- <img class="h-8 w-8 shadow rounded-full"
                    src="../../../assets/images/boy-phone.png" alt=""> -->
                <!-- <app-profile-dropdown></app-profile-dropdown> -->
                <button *ngIf="isLoggedIn === false" [routerLink]="['/login']"
                    class=" duration-150 flex items-center gap-2 bg-accent rounded py-3 px-4" (click)="closeMenu()">
                    <span>{{"LOGIN / REGISTER" | translate}}</span>
                </button>
                <div class="flex flex-col gap-1 ">

                    <!-- <p class="text-white">{{firstName + ' ' + lastName}}</p> -->

                    <!-- <p [routerLink]="['/my-account/my-profile']" fragment="edit" class="text-accent text-xs">{{"View/Edit profile" | translate}}</p> -->
                </div>
            </div>
            <div *ngIf="isLoggedIn === true"
                class="h-24 bg-primary bg-opacity-5 text-black  mx-9 flex items-center gap-6 font-normal">
                <!-- <app-profile-dropdown></app-profile-dropdown> -->
                <img class="h-8 w-8 shadow rounded-full" [src]="profileImg" alt="">
                <!-- <app-profile-dropdown></app-profile-dropdown> -->
                <div class="flex flex-col gap-1 ">
                    <!-- <app-profile-dropdown></app-profile-dropdown>s -->
                    <p class="text-white text-lg">{{firstName + ' ' + lastName}}</p>
                    <!-- <p class="text-white">{{firstName + ' ' + lastName}}</p> -->

                    <p [routerLink]="['/my-account/my-profile']" fragment="edit"
                        class="text-accent text-xs cursor-pointer">{{"View/Edit profile" | translate}}</p>
                </div>
            </div>
            <ul class="flex flex-col gap-5 mb-5 text-sm font-normal px-9">
                <li>

                </li>
                <!-- <div *ngIf="isUserLoggedIn === true">
                    <p [routerLink]="['/my-account/my-profile']" fragment="edit" class="text-accent text-xs" (click)="closeMenu()">{{"View/Edit profile" | translate}}</p>
                </div> -->
                <li [routerLink]="['/home']" [routerLinkActive]="['active']" class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"HOME" | translate}}</a>
                </li>


                <li [routerLink]="'property-listing'" [queryParams]="{propertyFor: 'S'}" [routerLinkActive]="['active']"
                    class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"RESALE" | translate}}</a>
                </li>
                <li [routerLink]="'property-listing'" [queryParams]="{propertyFor: 'R'}" [routerLinkActive]="['active']"
                    class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"RENT" | translate}}</a>
                </li>
                <li [routerLink]="'architect-listing'" [queryParams]="{propertyFor: 'R'}"
                    [routerLinkActive]="['active']" class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"ARCHITECTS" | translate}}</a>
                </li>

                <!-- <li class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150">{{"RESOURCES"}}</a>
                </li> -->
                <li [routerLink]="['/blogs']" [routerLinkActive]="['active']" class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"BLOGS" | translate}}</a>
                </li>
                <li [routerLink]="['/area-guides']" [routerLinkActive]="['active']" class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"AREA GUIDES" | translate}}</a>
                </li>


                <li [routerLink]="['/building-guides']" [routerLinkActive]="['active']"
                    class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"BUILDING GUIDES" |
                        translate}}</a>
                </li>
                <li *ngIf="postPropertyBtn == true && userTypes.length === 1" (click)="post('PT',1)"
                    class="cursor-pointer text-center bg-accent border-accent border rounded-md py-3  ">
                    <a class="hover:text-white duration-150" (click)="closeMenu()">{{"POST PROPERTY" | translate}}</a>
                </li>
                <li *ngIf="postPropertyBtn == true && userTypes.length > 1" (click)="post('PT',3)"
                    class="cursor-pointer text-center bg-accent border-accent border rounded-md py-3">
                    <a class="hover:text-white duration-150" (click)="closeMenu()">{{"POST PROPERTY" | translate}}</a>
                </li>
                <li *ngIf="postProjectBtn == true " (click)="post('PR',4)"
                    class="cursor-pointer text-center bg-accent border-accent border rounded-md py-3">
                    <a class="hover:text-white duration-150" (click)="closeMenu()">{{"POST PROJECT" | translate}}</a>
                </li>
                <p *ngIf="isUserLoggedIn === true" class=" border-b border-gray-400 text-gray-400 w-full text-lg pb-3">{{'My Account'|translate}}</p>

                <li *ngIf="isUserLoggedIn === true" class="cursor-pointer text-start" [routerLink]="['/my-account/my-profile']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY PROFILE" | translate}}</a>
                </li>

                <li *ngIf="isAgent === true"  class="cursor-pointer text-start" [routerLink]="['/my-account/agent-profile']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY AGENT PROFILE" | translate}}</a>
                </li>

                <li  *ngIf="isUserLoggedIn === true"  class="cursor-pointer text-start" [routerLink]="['/my-account/my-property-listing']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY PROPERTY" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true && isArchitect === true"  class="cursor-pointer text-start" [routerLink]="['/my-account/architech-profile']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY ARCHITECH PROFILE" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true &&  isArchitect === true"  class="cursor-pointer text-start" [routerLink]="['/my-account/my-projects']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY PROJECTS" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true && isOwner === true" class="cursor-pointer text-start" [routerLink]="['/my-account/company-profile']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY COMPANY PROFILE" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true" class="cursor-pointer text-start" [routerLink]="['/my-account/chats']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY CHATS" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true" class="cursor-pointer text-start" [routerLink]="['/my-account/my-stories']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY STORIES" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true" class="cursor-pointer text-start" [routerLink]="['/my-account/my-plans']" [routerLinkActive]="['active']">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()" >{{"MY PLANS" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true && userDetails" [routerLink]="['/my-account/saved-searches/']" routerLinkActive="active"
                    class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"SAVED SEARCHES" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true && userDetails" [routerLink]="['/my-account/favorites/']" routerLinkActive="active"
                    class="cursor-pointer text-start">
                    <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"FAVORITES" | translate}}</a>
                </li>

                <li *ngIf="isUserLoggedIn === true" class="cursor-pointer text-start">
                    <a [routerLink]="['/my-account/settings']" class="hover:text-accent duration-150"
                        (click)="closeMenu()" [routerLinkActive]="['active']">{{"SETTINGS" | translate}}</a>
                </li>
                <!-- <li *ngIf="userDetails" [routerLink]="['/my-account/settings/']" [routerLinkActive]="['active']"
                class="cursor-pointer text-start">
                <a class="hover:text-accent duration-150" (click)="closeMenu()">{{"SETTINGS" | translate}}</a>
            </li> -->

                

                <li *ngIf="isUserLoggedIn === true" [routerLink]="['/my-account/settings']"
                    class="cursor-pointer text-start" >
                    <a class="hover:text-accent duration-150" (click)="logout()">{{"LOG OUT" | translate}}</a>
                </li>


            </ul>

        </div>
    </div>
</header>