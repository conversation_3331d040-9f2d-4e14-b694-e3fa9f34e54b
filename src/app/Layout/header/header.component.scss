.header {
  @apply bg-primary text-gray-100 text-sm;
  &-container {
    @apply px-5  container mx-auto;
  }
}

.overlay {
  background: linear-gradient(180deg, rgba(0, 35, 73, 0.4) 0%, rgba(0, 35, 73, 0.4) 100%);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.active{
 color:rgb(229,183,75);

}