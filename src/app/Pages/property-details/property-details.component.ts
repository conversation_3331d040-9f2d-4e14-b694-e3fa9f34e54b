import { Component, ElementRef, Input, OnInit, Renderer2, ViewChild, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { FavoritesService } from 'src/app/Services/Favorites/favorites.service';
import { PropertyListingService } from 'src/app/Services/Properties/property-listing.service';
import { TranslateService } from '@ngx-translate/core';

import SwiperCore, { Autoplay, Pagination, Scrollbar, SwiperOptions, Navigation, Thumbs, FreeMode } from 'swiper';
import { SwiperComponent } from 'swiper/angular';
SwiperCore.use([Scrollbar, Autoplay, Pagination, Navigation, Thumbs, FreeMode]);

@Component({
  selector: 'app-property-details',
  templateUrl: './property-details.component.html',
  styleUrls: ['./property-details.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PropertyDetailsComponent implements OnInit {

  constructor(
    private toastr: ToastrService,
    private propertyListingService: PropertyListingService,
    private route: ActivatedRoute,
    private router: Router,
    private configSettings: configSettings,
    private favoritesService: FavoritesService,
    private translateService: TranslateService,


  ) { }

  userDetails: any;
  is_fav: any
  @Input() property_for: any


  showSearch: false
  bannerImg: any
  galleryArr: any

  lat: any
  lng: any

  @ViewChild('desc') desc: ElementRef<HTMLElement>

  propertyId: number = 0
  // thumbnail: string = ''
  lang: string = ''
  showMap: boolean = false
  showGallery: boolean = false
  similarPorperties: any

  ngOnInit(): void {
    this.userDetails = this.configSettings.getUserDetails()?.user_id == undefined ? '' : this.configSettings.getUserDetails().user_id
    this.configSettings.setShowLoader(true)
    this.route.params.subscribe(params => {

      this.propertyId = params['id']

    })

    // this.favorite()
    this.lang = this.configSettings.getLang()
    this.getPropertyDetails()
  }


  details: any
  property_details: any
  property_amenities: any
  PropertyDetails: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: true,
    navSpeed: 700,
    autoWidth: false,
    margin: 25,
    center: false,
    responsive: {
      0: {
        items: 2
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 3
      },
      1280: {
        items: 3
      },
      1536: {
        items: 3
      }
    },
    nav: false
  }
  youMayAlsoLike: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    autoWidth: true,
    margin: 10,
    center: false,
    responsive: {
      0: {
        items: 2
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 5
      },
      1280: {
        items: 5
      },
      1536: {
        items: 5
      }
    },
    nav: false
  }



  // gallery carousal
  galleryOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    rtl: false,
    autoHeight: true,
    autoWidth: true,
    center: false,
    margin: 25,
    responsive: {
      0: {
        items: 1,
      },
      640: {
        items: 1
      },
      768: {
        items: 1
      },
      896: {
        items: 1
      },
      1024: {
        items: 1,
      },
      1280: {
        items: 1,
      },
      1536: {
        items: 1
      }
    },
    nav: false
  }


  // property details api
  description: any
  googleLatLon: any
  getPropertyDetails() {

    const getParams = {
      lang: this.lang,
      property_id: this.propertyId,
      user_id: this.userDetails.user_id == undefined ? '' : this.userDetails.user_id,
    }


    this.propertyListingService.getPropertyDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.configSettings.setShowLoader(false)
            this.details = response.body.data
            this.property_amenities = this.details?.property_amenities
            this.similarPorperties = this.details.similar_properties
            this.bannerImg = this.details.property_media[0].file_name
            this.description = this.details.description.split('\n')
            this.galleryArr = this.details.property_media.slice(1)
            this.galleryArr = this.details.property_media
            if (this.galleryArr.length > 10) {
              this.thumbs = { ...this.thumbs, loop: true }
            }
            this.googleLatLon = this.details.latlon
            let latlonArr = this.details.latlon?.split(',')
            if (latlonArr) {
              this.lat = Number(latlonArr[0])
              this.lng = Number(latlonArr[1])
            }
            this.is_fav = this.details?.is_favourite
            if (this.similarPorperties.length < 6) {
              this.youMayAlsoLike = { ...this.youMayAlsoLike, loop: false }
            }
          }
        }

      },
      error: (err) => {
        this.toastr.error(err.error.message)
      }
    })
  }



  isReadMore: boolean = false
  // readMore() {

  //   if (this.isReadMore == true) {
  //     // this.desc.nativeElement.className = ''
  //     this.isReadMore = false
  //   } else {
  //     // this.desc.nativeElement.className = 'desc-height'
  //     this.isReadMore = true
  //   }
  //   // console.log(this.desc.nativeElement.className,'this.desc.nativeElement.className');


  // }
  toggleFav(num: number) {
    this.configSettings.setShowLoader(true)

    const getParams = {}
    const postParams = {
      "user_id": this.configSettings.getUserID(),
      "type": "P",
      "type_id": this.propertyId,
      "like": num
    }

    this.favoritesService.toggleFavorite(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.is_fav === 0 ? this.is_fav = 1 : this.is_fav = 0
            this.configSettings.setShowLoader(false)
            if (this.is_fav === 1) {
              // this.toastr.success('', 'Mark as favorite');
              this.translateService.get('You have successfully added the property to your favourites').subscribe(res => {
                this.toastr.success('', res);
              })

              return
            }
            if (this.is_fav === 0) {
              // this.toastr.success('', 'Unmark as favorite');
              this.translateService.get('The property has been removed from you favorites successfully').subscribe(res => {
                this.toastr.success('', res);
              })

              return
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  goToPropertyDetails() {
    this.ngOnInit()
  }


  routeToAgentProfile(value: any) {
    let agentCompanyObj = {
      company_about: value?.company_about,
      company_address: value?.company_address,
      company_agent_id: value?.company_agent_id,
      company_contact: value?.company_contact,
      company_email: value?.company_email,
      company_facebook: value?.company_facebook,
      company_instagram: value?.company_instagram,
      company_logo: value?.company_logo,
      company_name: value?.company_name,
      company_snapchat: value?.company_snapchat,
      company_twitter: value?.company_twitter,
      company_type: value?.company_type,
      company_whatsapp: value?.company_whatsapp,
    };
    this.router.navigate([`/agent-profile/${value?.company_agent_id}`])
    this.configSettings.setAgentDetails(agentCompanyObj)

  }

  thumbsSwiper: any = ''

  mainImage: SwiperOptions = {
    slidesPerView: 1,
    slidesPerGroup: 1,
    spaceBetween: 2,
    loop: true,
    watchSlidesProgress: true,
    autoHeight:true
  };

  tempNumber: any = 4
  loopOrNoLoop: boolean = false
  thumbs: SwiperOptions = {
    slidesPerView: 10,
    slidesPerGroup: 1,
    spaceBetween: 5,
    loop: false,
    centerInsufficientSlides: true
  };

  @ViewChild('galleryCarousel', { static: false }) galleryCarousel?: SwiperComponent;

  slideNext() {
    this.galleryCarousel?.swiperRef.slideNext(500);
  }
  slidePrev() {
    this.galleryCarousel?.swiperRef.slidePrev(500);
  }


}
