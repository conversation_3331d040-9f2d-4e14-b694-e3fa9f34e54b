<section *ngIf="details">
    <div>
        <div class="bg-cover bg-no-repeat relative block h-0 bg-center ">
            <img class="blur-sm w-full h-vh45 lg:h-vh40 xl:h-vh55 object-cover" [src]="bannerImg" alt="">
        </div>
        <div class="container mx-auto px-4 sm:px-14 md:px-24 relative z-10">
            <div class="">
                <!-- block relative h-0 block h-full relative -->
                <img class="w-full h-vh50 lg:h-vh60  object-cover" [src]="bannerImg" alt="">
            </div>
            <div class="grid grid-cols-12 gap-4 px-2">
                <div class="col-span-12 md:col-span-12 xl:col-span-9">
                    <div class="">
                        <div
                            class="block relative h-0 bottom-28 sm:bottom-28 md:bottom-36 lg:bottom-40 xl:bottom-44 z-10 ">
                            <img (click)="routeToAgentProfile(details)"
                                class="w-12 md:w-16 lg:w-24 h-12 md:h-16 lg:h-24 rounded-full cursor-pointer"
                                [src]="details?.company_logo" alt="">
                            <!-- <img class="w-12 md:w-16 lg:w-24 h-12 md:h-16 lg:h-24 rounded-full"
                                *ngIf="details?.logo_image == ''" src="assets/images/architectslogo.jpg" alt=""> -->
                            <div class="flex flex-row space-x-4 mt-5">
                                <div *ngIf="galleryArr?.length > 0" (click)="showGallery = true" class=" "><button
                                        class="items-center bg-white h-9 sm:h-9 lg:h-10 justify-items-center flex px-4  border-0 w-full text-primary md:text-sm lg:text-sm font-medium capitalize hover:bg-accent hover:text-white"><img
                                            class="lg:w-6 w-4 rtl:ml-4 rtl:mr-[unset] mr-4"
                                            src="assets/images/image-gallery.png" alt="">
                                        {{"Gallery"| translate}}</button></div>
                                <div (click)="showMap = true" class=" "><button
                                        class="items-center bg-white h-9 sm:h-9 lg:h-10 justify-items-center flex px-4  border-0 w-full text-primary md:text-sm lg:text-sm font-medium capitalize hover:bg-accent hover:text-white"><img
                                            class="lg:w-6 w-4 rtl:ml-4 rtl:mr-[unset] mr-4 " src="assets/images/map.png"
                                            alt=""> {{"map" |
                                        translate}}</button>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-3 gap-4 mt-6">
                            <div class="col-span-2">
                                <h2 class="text-lg lg:text-3xl font-bold text-white uppercase ">
                                    {{details?.title}}</h2>
                            </div>
                            <div>
                                <ul class="m-0 rtl:text-left ltr:text-right">
                                    <li *ngIf="details?.fb != null"
                                        class="md:inline-block lg:inline-block inline-block bg-steelBlue mx-0.5 lg:mx-1.5 text-center">
                                        <a [href]="details?.fb"><img
                                                class="lg:w-7 lg:h-7 md:h-4 md:w-4 h-4 w-4 lg:p-1.5 md:p-0.5 p-0.5"
                                                src="assets/images/facebookblue.png" alt=""></a>
                                    </li>
                                    <li *ngIf="details?.instagram != null"
                                        class="md:inline-block lg:inline-block inline-block bg-steelBlue mx-0.5 lg:mx-1.5 text-center">
                                        <a [href]="details?.twitter"><img
                                                class="lg:w-7 lg:h-7 md:h-4 md:w-4 h-4 w-4  lg:p-1.5 md:p-0.5 p-0.5"
                                                src="assets/images/twitterblue.png" alt=""></a>
                                    </li>
                                    <li *ngIf="details?.twitter"
                                        class="md:inline-block lg:inline-block inline-block bg-steelBlue mx-0.5 lg:mx-1.5 text-center">
                                        <a [href]="details?.instagram"><img
                                                class="lg:w-7 lg:h-7 md:h-4 md:w-4 h-4 w-4  lg:p-1.5 md:p-0.5 p-0.5"
                                                src="assets/images/instagram-symbolblue.png" alt=""></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="flex items-center gap-2 py-3 ">
                            <img src="../../../assets/icons/pin.png" class="w-3 mb-1">
                            <p class="text-slate-300 text-sm lg:text-base">{{details?.address}}</p>

                        </div>
                        <div class="grid grid-cols-3 gap-4 gap-y-1 border-b border-white pb-3.5">
                            <div class="col-span-2">
                                <div class="flex items-center gap-3 ">
                                    <div *ngIf="details?.size > 0" class="flex items-center gap-2"><img
                                            src="../../../assets/icons/size.png" class="h-4 w-6"><span
                                            class="text-slate-300 text-sm lg:text-base">{{details?.size | number:'1.0'}}
                                            {{"sq.Ft" | translate}}</span>
                                    </div>
                                    <div *ngIf="details?.no_of_bedrooms > 0" class="flex items-center gap-2"><img
                                            src="../../../assets/icons/rooms.png" class="h-4 w-6"><span
                                            class="text-slate-300 text-sm lg:text-base">{{details?.no_of_bedrooms}}</span>
                                    </div>
                                    <div *ngIf="details?.no_of_bathrooms >0 " class="flex items-center gap-2"><img
                                            src="../../../assets/icons/bathrooms.png" class="h-4 w-6"><span
                                            class="text-slate-300 text-sm lg:text-base">{{details?.no_of_bathrooms}}</span>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="userDetails !== ''" class="rtl:text-left ltr:text-right cursor-pointer">
                                <!-- <img src="../../../assets/icons/heart.png"  class="h-4 w-4 inline-block "> -->
                                <img *ngIf="is_fav === 0" src="../../../assets/icons/favorite.svg"
                                    class="h-8 w-8 inline-block" (click)="toggleFav(1)">
                                <img *ngIf="is_fav === 1" src="../../../assets/icons/favorite-red.svg"
                                    class="h-8 w-8 inline-block" (click)="toggleFav(0)">
                                <!-- <div class="h-8 w-8 absolute bottom-4 right-4 hover:cursor-pointer hover:scale-105">
                                </div> -->
                                <span class="text-white text-sm lg:text-lg ml-1.5 ">{{"Save" | translate}}</span>
                            </div>
                            <div class="col-span-full text-2xl font-bold lg:hidden">
                                <h3 *ngIf="details.rental_frequency =='' "
                                    class="text-white">
                                    {{details?.price}}{{"KD" | translate}} {{ details.rental_frequency | titlecase}}
                                </h3>
                                <h3 *ngIf="details.rental_frequency !=='' "
                                    class="text-white">
                                    {{details?.price}}{{"KD" | translate}} / {{ details.rental_frequency | titlecase}}
                                </h3>
                            </div>
                        </div>
                        <!-- <div class="pt-5">
                            <div class="flex ">
                                <button type="button"
                                    class="border-accent border rounded-3xl text-sm text-slate-300 py-1.5 px-8 mr-4 flex content-between"><img
                                        class="lg:w-5 lg:h-5 md:h-4 md:w-4 h-4 w-4 mr-4" src="assets/icons/2dPlan.svg"
                                        alt=""> Master Plan </button>
                                <button type="button"
                                    class="border-accent border rounded-3xl text-sm text-slate-300 py-1.5 px-8  flex content-between"><img
                                        class="lg:w-5 lg:h-5 md:h-4 md:w-4 h-4 w-4 mr-4"
                                        src="assets/icons/masterPlan.svg" alt=""> 2D Plan </button>
                            </div>
                        </div> -->
                        <div class="pt-5 lg:pt-12"*ngIf="description?.length > 0">
                            <h2 class="text-white font-bold text-base lg:text-3xl mb-4"  >{{"Overview" | translate}}</h2>
                            <p class="text-white text-sm  break-all" *ngFor="let item of description" >
                                {{item}}
                                <!-- [ngClass]="isReadMore == true ? 'h-full overflow-visible':'h-14 overflow-hidden'" #desc
                                id="desc" -->
                                <!-- {{details?.description}} -->
                            </p>
                            <!-- 
                            <button *ngIf=" isReadMore" type="button"
                                class="text-slate-300 border mt-5 text-center mx-auto block py-1.5 px-8 hover:bg-accent hover:text-white"
                                (click)="readMore()">{{"Read Less" | translate}}</button>
                            <button *ngIf="!isReadMore" type="button"
                                class="text-slate-300 border mt-5 text-center mx-auto block py-1.5 px-8 hover:bg-accent hover:text-white"
                                (click)="readMore()">{{"Read More" | translate}}</button> -->

                        </div>
                        <div class="pt-2 lg:pt-15" *ngIf="property_amenities?.length != 0">
                            <h2 class="text-white font-bold text-base lg:text-3xl mb-6">{{"Amenities & Features" |
                                translate}}</h2>
                            <div class="grid grid-cols-4 md:grid-cols-3 lg:grid-cols-5 lg:space-y-0">

                                <div class="mx-auto py-2.5" *ngFor="let item of property_amenities">
                                    <span
                                        class="bg-steelBlue rounded-full w-16 h-16 xl:w-24 xl:h-24 flex items-center mx-auto"><img
                                            class="w-6 h-6 xl:w-12 xl:h-12 block mx-auto" *ngIf="item?.icon"
                                            [src]="item?.icon" alt=""></span>
                                    <p
                                        class="text-slate-300 capitalize text-center text-xs xl:text-base mx-auto mt-2 w-3/4">
                                        {{item?.name}}</p>
                                </div>
                            </div>
                        </div>





                    </div>

                </div>
                <div class="col-span-3 hidden xl:block">
                    <div class="block relative bottom-16 z-10 ">
                        <div class="bg-white p-5">
                            <h3 *ngIf="details.rental_frequency =='' "
                                class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl">
                                {{details?.price}}{{"KD" | translate}} {{ details.rental_frequency | titlecase}}</h3>
                            <h3 *ngIf="details.rental_frequency !=='' "
                                class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl">
                                {{details?.price}}{{"KD" | translate}} /{{ details.rental_frequency | titlecase}}</h3>
                            <span
                                class="capitalize bg-slate-200 w-full text-xs md:w-full lg:w-full xl:w-48 flex items-center text-center h-auto md:h-auto lg:h-6 mx-auto rounded-3xl justify-center my-3">
                                {{"last updated" | translate}} {{details?.last_updated | date: 'd MMM y'}}</span>
                            <button type="button"
                                class="capitalize bg-primary text-xs text-white text-center h-8 w-full mt-5 lg:text-lg"><a
                                    href="tel:{{details?.contact_phone}}" class="block">{{"contact now" |
                                    translate}}</a></button>
                        </div>
                        <div class="mt-2">
                            <div class="grid sm:h-30 md:h-max lg:h-36 xl:h-52 2xl:h-60">
                                <span class="relative block h-0"><img class="w-full aspect-[4/3]"
                                        src="../../../assets/images/mapdetails.jpg" alt=""></span>
                                <div class="block text-center mx-auto z-10 relative">
                                    <img class="block mx-auto text-center w-10 md:w-12 lg:w-14 "
                                        src="../../../assets/icons/placeholder-filled-point.png" alt="">
                                    <button (click)="showMap = true"
                                        class="w-full md:w-32 bg-primary text-xs lg:text-sm text-white p-2 mx-auto lg:w-32 xl:w-44 rounded-3xl md:mt-8 lg:mt-10 xl:mt-14">
                                        {{"Find out more" | translate}} </button>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="details.company_address || details.company_name  > 0" class="bg-white mt-2 p-5 mx-auto text-center cursor-pointer"
                            (click)="routeToAgentProfile(details)">
                            <h3 class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl uppercase">
                                {{"Agency" | translate}}</h3>
                            <img class="w-12 md:w-16 lg:w-24 aspect-square rounded-full mx-auto text-center block my-2"
                                src="{{details?.company_logo}}" alt="">
                            <span class="capitalize text-primary w-full text-sm mt-2 font-semibold text-center">
                                {{details.company_name}}</span>
                            <h4 *ngIf="details?.company_address"
                                class="capitalize  text-xs text-primary text-cente w-full mt-2 ">
                                {{details?.company_address}}
                            </h4>
                        </div>
                    </div>
                </div>

                <!--  -->

                <div class="col-span-12">
                    <h2 class="text-white font-bold text-base lg:text-3xl mb-6 capitalize">{{"information" | translate}}
                    </h2>
                    <div
                        class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 lg:space-y-0">
                        <div class="py-2.5">
                            <ul class="flex gap-2 items-center">
                                <li><img class="w-4 h-3 rtl:-scale-x-100"
                                        src="../../../assets/icons/feather-arrow-right.svg" alt="">
                                </li>
                                <li class="text-white font-bold text-[10px] md:text-xs lg:text-sm">{{"Property For" |
                                    translate}}:</li>
                                <li *ngIf="details?.property_for == 'S'"
                                    class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{"" | translate}}</li>
                                <li *ngIf="details?.property_for == 'R'"
                                    class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{"Rent" | translate}}</li>
                            </ul>
                        </div>
                        <div class="py-2.5">
                            <ul class="flex gap-2 items-center">
                                <li><img class="w-4 h-3 rtl:-scale-x-100"
                                        src="../../../assets/icons/feather-arrow-right.svg" alt="">
                                </li>
                                <li class="text-white font-bold text-[10px] md:text-xs lg:text-sm">{{"ID" | translate}}:
                                </li>
                                <li class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{details?.property_number}}</li>
                            </ul>
                        </div>
                        <div *ngIf="details?.no_of_bathrooms > 0" class="py-2.5">
                            <ul class="flex gap-2 items-center">
                                <li><img class="w-4 h-3 rtl:-scale-x-100"
                                        src="../../../assets/icons/feather-arrow-right.svg" alt="">
                                </li>
                                <li class="text-white font-bold text-[10px] md:text-xs lg:text-sm">{{"Total Baths" |
                                    translate}}:</li>
                                <li class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{details?.no_of_bathrooms}}
                                </li>
                            </ul>
                        </div>
                        <div class="py-2.5">
                            <ul class="flex gap-2 items-center">
                                <li><img class="w-4 h-3 rtl:-scale-x-100"
                                        src="../../../assets/icons/feather-arrow-right.svg" alt="">
                                </li>
                                <li class="text-white font-bold text-[10px] md:text-xs lg:text-sm">{{"Furnishing" |
                                    translate}}:</li>
                                <li *ngIf="details?.property_type ==='UF'"
                                    class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{"Unfurnished" | translate}}</li>
                                <li *ngIf="details?.property_type ==='SF'"
                                    class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{"SemiFurnished" | translate}}</li>
                                <li *ngIf="details?.property_type ==='FN'"
                                    class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{"Furnished" | translate}}</li>
                            </ul>
                        </div>
                        <div *ngIf="details?.no_of_bedrooms > 0" class="py-2.5">
                            <ul class="flex gap-2 items-center">
                                <li><img class="w-4 h-3 rtl:-scale-x-100"
                                        src="../../../assets/icons/feather-arrow-right.svg" alt="">
                                </li>
                                <li class="text-white font-bold text-[10px] md:text-xs lg:text-sm">{{"Bedrooms" |
                                    translate}}:</li>
                                <li class="text-[10px] md:text-xs lg:text-base text-slate-300">
                                    {{details?.no_of_bedrooms}}
                                </li>
                            </ul>
                        </div>
                        <div class="py-2.5">
                            <ul class="flex gap-2 items-center">
                                <li><img class="w-4 h-3 rtl:-scale-x-100"
                                        src="../../../assets/icons/feather-arrow-right.svg" alt="">
                                </li>
                                <li class="text-white font-bold text-[10px] md:text-xs lg:text-sm">{{"Year Build" |
                                    translate}}:</li>
                                <li class="text-[10px] md:text-xs lg:text-base text-slate-300">{{details?.build_year}}
                                </li>
                            </ul>
                        </div>
                        <div class="py-2.5">
                            <ul class="flex gap-2 items-center">
                                <li><img class="w-4 h-3 rtl:-scale-x-100"
                                        src="../../../assets/icons/feather-arrow-right.svg" alt="">
                                </li>
                                <li class="text-white font-bold text-[10px] md:text-xs lg:text-sm">{{"Space" |
                                    translate}}:</li>
                                <li class="text-[10px] md:text-xs lg:text-base text-slate-300">{{details?.size |
                                    number:'1.0'}}
                                    {{"Sq.Ft" | translate}}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

            </div>
            <div class="col-span-12 block  xl:hidden mt-10">
                <div class="flex flex-col md:flex-row mx-auto sm:gap-2 gap-5 items-center">

                    <div class="bg-white p-5 sm:p-2 grid sm:h-40 md:h-48  h-56 w-[100%] md:w-[33.33%]">
                        <h3 *ngIf="details.rental_frequency =='' "
                            class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl">
                            {{details?.price}}{{"KD" | translate}} {{ details.rental_frequency | titlecase}}</h3>
                        <h3 *ngIf="details.rental_frequency !=='' "
                            class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl">
                            {{details?.price}}{{"KD" | translate}} /{{ details.rental_frequency | titlecase}}</h3>
                        <span
                            class="capitalize bg-slate-200 w-4/5 md:w-full sm:text-[8px] text-xs flex items-center text-center h-auto mx-auto rounded-3xl justify-center sm:h-5 sm:my-1 my-3">
                            {{"last updated" | translate}} {{details?.last_updated | date: 'd MMM y'}}</span>
                            <button type="button"
                            class="capitalize bg-primary text-xs text-white text-center h-8 w-full mt-5 lg:text-lg"><a
                                href="tel:{{details?.contact_phone}}" class="block">{{"contact now" |
                                translate}}</a></button>
                    </div>

                    <div class="sm:py-2 md:p-2 w-[100%] md:w-[33.33%]">
                        <div class="grid md:h-40 sm:h-48 h-56 w-full mx-auto text-center">
                            <span class="relative block md:bottom-4 h-0"><img class="w-full sm:h-40 md:h-48 h-56"
                                    src="../../../assets/images/mapdetails.jpg" alt=""></span>
                            <div class="block text-center w-2/5 md:w-3/4 mx-auto z-10 relative">
                                <img class="block mx-auto text-center w-10 "
                                    src="../../../assets/icons/placeholder-filled-point.png" alt="">
                                <button (click)="showMap = true"
                                    class="w-full bg-primary text-xs  text-white p-2 mx-auto rounded-3xl mt-8">
                                    {{"Find out more" | translate}}</button>
                            </div>
                        </div>
                    </div>
                    <div *ngIf="details.company_address || details.company_name  > 0" class="w-[100%] md:w-[33.33%]">
                        <div class="bg-white p-5 sm:p-2 sm:h-40 md:h-48 text-center h-56 cursor-pointer"
                            (click)="routeToAgentProfile(details)">
                            <h3 class="text-primary text-center font-bold sm:text-base text-2xl uppercase">
                                {{"Agency" | translate}}
                            </h3>
                            <img class="w-12 h-12  rounded-full mx-auto text-center block my-2"
                                src="{{details?.company_logo}} " alt="">
                            <span
                                class="capitalize text-primary w-full sm:text-[11px] text-sm mt-2 font-semibold text-center">
                                {{details.company_name}}</span>
                            <h4 *ngIf="details?.company_address"
                                class="capitalize  text-xs text-primary text-cente w-full mt-2 pb-2">
                                {{details?.company_address}}
                            </h4>
                        </div>
                    </div>



                </div>
            </div>

            <div class="container mx-auto mb-24 ">
                <div id="" class="mt-14 flex justify-between items-center mb-10">
                    <p class="text-2xl text-white font-bold tracking-wide uppercase">{{"You may also like" | translate}}
                    </p>
                    <div class="flex rtl:flex-row-reverse items-center gap-5">
                        <img src="../../../assets/icons/arrow-semi-left.svg"
                            class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="youMayalsoLikeEleRef.prev()">
                        <img src="../../../assets/icons/arrow-semi-right.svg"
                            class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="youMayalsoLikeEleRef.next()">
                    </div>
                </div>
                <div class="mx-auto">
                    <owl-carousel-o [options]="youMayAlsoLike" #youMayalsoLikeEleRef class="youMayalsoLikeCarousal">
                        <ng-container *ngFor="let property of similarPorperties">
                            <ng-template carouselSlide [width]="250">
                                <app-property-card [property]="property"
                                    (click)="goToPropertyDetails()"></app-property-card>
                            </ng-template>
                        </ng-container>

                    </owl-carousel-o>
                </div>
            </div>
        </div>
    </div>


</section>
<section *ngIf="!details" class="h-vh100">
    <!-- if page is empty -->
</section>

<div *ngIf="showMap" (click)="showMap = false" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="showMap"
    class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white h-1/2 w-11/12 md:w-2/3 lg:w-2/3 lg:h-2/3 xl:w-1/2  rounded-xl">
    <div class="h-[92%]">
        <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b">
            <h5 class="text-lg font-bold leading-normal ">
                {{"Map View" | translate}}
            </h5>
            <button (click)="showMap = false" type="button"
                class="flex hover:cursor-pointer duration-200 ease-in-out hover:opacity-75 ">
                <img src="../../../assets/icons/close-icon.png" alt="" class="w-4 mt-0.5 rtl:ml:1.5 lrt:mr-1.5">
                <span class="text-accent ">{{"Close" | translate}}</span>
            </button>
        </div>
        <div>
        </div>


        <!-- <div>
            <app-map [type]="'modal'" [showSearch]="showSearch" [lat]="lat" [lng]="lng"></app-map>
        </div> -->
        <!-- <div class="map pointer-events-none h-full"> -->
        <a target="_blank" [href]="'https://www.google.com/maps/search/?api=1&query='+googleLatLon+'&zoom=20'"
            class="map  h-full block cursor-pointer ">
            <app-map class=" h-full pointer-events-none" [showSearch]="showSearch" [type]="'modal'" [lat]="lat"
                [lng]="lng"></app-map>
            <div class="map-center-overlay">
                <img src="../../../assets/icons/location-pointer.svg" width="45" height="45">
            </div>
        </a>
        <!-- </div> -->
    </div>
</div>


<div *ngIf="showGallery" (click)="showGallery = false; thumbsSwiper=0" class="bg-overlay fixed inset-0 z-[99] "></div>
<div *ngIf="showGallery"
    class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white w-full lg:w-1/2 rounded-xl ">
    <div class="relative max-h-[80vh] overflow-hidden m-1 lg:m-3">
        <swiper #galleryCarousel class="" [config]="mainImage" [thumbs]="{ swiper: thumbsSwiper }">
            <ng-template swiperSlide class="" *ngFor="let img of galleryArr">
                <img class=" w-full cursor-pointer" [src]="img.file_name">
            </ng-template>
        </swiper>
        <img src="../../../assets/icons/arrow-semi-right.svg"
            class="h-8 aspect-square absolute z-[101] top-1/2 translate-y-1/2 right-3 cursor-pointer "
            (click)="slideNext()">
        <img src="../../../assets/icons/arrow-semi-left.svg"
            class="h-8 aspect-square absolute z-[101] top-1/2 translate-y-1/2 left-3 cursor-pointer "
            (click)="slidePrev()">
    </div>
    <swiper class="h-full textSlide" [config]="thumbs" (swiper)="thumbsSwiper = $event">
        <ng-template swiperSlide class="pb-1 lg:pb-3 " *ngFor="let img of galleryArr">
            <img class=" w-full aspect-square  object-cover cursor-pointer" [src]="img.file_name">
        </ng-template>
    </swiper>
    <!-- <div>
        <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b ">
            <h5 class="text-lg font-bold leading-normal ">
                {{"Gallery" | translate}}
            </h5>
            <button (click)="showGallery = false" type="button" class="flex hover:cursor-pointer ">
                <img src="../../../assets/icons/close-icon.png" alt="" class="w-4 mt-0.5 rtl:ml:1.5 lrt:mr-1.5">
                <span class="text-accent hover:text-accentDark duration-200 ease-in-out ">{{"Close" | translate}}</span>
            </button>
        </div>
        <div class="h-full">
            carousal
            <div class="justify-center items-center relative container  ">

                <div class="h-auto">
                    <owl-carousel-o [options]="galleryOptions" #gallery class="galleryCarousal ">
                        <ng-container *ngFor="let img of galleryArr">
                            <ng-template carouselSlide>
                                <img class="object-cover " [src]="img.file_name">
                            </ng-template>
                        </ng-container>
                    </owl-carousel-o>
                </div>

                <p (click)="gallery.prev()" class="hover:cursor-pointer absolute top-1/2 left-2 hover:scale-105 z-10">
                    <img class="h-7 w-7" src="assets/icons/arrow-left.png">
                </p>
                <p (click)="gallery.next()" class="hover:cursor-pointer absolute top-1/2 right-2 hover:scale-105 z-10">
                    <img class="h-7 w-7" src="assets/icons/arrow-right.png">
                </p>
            </div>
        </div>
    </div> -->
</div>