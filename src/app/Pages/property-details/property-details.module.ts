import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PropertyDetailsRoutingModule } from './property-details-routing.module';
import { PropertyDetailsComponent } from './property-details.component';
import { MapModule } from 'src/app/Components/map/map.module';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { TranslateModule } from '@ngx-translate/core';
import { PropertyCardModule } from 'src/app/Components/property-card/property-card.module';
import { SwiperModule } from 'swiper/angular';


@NgModule({
  declarations: [PropertyDetailsComponent],
  imports: [
    CommonModule,
    PropertyDetailsRoutingModule,
    CarouselModule,
    MapModule,
    TranslateModule,
    PropertyCardModule,
    SwiperModule
  ],exports:[PropertyDetailsComponent]
})
export class PropertyDetailsModule { }
