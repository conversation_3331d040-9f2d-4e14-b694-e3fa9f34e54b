import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { RegistrationRoutingModule } from './registration-routing.module';
import { RegistrationComponent } from './registration.component';
import { ArchitectRegistrationComponent } from './architect-registration/architect-registration.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AgentRegistrationComponent } from './agent-registration/agent-registration.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [RegistrationComponent, ArchitectRegistrationComponent,AgentRegistrationComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RegistrationRoutingModule,
    NgSelectModule,
    TranslateModule
  ],exports:[RegistrationComponent]
})
export class RegistrationModule { }
