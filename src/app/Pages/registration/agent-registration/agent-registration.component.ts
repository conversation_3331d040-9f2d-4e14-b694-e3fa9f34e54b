import { Location } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { AddressHelper } from 'src/app/globalFunctions/addressHelpers';
import { AmenitiesHelper } from 'src/app/globalFunctions/amenitiesHelpers';
import { ValidationHelpers } from 'src/app/globalFunctions/validationHelpers';
import { PostPropertyService } from 'src/app/Services/Properties/post-property.service';
import { PropertyCategoryService } from 'src/app/Services/Properties/property-category.service';

@Component({
  selector: 'app-agent-registration',
  templateUrl: './agent-registration.component.html',
  styleUrls: ['./agent-registration.component.scss']
})
export class AgentRegistrationComponent implements OnInit {

  constructor(
    private fb : FormBuilder,
    private router: Router,
    private configSettings: configSettings,
    private toastr: ToastrService,
    private location: Location,
    public addressHelper: AddressHelper,
    public validationHelpers: ValidationHelpers,
    public amenitiesHelper : AmenitiesHelper,
    private propertyCategoryService: PropertyCategoryService,
    private postPropertyService : PostPropertyService
  ) { }

  @Input() userDetails: any

  counter = Array;

  features: Array<any> = []
  amenities: Array<any> = []

  responseData: any
  ngOnInit(): void {
    
  }
  registrationForm = this.fb.group({
      property_category: ['',Validators.required],
      property_for: ['',Validators.required],
      social_media_link: [''],
      property_title_en: [''],
      land_size: [''],
      property_desc_en: [''],
      house_size: [''],
      rent_price: [''],
      bed_rooms: [''],
      bath_rooms: [''],
      property_type: ['',Validators.required],
      features: [''],
      amenities: [''],
      email: ['',Validators.pattern(/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)],
      code: [''],
      number: [''],
      company_name: [''],
      country_id: ['', Validators.required],
      area_id: ['', Validators.required],
      state_id: ['', Validators.required],
  
  })


  isCatErr: boolean = false
  isCountryErr: boolean = false
  isStateErr: boolean = false
  isAreaErr: boolean = false
  isTypeErr: boolean = false
  isPropAsErr: boolean = false
    // post property 
    postProperty() {
      this.registrationForm.controls['property_category'].errors?.required ? this.isCatErr = true : this.isCatErr = false
      this.registrationForm.controls['property_for'].errors?.required ? this.isPropAsErr = true : this.isPropAsErr = false
      this.registrationForm.controls['property_type'].errors?.required ? this.isTypeErr = true : this.isTypeErr = false
      this.registrationForm.controls['country_id'].errors?.required ? this.isCountryErr = true : this.isCountryErr = false
      this.registrationForm.controls['state_id'].errors?.required ? this.isStateErr = true : this.isStateErr = false
      this.registrationForm.controls['area_id'].errors?.required ? this.isAreaErr = true : this.isAreaErr = false
      
      if (!this.registrationForm.invalid) {
        const getParams = {
      
        };
        const postParams = {
          property_for: this.registrationForm.controls['property_for'].value,
          whatsapp_number: '',
          company_name: this.registrationForm.controls['company_name'].value,
          mobile_number: this.registrationForm.controls['code'].value?.toString().concat(this.registrationForm.controls['number'].value),
          email: this.registrationForm.controls['email'].value,
          social_media_link: this.registrationForm.controls['social_media_link'].value,
          property_category: this.registrationForm.controls['property_category'].value,
          property_type: this.registrationForm.controls['property_type'].value,
          keywords: '',
          rental_frequency: '',
          property_title_en: this.registrationForm.controls['property_title_en'].value,
          property_title_ar: '',
          property_desc_en: this.registrationForm.controls['property_desc_en'].value,
          property_desc_ar: '',
          established_year: '',
          features: this.registrationForm.controls['features'].value,
          amenities: this.registrationForm.controls['amenities'].value,
          rent_price: this.registrationForm.controls['rent_price'].value,
          bath_rooms: this.registrationForm.controls['bath_rooms'].value,
          state_id: this.registrationForm.controls['state_id'].value,
          land_size: this.registrationForm.controls['land_size'].value,
          house_size: this.registrationForm.controls['house_size'].value,
          bed_rooms: this.registrationForm.controls['bed_rooms'].value,
          no_of_floor: '',
          country_id: this.registrationForm.controls['country_id'].value,
          area_id: this.registrationForm.controls['area_id'].value,
          latlon: '',
          address_line_1: '',
          address_line_2: '',
          landmark: '',
          paymode: '',
          user_id: this.userDetails.user_id,
          property_images: '',
          passport_image: '',
          logo: '',
        };
        this.postPropertyService
          .postProperties(getParams, postParams)
          .subscribe({
            next:(response) => {
              if (response.status === 200) {
                if (response.body.status == 200) {
               this.router.navigate(['/choose-plan'])   
               this.responseData = response.body.data
               this.toastr.success(response.body.message)
                } else {
                  this.toastr.warning('', response.body.message);
                }
              } else {
                this.toastr.error('', 'Something went wrong');
              }
            },
            error:(err) => {
              this.toastr.error('', err.error.message);
            }
          })
        
      }

    }

    goBack(){
      this.location.back()
    }

    propertyCat: any
    getPropertyCategory() {
      const getParams = {
        parent_id: ''
      };
      this.propertyCategoryService.getPropertyCategory(getParams).subscribe({
        next: (response) => {
          if (response.status === 200) {
            if (response.body.status == 200) {
              this.propertyCat = response?.body?.data; 
                         
            }
          }
        },
        error: (err) => {
          this.toastr.error('', err.error.message);
        },
      });
    }

    showAmenitiesFun(){
      this.showAmenities = !this.showAmenities
      this.showFeatures = false
    }

    showFeaturesFun(){
      this.showFeatures = !this.showFeatures
      this.showAmenities = false
    }

    
    featureName: Array<any> = []
    showFeatures: boolean = false
    getFeatures(e: any,arr : any){
  
      if (e.target.checked) {
        this.featureName.push(e.target.nextSibling.innerHTML)
        this.features.push(e.target.value)
      } else if (!e.target.checked ) {
        var index = this.features.indexOf(e.target.value);
        if (index > -1) {
          this.features.splice(index, 1)
        }
        var name = this.featureName.indexOf(e.target.nextSibling.innerHTML);
        if (name > -1) {
          this.featureName.splice(name, 1)
        }
      }    
      
      this.registrationForm.controls['features'].setValue(this.features.join(','))
    }


    amenitiesName: Array<any> = []
    showAmenities: boolean = false
    getAmenities(e: any,arr : any){
  
      if (e.target.checked) {
        this.amenitiesName.push(e.target.nextSibling.innerHTML)
        this.amenities.push(e.target.value)
      } else if (!e.target.checked ) {
        var index = this.amenities.indexOf(e.target.value);
        if (index > -1) {
          this.amenities.splice(index, 1)
        }
        var name = this.amenitiesName.indexOf(e.target.nextSibling.innerHTML);
        if (name > -1) {
          this.amenitiesName.splice(name, 1)
        }
      }    
      
      this.registrationForm.controls['amenities'].setValue(this.amenities.join(','))
    }
  
}
