<section class="">
    <div class="container mx-auto sm:px-32  md:px-20 lg:px-10 xl:px-40 2xl:px-52 pt-4 pb-6 lg:rounded-xl">
    <div class="flex  bg-white lg:rounded-xl">
      <div class=" flex w-full  px-6   lg:px-12 lg:w-4/5">
        <div class="w-full">
          <div class="flex pt-3">
          <span (click)="goBack()" class="relative cursor-pointer mr-3 py-1"><img src="../../../assets/icons/gray-left-arrow.png" alt=""></span>
          <p  class=" text-gray-700  text-sm font-semibold py-0.5">{{"Kindly fill all required fields to proceed." | translate}}</p>
        </div>
        <div class="px-8">
          <div class="mt-4">
            <h1 class="text-primary font-bold text-lg">{{"Basic Details" | translate}}</h1>
          </div>
  
          <form class=" grid grid-cols-1  md:grid-cols-2 gap-x-5" [formGroup]="registrationForm">
            

            <div>
                <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"SELECT PROPERTY CATEGORY" | translate}}*</label>
                <select formControlName="property_category" class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none" (click)="getPropertyCategory()">
                  <option value="" disabled>{{"Please Select Property Category" | translate}}</option>
                  <option [value]="item?.id" *ngFor="let item of propertyCat">{{item?.name}}</option>
                </select>
                <div *ngIf="this.registrationForm.controls.property_category.invalid && (this.registrationForm.controls.property_category.dirty || this.registrationForm.controls.property_category.touched || isCatErr)" class="text-red-600 text-xs mt-2 font-semibold">
                  <div *ngIf="this.registrationForm.controls.property_category.errors" >
                      {{'Please Select Property Category.'}}
                  </div>
                </div>
              </div>
  
            <div>
              <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"PROPERTY FOR" | translate}}*</label>
              <select formControlName="property_for" class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none">
                  <option value="" disabled>{{"Please Select Property For" | translate}}</option>
                  <option value="S">{{"Sale" | translate}}</option>
                <option value="R">{{"Re-Sale" | translate}}</option>
              </select>
              <div *ngIf="this.registrationForm.controls.property_for.invalid && (this.registrationForm.controls.property_for.dirty || this.registrationForm.controls.property_for.touched || isPropAsErr)" class="text-red-600 text-xs mt-2 font-semibold">
                <div *ngIf="this.registrationForm.controls.property_for.errors" >
                    {{'Please Select Property For.'}}
                </div>
              </div>
            </div>

            <div>
              <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"SELECT COUNTRY" | translate}}*</label>
              <select formControlName="country_id" class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none" (click)="addressHelper.getCountry()">
                <option value=""  *ngIf="addressHelper.countries?.length == 0">{{"Choose your country" | translate}}</option>
                <option [ngValue]="country.country_id" *ngFor="let country of addressHelper.countries">{{country.name}}</option>
              </select>
              <div *ngIf="this.registrationForm.controls.country_id.invalid && (this.registrationForm.controls.country_id.dirty || this.registrationForm.controls.country_id.touched || isCountryErr)" class="text-red-600 text-xs mt-2 font-semibold">
                <div *ngIf="this.registrationForm.controls.country_id.errors" >
                    {{'Please Select Country.'}}
                </div>
              </div>
            </div>

            <div>
                <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"SELECT STATE" | translate}}*</label>
                <select formControlName="state_id" class="block w-full rounded-sm text-sm  border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none " [disabled]="addressHelper.states?.length == 0" (click)="addressHelper.getState(registrationForm.controls.country_id.value)">
                    <option value="" *ngIf="addressHelper.states?.length == 0"> 
                    <span *ngIf="registrationForm.controls.country_id.value == ''">{{"Please Select Country First" | translate}}</span>  
                    <span *ngIf="registrationForm.controls.country_id.value != ''">{{"Choose State" | translate}}</span>  
                    </option>
                    <option [ngValue]="state.state_id" *ngFor="let state of addressHelper.states" >{{state.name}}</option>
                  </select>
                  <div *ngIf="this.registrationForm.controls.state_id.invalid && (this.registrationForm.controls.state_id.dirty || this.registrationForm.controls.state_id.touched || isStateErr)" class="text-red-600 text-xs mt-2 font-semibold">
                    <div *ngIf="this.registrationForm.controls.state_id.errors" >
                        {{'Please Select State.'}}
                    </div>
                  </div>
              </div>

            
            <div>
              <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"SELECT CITY OR AREA" | translate}}*</label>
              <select formControlName="area_id" class="block w-full rounded-sm text-sm  border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none " [disabled]="addressHelper.areas?.length == 0" (click)="addressHelper.getAreas(registrationForm.controls.state_id.value)">
               
                <option  value="" *ngIf="addressHelper.areas?.length == 0">
                  <span *ngIf="registrationForm.controls.state_id.value == ''">{{"Please Select State First" | translate}}</span>  
                  <span *ngIf="registrationForm.controls.state_id.value != ''">{{"Choose City" | translate}}</span>  
                </option>
                <option [ngValue]="area.area_id" *ngFor="let area of addressHelper.areas" >{{area.name}}</option>
              </select>
              <div *ngIf="this.registrationForm.controls.area_id.invalid && (this.registrationForm.controls.area_id.dirty || this.registrationForm.controls.area_id.touched || isAreaErr)" class="text-red-600 text-xs mt-2 font-semibold">
                <div *ngIf="this.registrationForm.controls.area_id.errors" >
                    {{'Please Select Area.'}}
                </div>
              </div>
            </div>

            <div>
                <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"UPLOAD VIDEO LINK" | translate}}</label>
                <input formControlName="social_media_link" placeholder="" type="text" class="block w-full text-sm placeholder:text-sm placeholder:text-[#202020]  placeholder:font-medium   border border-sky-700 bg-white px-2 py-2 rounded-sm  placeholder-gray-400 focus:outline-none " />
              </div>
         
  
          <div class="mt-6 col-span-1 md:col-span-2">
            <h1 class="text-primary font-bold text-lg">{{"Property Details" | translate}}</h1>
          </div>
  
          
            <div>
              <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"ADD TITLE" | translate}}</label>
              <input formControlName="property_title_en" type="text"  class="block w-full text-sm  rounded-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none "/>
            </div>
            <div>
                <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"BED ROOMS" | translate}}</label>
                <select formControlName="bed_rooms" class="block w-full rounded-sm text-sm  border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none " >
                 
                  <option  value="" disabled>Please select Number of Bedrooms" | translate}}</option>
                  <option *ngFor="let item of counter(10) ;let i= index" [value]="i+1" class="bg-inherit">{{i+1}} {{"Bedroom" | translate}}</option>

                </select>
              </div>
  

              <div>
                <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"ADD LAND SIZE" | translate}}</label>
                <input formControlName="land_size" type="text"  class="block w-full text-sm  rounded-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none "/>
              </div>
            

              <div>
                <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"BATHROOMS" | translate}}</label>
                <select formControlName="bath_rooms" class="block w-full rounded-sm text-sm  border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none " >
                    <option  value="" disabled>Please select Number of Bathrooms" | translate}}</option>
                    <option *ngFor="let item of counter(10) ;let i= index" [value]="i+1" class="bg-inherit">{{i+1}} {{"Bathroom" | translate}}</option>

                </select>
              </div>
              <div>
                <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"ADD HOUSE SIZE" | translate}}</label>
                <input formControlName="house_size" type="text"  class="block w-full text-sm  rounded-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none "/>
              </div>
             
              <div>
                <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"CHOOSE FURNISHED TYPE" | translate}}*</label>
                <select formControlName="property_type" class="block w-full rounded-sm text-sm  border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none " >
                 
                  <option value="" disabled>{{"Please Select Furnished Type" | translate}}</option>
                  <option value="FN" >{{"Furnished" | translate}}</option>
                  <option value="SF" >{{"Semi-Furnished" | translate}}</option>
                  <option value="UF" >{{"Un-Furnished" | translate}}</option>
                </select>
                <div *ngIf="this.registrationForm.controls.property_type.invalid && (this.registrationForm.controls.property_type.dirty || this.registrationForm.controls.property_type.touched || isTypeErr)" class="text-red-600 text-xs mt-2 font-semibold">
                  <div *ngIf="this.registrationForm.controls.property_type.errors" >
                      {{'Please Select Furnished Type.'}}
                  </div>
                </div>
              </div>

              <div>
                <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"ADD DESCRIPTION" | translate}}</label>
                <textarea formControlName="property_desc_en" placeholder="Enter property descriptions..." type="text"  class="h-24 placeholder:text-sm placeholder:text-[#202020]  placeholder:font-medium resize-none block w-full text-sm  rounded-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none "></textarea>
              </div>

              <div>
                <div>
                    <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"ADD FEATURES" | translate}}</label>
                    <span class="h-0 block relative "><img src="../../../../assets/images/downarrow.svg" class="absolute right-1 top-4" alt=""></span>
                    <p class="block w-full rounded-sm text-sm  border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none cursor-pointer" (click)="amenitiesHelper.getPropertyAmenities('F');showFeaturesFun()">
                      <span *ngIf="featureName.length == 0" >{{"Please Select Features" | translate}}</span>
                      <span *ngIf="featureName.length > 0" >{{featureName.join(',')}}</span>
                    </p>
                    <div class="h-0 relative block z-20" *ngIf="showFeatures">
                      <ul class="p-2 w-full bg-slate-100 " >
                        <li class="py-1" *ngFor="let features of amenitiesHelper.features"> 
                          <div class="form-check flex">
                          <input (change)="getFeatures($event,amenitiesHelper.features)" [value]="features?.id" type="checkbox" class="form-check-input" id="exampleCheck1">
                          <label class="form-check-label text-sm ml-4 cursor-pointer" for="exampleCheck1">{{features?.name}}</label>
                        </div>
                      </li>
                  
                      </ul>
                    </div>
                  </div>
    
                  <div>
                    <label class= "relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"ADD AMENITIES" | translate}}</label>
                    <span class="h-0 block relative "><img src="../../../../assets/images/downarrow.svg" class="absolute right-1 top-4" alt=""></span>
                    <p class="block w-full rounded-sm text-sm  border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none cursor-pointer" (click)="amenitiesHelper.getPropertyAmenities('A');showAmenitiesFun()">
                      <span *ngIf="amenitiesName.length == 0" >{{"Please Select Amenities" | translate}}</span>
                      <span *ngIf="amenitiesName.length > 0" >{{amenitiesName.join(',')}}</span>
                    </p>
                    <div class="h-0 relative block z-20" *ngIf="showAmenities">
                      <ul class="p-2 w-full bg-slate-100 " >
                        <li class="py-1" *ngFor="let amenities of amenitiesHelper.amenities"> 
                          <div class="form-check flex">
                          <input (change)="getAmenities($event,amenitiesHelper.amenities)" [value]="amenities?.id" type="checkbox" class="form-check-input" id="exampleCheck1">
                          <label class="form-check-label text-sm ml-4 cursor-pointer" for="exampleCheck1">{{amenities?.name}}</label>
                        </div>
                      </li>
                  
                      </ul>
                    </div>
                  </div>
              </div>
              
              <div>
                <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"SELL PRICE/RENT PRICE (PER MONTH)" | translate}}</label>
                <input formControlName="rent_price" type="text" placeholder=""  class="placeholder:text-sm placeholder:text-[#202020]  placeholder:font-medium block w-full text-sm  rounded-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none "/>
              </div>
              
         
  
          <div class="mt-6 col-span-1 md:col-span-2">
            <h1 class="text-primary font-bold text-lg">{{"Contact Details" | translate}}</h1>
          </div>
  
          
          <div>
            <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"FULL NAME" | translate}}</label>
            <input formControlName="company_name" type="text" class="placeholder:text-sm placeholder:text-[#202020]  placeholder:font-medium block w-full text-sm  rounded-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none "/>
          </div>

          <div>
            <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"EMAIL ADDRESS" | translate}}</label>
            <input formControlName="email" type="text" class="placeholder:text-sm placeholder:text-[#202020]  placeholder:font-medium block w-full text-sm  rounded-sm border border-sky-700 bg-white px-2 py-2  placeholder-gray-400 focus:outline-none "/>
            <div *ngIf="this.registrationForm.controls.email.invalid && (this.registrationForm.controls.email.dirty || this.registrationForm.controls.email.touched || isTypeErr)" class="text-red-600 text-xs mt-2 font-semibold">
              <div *ngIf="this.registrationForm.controls.email.errors" >
                  {{'Please Enter Valid Email Address.'}}
              </div>
            </div>
          </div>

          <div>
            <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">{{"MOBILE NUMBER" | translate}}</label>
            <div  class=" w-full inline-block border rounded hover: border-gray-500">
                <span >
                    <select formControlName="code" class="w-1/3 px-2 py-2  text-gray-500  2xl:w-1/4  bg-white text-sm border-r-0 placeholder:font-normal focus:outline-none ng-pristine ng-valid ng-touched" >
                        <option class="text-sm">965</option>
                        <option class="text-sm">96</option>
                        <option class="text-sm">91</option>
                    </select>
                </span>
                <input (keypress)="validationHelpers.validatePhone($event)" formControlName="number" type="text" name="phone" placeholder="Mobile number" class="w-2/3 2xl:w-3/4 px-4 text-sm font-medium border-l-0 placeholder:font-normal focus:outline-none ng-pristine ng-valid ng-touched" >
                <div  class="block relative h-0 bottom-[28px] left-24 w-1 text-gray-200">
                    <p>|</p>
                </div>
             
            </div>
            <!-- <div *ngIf="this.registrationForm.controls.code.invalid && (this.registrationForm.controls.code.dirty || this.registrationForm.controls.code.touched)" class="text-red-600 text-xs mt-2 font-semibold">
              <div *ngIf="this.registrationForm.controls.code.errors" >
                  {{'Please select phone code.'}}
              </div>
            </div> -->
            <div *ngIf="validationHelpers.phoneErr" class="text-red-600 text-xs mt-2 font-semibold">
              {{'Please enter valid phone number.'}}
           </div>
          </div>
          </form>
  
          <div class="md:w-1/2 md:pr-3">
            <button  (click)="postProperty()" [ngClass]="registrationForm.invalid ? 'hover:bg-slate-400 focus:outline-none focus:ring focus:ring-slate-300 focus:ring-opacity-50 cursor-not-allowed': ''" class="my-8 flex w-full justify-center transform items-center  rounded-sm bg-primary px-6 py-2 text-sm capitalize tracking-wide text-white transition-colors duration-300 hover:bg-blue-400 focus:outline-none focus:ring focus:ring-blue-300 focus:ring-opacity-50">
              <span *ngIf="!registrationForm.invalid"> {{"SAVE DETAILS" | translate}} </span>
              <span *ngIf="registrationForm.invalid">{{"PLEASE FILL ALL THE REQUIRED FIELDS" | translate}}</span>
            </button>
          </div>
        </div>
        </div>
      </div>
    </div>
    </div>
  </section>