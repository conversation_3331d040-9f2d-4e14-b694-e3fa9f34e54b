import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { configSettings } from 'src/app/Config/config.settings';
import { AddressService } from 'src/app/Services/Address/address.service';
import { UserTypesService } from 'src/app/Services/user-types.service';

@Component({
  selector: 'app-registration',
  templateUrl: './registration.component.html',
  styleUrls: ['./registration.component.scss']
})
export class RegistrationComponent implements OnInit {

  constructor(
   private route: ActivatedRoute,
   private configSettings: configSettings,
  ) { }

  userTypeId: string
  userDetails: any
  ngOnInit(): void {
    this.route.params.subscribe(param => {
      this.userTypeId = param['type']      
    
    })
    this.getUserTypes()
  }

  getUserTypes() {
    this.userDetails = this.configSettings.getUserDetails()

    // this.userDetails?.user_types.forEach((e: any) => {
    //   if (e.type_id == 4) {
    //     console.log(e,'e');
    //   }
    // });

    //  console.log(this.userDetails?.user_types[0]?.type_id,'this.userdata');
    
    
    
  }


}
