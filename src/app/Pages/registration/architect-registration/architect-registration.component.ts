import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ExpertiseService } from 'src/app/Services/Expertise/expertise.service';
import { AddressService } from 'src/app/Services/Address/address.service';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { configSettings } from 'src/app/Config/config.settings';
import { UserAuthenticationService } from 'src/app/Services/user-authentication.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-architect-registration',
  templateUrl: './architect-registration.component.html',
  styleUrls: ['./architect-registration.component.scss']
})
export class ArchitectRegistrationComponent implements OnInit {
  first_name: string;
  agencyName: string;
  phoneCode: any;
  lang: any;

  constructor(
    private expertiseService: ExpertiseService,
    private toastr: ToastrService,
    private addressService: AddressService,
    private configSettings: configSettings,
    private userAuthenticationService:UserAuthenticationService,
    private router:Router,
    private translateService: TranslateService,


  ) { }
  // VARIABLES
  @Input() userDetails: any
  @Input() userTypeId: any

  userTypeNumber:any
  expertiseList: any
  listOfUserExpertise:any = []

  countryList: any
  // selectedCountry: number = 114

  stateList: any
  selectedState: number

  areaList: any
  selectedArea: number

  allTypesOfUser:any = []

  // //////////////////
  @ViewChild('companyLogoInput') companyLogoInputRef: ElementRef

  ngOnInit(): void {
    this.lang = this.configSettings.getLang();
    this.configSettings.setShowLoader(true);

  if(this.userTypeId == 'AR'){
    this.userTypeNumber = 4
  } else if(this.userTypeId == 'AG'){
    this.userTypeNumber = 3
  }

    this.userDetails.user_types.forEach((el: any) => {
      this.allTypesOfUser.push(el.id)
    })

    if(this.allTypesOfUser.includes(this.userTypeNumber)){
      console.log(this.allTypesOfUser)
    } else{
      this.allTypesOfUser.push(this.userTypeNumber)
      console.log(this.allTypesOfUser)
    }

    // this.allTypesOfUser.push(+this.userTypeId)
    this.getExpertiseList()
    
  }

  

  userInformation = new FormGroup({
    companyName: new FormControl(''),
    agencyNameAr: new FormControl('',),
    agencyNameEn: new FormControl('',Validators.required),
    countryCode: new FormControl('',Validators.required),
    architectNameAr: new FormControl('',),
    architectNameEn: new FormControl('',Validators.compose([Validators.required, Validators.pattern("^[a-zA-Z@!@#$%^&*]{1,15}$")])),
    email: new FormControl('',Validators.compose([Validators.required, Validators.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$")])),
    phoneNumber: new FormControl('',[Validators.required, Validators.pattern("^[0-9]{8,12}$"), Validators.minLength(8), Validators.maxLength(12)]),
    wtspNumber: new FormControl('',[Validators.required, Validators.pattern("^[0-9]{8,12}$"), Validators.minLength(8), Validators.maxLength(12)]),
    aboutUsEn: new FormControl('',Validators.required),
    aboutUsAr: new FormControl('',Validators.required),
    expertise: new FormControl('',Validators.required),
    country: new FormControl(114),
    state: new FormControl('',Validators.required),
    area: new FormControl('',Validators.required),
    flat: new FormControl('',Validators.required),
    street: new FormControl('',Validators.required),
    building: new FormControl('',Validators.required),
    address_line_en: new FormControl('',),
    address_line_ar: new FormControl('',),
    zipCode: new FormControl('',[Validators.required, Validators.pattern("^[0-9]{6,8}$"), Validators.maxLength(8)]),
    file: new FormControl('',Validators.required),

  })
  
  


  



  getExpertiseList() {
    this.expertiseService.getExpertise({}).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            // console.log(response.body.data)
            this.expertiseList = response.body.data
            this.getCountries()
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }
  

  getCountries() {
    const getParams = {
    }

    this.addressService.getCountry(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.countryList = response.body.data
            this.getStates(114)
            this.configSettings.setShowLoader(false);
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  getStates(countryID: number) {

    const getParams = {
      country_id: countryID
    }

    this.addressService.getState(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.stateList = response.body.data

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


  getAreas(stateID: number) {

    const getParams = {
      state_id: stateID
    }

    this.addressService.getAreas(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.areaList = response.body.data
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  addExpertise(e: any) {
    this.listOfUserExpertise.push(e);
    // console.log(this.listOfUserExpertise.length);
    
  }

  removeExpertise(e: any) {
    this.listOfUserExpertise.splice(
      this.listOfUserExpertise.indexOf(e.value),
      1
    );
  }

  // ///////////////////////////////////////////////////////////
  companyLogo: string

  removeImage() {
    this.companyLogo=''}

  addCompanyLogo(event: any) {
    if (event.target.files && event.target.files[0]) {
      const filesAmount = event.target.files.length;
      for (let i = 0; i < filesAmount; i++) {
        const reader = new FileReader();

        reader.onload = (event: any) => {
          this.companyLogo = event.target.result
          // this.propertyInformation.patchValue({
          //   passportImage: event.target.result
          // });
        }

        reader.readAsDataURL(event.target.files[i]);
      }
    }
  }

  addCompanyLogoBtn() {
    this.companyLogoInputRef.nativeElement.click()
  }

  getuserFormInfo(userInformation:any) {
    
    
    // console.log(userInformation.agencyName)
    // console.log(userInformation.controls.agencyName.value)

    
    if((userInformation.controls.agencyNameEn.value) ==="" && this.userTypeId === "AG" ){
      this.translateService.get('Agency name cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
       
      })
      return
    }
    
    if((userInformation.controls.agencyNameAr.value) ==="" && this.userTypeId === "AG" ){
      this.translateService.get('Agency name cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
       
      })
      return
    }
    if((userInformation.controls.architectNameEn.value) ==="" && this.userTypeId === "AR" ){
      this.translateService.get('Architect  name cannot be empty in English').subscribe(res =>{
        this.toastr.error('', res);
       
      })
      return
    }
    // if(userInformation.get('architectNameEn')?.invalid && this.userTypeId === "AR" ) {
    //   this.translateService.get('Please enter valid architect name').subscribe(res =>{
    //     this.toastr.error('', res);
    //   })
    //   return
    // }
    if((userInformation.controls.architectNameAr.value) ==="" && this.userTypeId === "AR" ){
      this.translateService.get('Architect  name cannot be empty in Arabic').subscribe(res =>{
        this.toastr.error('', res);
       
      })
      return
    }
    
    if(userInformation.controls.email.value ==="") {
      this.translateService.get('Contact email cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.get('email')?.invalid) {
      this.translateService.get('Please enter valid email').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.phoneNumber.value =="") {
      this.translateService.get('Phone number cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.get('phoneNumber')?.invalid) {
      this.translateService.get('Please enter valid phone number').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.wtspNumber.value =="") {
      this.translateService.get('Whatsapp number cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.wtspNumber.value =="") {
      this.translateService.get('Please enter valid whatsapp number').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.expertise.value =="") {
      this.translateService.get('Expertise cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.country.value =="") {
      this.translateService.get('Country name cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.state.value =="") {
      this.translateService.get('State cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    
    
    if(userInformation.controls.area.value =="") {
      this.translateService.get('Area cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
        
      })
      return
    }
    if(userInformation.controls.aboutUsEn.value =="") {
      this.translateService.get('About us cannot be empty(English)').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.aboutUsAr.value =="") {
      this.translateService.get('About us cannot be empty(Arabic)').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.street.value =="") {
      this.translateService.get('Street name cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.building.value =="") {
      this.translateService.get('Building name cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.flat.value =="") {
      this.translateService.get('Flat name cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.zipCode.value =="") {
      this.translateService.get('ZipCode cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.get('zipCode')?.invalid) {
      this.translateService.get('Invalid ZipCode').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }  
   
    if(userInformation.controls.address_line_en.value =="") {
      this.translateService.get('Address Line (English) cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.address_line_ar.value =="") {
      this.translateService.get('Address Line (Arabic) cannot be empty').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(userInformation.controls.file.value =="") {
      this.translateService.get('Please upload photos').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    else{
    
   

const getParams ={}

const postParams = {


  user_id: this.userDetails?.user_id,
  first_name: this.userDetails?.first_name,
  last_name: this.userDetails?.last_name,
  email: this.userDetails?.email,
  phone: this.userDetails?.phone,
  user_type_id: this.allTypesOfUser.toString(),

  expertise: this.listOfUserExpertise.toString(),

  company_name_en: this.userInformation.controls['companyName'].value,
  architect_name_en: this.userInformation.controls['architectNameEn'].value,
  architect_name_ar: this.userInformation.controls['architectNameAr'].value,
  agent_name_en: this.userInformation.controls['agencyNameEn'].value,
  address_line_en: this.userInformation.controls['address_line_en'].value,
  address_line_ar: this.userInformation.controls['address_line_ar'].value,
  agent_name_ar: this.userInformation.controls['agencyNameAr'].value,

  contact_number: this.userInformation.controls['phoneNumber'].value,
  whatsapp: this.userInformation.controls['wtspNumber'].value,
  contact_email: this.userInformation.controls['email'].value,
  

  desc_en: this.userInformation.controls['aboutUsEn'].value,
  desc_ar: this.userInformation.controls['aboutUsAr'].value,
  
  country_id: this.userInformation.controls['country'].value,
  state_id: this.userInformation.controls['state'].value,
  area_id: this.userInformation.controls['area'].value,
  street: this.userInformation.controls['street'].value,
  building: this.userInformation.controls['building'].value,
  flat: this.userInformation.controls['flat'].value,
  zip_code: this.userInformation.controls['zipCode'].value,
  company_logo: this.companyLogo


  


}
 
  this.userAuthenticationService.postEditProfile(getParams, postParams).subscribe({
  next: (response) => {
    if (response.status === 200) {
      if (response.body.status == 200) {
        if (this.userTypeId === 'UR') {
        this.toastr.success(response.body.message)
        }
        this.configSettings.setIsUserLoggedIn(true)
        
        localStorage.removeItem('userDetails')
        this.configSettings.setLocalStorage('userDetails', response?.body?.data)
        this.configSettings.setUserTypes()

        if(this.userTypeId === 'AG'){
          this.translateService.get('Agent details successfully updated.').subscribe(res =>{
            this.toastr.success('', res);
          })
          this.router.navigate(['/choose-plan/AG'])
        } else if(this.userTypeId === 'AR') {
          this.translateService.get("Architect details successfully updated.").subscribe(res =>{
            this.toastr.success('', res);
          })
          this.router.navigate(['/choose-plan/AR'])
        }

      }
    }

  },
  error: (err) => {
    this.toastr.error(err.error.message)
  }
})

  }

  

}
}

// const postParams = {
//   user_id: this.userDetails?.user_id,
//   first_name: this.userDetails?.first_name,
//   last_name: this.userDetails?.last_name,
//   email: this.userDetails?.email,
//   phone: this.userDetails?.phone,
//   user_types: this.allTypesOfUser.toString(),

//   expertise: this.listOfUserExpertise.toString(),

//   company_name_en: this.registrationForm.controls['company_name'].value,
//   architect_name_en: this.registrationForm.controls['architect_name'].value,
//   agent_name_en: this.registrationForm.controls['agency_name'].value,

//   country_id: this.registrationForm.controls['country_id'].value,
//   state_id: this.registrationForm.controls['state_id'].value,
//   area_id: this.registrationForm.controls['area_id'].value,
//   street: this.registrationForm.controls['street'].value,
//   flat: this.registrationForm.controls['flat'].value,
//   building: this.registrationForm.controls['building'].value,
//   zip_code: this.registrationForm.controls['zip_code'].value,
//   company_logo: this.companyLogo,
//   portfolio: this.portfolio,
//   project_photo: this.project

// }

// this.userAuthenticationService.postEditProfile(getParams, postParams).subscribe({
//   next: (response) => {
//     if (response.status === 200) {
//       if (response.body.status == 200) {
//         this.toastr.success(response.body.message)
//         this.configSettings.setIsUserLoggedIn(true)
        
//         localStorage.removeItem('userDetails')
//         this.configSettings.setLocalStorage('userDetails', response?.body?.data)
//         this.configSettings.setUserTypes()
//         this.router.navigate(['/choose-plan'])
//       }
//     }

//   },
//   error: (err) => {
//     this.toastr.error(err.error.message)
//   }
// })