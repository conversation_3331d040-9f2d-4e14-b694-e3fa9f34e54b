<section class="container mx-auto px-4 md:px-12 lg:px-24">
  <div class="container px-6  md:px-12 lg:px-24 bg-white my-14 pt-14 pb-24 rounded-lg">
    <form [formGroup]="userInformation" (ngSubmit)="getuserFormInfo(userInformation)">
      <div class="grid grid-cols-2 gap-x-12 lg:gap-x-20 xl:gap-x-32 gap-y-6 mb-12">
        <div class="col-span-full mb-1">
          <p *ngIf="userTypeId ==='AG'" class=" text-xl text-primary font-bold">{{"Agent Details" | translate}}</p>
          <p *ngIf="userTypeId ==='AR'" class=" text-xl text-primary font-bold">{{"Architect Details" | translate}}
          </p>
        </div>
        <!-- 
        <div *ngIf="userTypeId ==='2'" class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Company Name" | translate}}</p>
          <input formControlName="companyName" type="text" placeholder=""
            class="w-full bg-lightGray outline-none px-2 py-3">
        </div> -->

        <div *ngIf="userTypeId =='AG'" class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Agency Name" | translate}}({{"English" | translate}})<span
              class="text-red-600">*</span></p>
          <input formControlName="agencyNameEn" type="text" class="w-full bg-lightGray outline-none px-2 py-3">
        </div>
        <div *ngIf="userTypeId =='AG'" class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Agency Name" | translate}}({{"Arabic" | translate}})<span
              class="text-red-600">*</span></p>
          <input formControlName="agencyNameAr" type="text" class="w-full bg-lightGray outline-none px-2 py-3">
        </div>

        <div *ngIf="userTypeId =='AR'" class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Architect Name" | translate}}({{"English" | translate}})<span
              class="text-red-600">*</span></p>
          <input formControlName="architectNameEn" type="text" placeholder=""
            class="w-full bg-lightGray outline-none px-2 py-3">
        </div>
        <div *ngIf="userTypeId =='AR'" class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Architect Name" | translate}}({{"Arabic" | translate}})<span
              class="text-red-600">*</span></p>
          <input formControlName="architectNameAr" type="text" placeholder=""
            class="w-full bg-lightGray outline-none px-2 py-3">
        </div>

        <div class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Contact email" | translate}}<span class="text-red-600">*</span></p>
          <input formControlName="email" type="text" placeholder="" class="w-full bg-lightGray outline-none px-2 py-3">
          <!-- <div *ngIf="userInformation.get('email')?.invalid && userInformation.get('email')?.touched">
            <small
              *ngIf="(userInformation.get('email')?.errors && 
              userInformation.get('email')?.hasError('pattern')) || (userInformation.get('email')?.invalid && userInformation.get('email')?.touched)"
              class="text-red-700 "></small>
          </div> -->
        </div>
        <!-- <div class="grid grid-cols-4 gap-2" > -->
        <!-- <div class="flex justify-between col-span-1 md:col-span-1 flex-col gap-2">
           <p class="text-primary">{{"Phone Code" | translate}}<span
          class="text-red-600">*</span></p>
        <span class="downarrow"><img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
            class="rtl:left-[10px] rtl:right-[unset]">
        </span>
        <select formControlName="countryCode" class="post-property-dropdown"  name="country" class="w-full bg-lightGray outline-none px-2 py-3"
         >
          <option  (click)="getCountries()"  *ngFor="let country of countryList"  [ngClass]="{'hidden': country.is_active =='No'}"> +{{country.phonecode}}</option>
        </select>
      
        </div> -->
        <div class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Phone Number" | translate}}<span class="text-red-600">*</span></p>
          <input formControlName="phoneNumber" type="text" placeholder="" maxlength="12"
            class="w-full bg-lightGray outline-none px-2 py-3">
          <!-- <div *ngIf="userInformation.get('phoneNumber')?.invalid && userInformation.get('phoneNumber')?.touched">
            <small
              *ngIf="(userInformation.get('phoneNumber')?.errors && 
              userInformation.get('phoneNumber')?.hasError('pattern')) || (userInformation.get('phoneNumber')?.invalid && userInformation.get('phoneNumber')?.touched)"
              class="text-red-700 "></small>
          </div> -->
        </div>
        <!-- </div>
      <div class="grid grid-cols-4 gap-2" > -->
        <!-- <div class="flex justify-between col-span-1 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Phone Code" | translate}}<span
         class="text-red-600">*</span></p>
       <span class="downarrow"><img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
           class="rtl:left-[10px] rtl:right-[unset]">
       </span>
       <select formControlName="countryCode" class="post-property-dropdown"  name="country" class="w-full bg-lightGray outline-none px-2 py-3"
        >
         <option  (click)="getCountries()"  *ngFor="let country of countryList"  [ngClass]="{'hidden': country.is_active =='No'}"> +{{country.phonecode}}</option>
         

       </select>-->




        <div class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Whatsapp Number" | translate}}<span class="text-red-600">*</span></p>
          <input formControlName="wtspNumber" type="text" placeholder="" maxlength="12"
            class="w-full bg-lightGray outline-none px-2 py-3">
          <!-- <div *ngIf="userInformation.get('wtspNumber')?.invalid && userInformation.get('wtspNumber')?.touched">
            <small
              *ngIf="(userInformation.get('wtspNumber')?.errors && 
                userInformation.get('wtspNumber')?.hasError('pattern')) || (userInformation.get('wtspNumber')?.invalid && userInformation.get('wtspNumber')?.touched)"
              class="text-red-700 "></small>
          </div> -->
        </div>



        <div class="flex col-span-2 md:col-span-1 flex-col gap-2 ">
          <p class="text-primary">{{"Expertise" | translate}}<span class="text-red-600">*</span></p>
          <span *ngIf="listOfUserExpertise<=0" class="downarrow"><img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
              class="rtl:left-[10px] rtl:right-[unset]">
          </span>
          <ng-select formControlName="expertise" (add)="addExpertise($event)" (remove)="removeExpertise($event)"
            (clear)="listOfUserExpertise=[]" [multiple]="true" [closeOnSelect]="false" class="post-property-dropdown "
            placeholder="{{'Choose expertise' | translate}}">
            <!-- <ng-option *ngFor="let expertise of expertiseList" [value]="expertise.id">{{expertise.name_en}}</ng-option> -->
            <ng-option *ngFor="let expertise of expertiseList" [value]="expertise.id"
              class="bg-inherit text-black">
              <span>{{expertise['name_' + lang]}}</span>
            </ng-option>
          </ng-select>
        </div>

        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Country" | translate}}<span class="text-red-600">*</span></p>
          <span class="downarrow"><img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
              class="rtl:left-[10px] rtl:right-[unset]">
          </span>
          <ng-select formControlName="country" class="post-property-dropdown" [readonly]="true" name="country"
            placeholder="{{'Choose country' | translate}}">
            <ng-option *ngFor="let country of countryList" [value]="country.country_id">{{country.name}}</ng-option>
          </ng-select>
        </div>

        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"State" | translate}}<span class="text-red-600">*</span></p>
          <span class="downarrow"><img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
              class="rtl:left-[10px] rtl:right-[unset]">
          </span>
          <ng-select formControlName="state" class="post-property-dropdown " (change)="getAreas($event)"
            placeholder="{{'Choose State' | translate}}">
            <ng-option *ngFor="let state of stateList" [value]="state.state_id">{{state.name}}</ng-option>
          </ng-select>
        </div>

        <div class="flex col-span-2 md:col-span-1 flex-col gap-2 ">
          <p class="text-primary">{{"Area" | translate}}<span class="text-red-600">*</span></p>
          <span class="downarrow"><img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
              class="rtl:left-[10px] rtl:right-[unset]">
          </span>
          <ng-select formControlName="area" class="post-property-dropdown" placeholder="{{'Choose Area' | translate}}">
            <ng-option *ngFor="let area of areaList" [value]="area.area_id">{{area.name}}</ng-option>
          </ng-select>
        </div>

        <div class="col-span-full mt-10">
          <p class="mb-1 text-xl text-primary font-bold">{{"About Us" | translate}}<span class="text-red-600">*</span>
          </p>
        </div>

        <div class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <textarea cols="30" rows="5" type="text" placeholder="In English" formControlName="aboutUsEn"
            class="w-full bg-lightGray outline-none px-2 py-3 placeholder:text-sm resize-none"></textarea>
        </div>

        <div class="flex justify-between col-span-2 md:col-span-1 flex-col gap-2">
          <textarea cols="30" rows="5" type="text" dir="rtl" placeholder="العربية" formControlName="aboutUsAr"
            class="w-full bg-lightGray outline-none px-2 py-3 placeholder:text-sm resize-none"></textarea>
        </div>

        <!-- ////////////////////////////////////////////////////////////////////////////////////// -->
        <div class="col-span-full mt-10">
          <p class=" text-xl text-primary font-bold">{{"Address Details" | translate}}<span
              class="text-red-600">*</span></p>
        </div>

        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Street Name/Street No" | translate}}<span class="text-red-600">*</span></p>
          <input formControlName="street" type="text" placeholder="" class="w-full bg-lightGray outline-none px-2 py-3">
        </div>

        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Building/Building No." | translate}}<span class="text-red-600">*</span></p>
          <input formControlName="building" type="text" placeholder=""
            class="w-full bg-lightGray outline-none px-2 py-3">
        </div>

        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Flat" | translate}}<span class="text-red-600">*</span></p>
          <input formControlName="flat" type="text" placeholder="" class="w-full bg-lightGray outline-none px-2 py-3">
        </div>

        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Zip Code" | translate}}<span class="text-red-600">*</span></p>
          <input formControlName="zipCode" type="text" placeholder="" maxlength="8"
            class="w-full bg-lightGray outline-none px-2 py-3">
          <!-- <div *ngIf="userInformation.get('zipCode')?.invalid && userInformation.get('zipCode')?.touched">
            <small
              *ngIf="(userInformation.get('zipCode')?.errors && 
                userInformation.get('zipCode')?.hasError('pattern')) || (userInformation.get('zipCode')?.invalid && userInformation.get('zipCode')?.touched)"
              class="text-red-700 "></small>
          </div> -->
        </div>
        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Address Line" | translate}}({{"English" | translate}})<span
              class="text-red-600">*</span></p>
          <input formControlName="address_line_en" type="text" placeholder=""
            class="w-full bg-lightGray outline-none px-2 py-3">
        </div>
        <div class="flex col-span-2 md:col-span-1 flex-col gap-2">
          <p class="text-primary">{{"Address Line" | translate}}({{"Arabic" | translate}})<span
              class="text-red-600">*</span></p>
          <input formControlName="address_line_ar" dir="rtl" type="text" placeholder=""
            class="w-full bg-lightGray outline-none px-2 py-3">
        </div>

        <div class="col-span-full ">
          <p class="mb-3 text-xl text-primary font-bold">{{"Upload Documents" | translate}}<span
              class="text-red-600">*</span></p>
        </div>


        <div class="mb-6 col-span-full">
          <p class="mb-3 text-sm">{{"Upload your Agency Logo" | translate}}</p>
          <div class="flex gap-5">
            <div *ngIf="companyLogo"
              class="border-2 border-[#c8c8c8] border-dashed  p-6 flex flex-wrap  max-w-[77%] gap-3">
              <div class="relative">
                <img [src]="companyLogo" class=" object-cover h-32 w-36 rounded-sm shadow-md">
                <img (click)="removeImage()" src="../../../assets/icons/remove-keyword.svg"
                  class="h-4 w-4 absolute -top-2 -right-2 hover:cursor-pointer">
              </div>
            </div>
            <div class="drag-here-img h-44 w-44 border border-[#969696] p-2 relative overflow-hidden">
              <div class="text-[.7rem] text-[#bdbdbd] absolute top-[45%] left-0 right-0">
                <!-- <div class=" text-center mx-auto">{{"Drag a product image here" | translate}}</div> -->
                <div class="relative text-center or"><span class="px-2 relative bg-white z-[3]">or</span></div>
              </div>
              <input #companyLogoInput formControlName="file" class="block h-full absolute inset-0 opacity-0 z-[1]"
                id="file" type="file" multiple="" (change)="addCompanyLogo($event)">
              <div
                class="absolute bottom-4 z-[2]  text-center font-normal cursor-pointer text-[.65rem] right-0 left-0 mx-3 py-1 border-2 bg-gray-50 border-[#969696] text-primary rounded-md"
                (click)="addCompanyLogoBtn()">{{"Select from device" | translate}}</div>
            </div>
          </div>
        </div>
      </div>
      <button
        class="border block mx-auto border-accent text-accent font-bold  ml-auto px-10 py-3 text-center cursor-pointer hover:bg-accent hover:text-white transition duration-150">
        {{"Submit" | translate}}</button>
    </form>
  </div>
</section>