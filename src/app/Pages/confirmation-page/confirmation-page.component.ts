import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { CheckUserSubscriptionsService } from 'src/app/Services/Check-user-subscriptions/check-user-subscriptions.service';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-confirmation-page',
  templateUrl: './confirmation-page.component.html',
  styleUrls: ['./confirmation-page.component.scss']
})
export class ConfirmationPageComponent implements OnInit {

  constructor(
    private activatedRoute: ActivatedRoute,
    private configSettings: configSettings,
    private checkUserSubscriptionsService: CheckUserSubscriptionsService,
    private toastr: ToastrService,
    private router: Router,
    public translateService: TranslateService
  ) { }

  PaymentID: any
  PostDate: any
  Ref: any
  Result: any
  TrackID: any
  TranID: any
  UDF6: any
  UDF7: any
  amount: any

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe(params => {
      console.log(params)
      this.PaymentID = params.paymentId
      this.PostDate = params.PostDate
      this.Ref = params.Ref
      this.Result = params.Result
      this.TrackID = params.TrackID
      this.TranID = params.TranID
      this.UDF6 = params.UDF6
      this.UDF7 = params.UDF7
      this.amount = params.UDF5
      this.subMess()
    })
  }

  subMess(){
    if (this.Result === 'CAPTURED') {
      this.translateService.get('Your plan has been added successfully ')
      .subscribe(res => {
        this.toastr.success('', res);
      })
    }
    else {
      this.translateService.get('Your payment has been  failed,try again').subscribe(res => {
        this.toastr.error('', res);
      })
    }
  }


  post(postType: string, userTypeID: number) {
    this.configSettings.setShowLoader(true);



    const getParams = {
      id: this.configSettings.getUserID(),
      user_type_id: userTypeID
    }

    this.checkUserSubscriptionsService.checkSubscriptionValidity(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.configSettings.setShowLoader(false);
            

            if (postType == "PT") {

              localStorage.setItem('maxPhotos', JSON.stringify(response.body.data.max_photos_per_post));
              this.router.navigate(['/post-property'])
            }


            if (postType == "PR") {

              localStorage.setItem('maxPhotos', JSON.stringify(response.body.data.max_photos_per_post));
              this.router.navigate(['/post-project'])
            }


          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
        this.configSettings.setShowLoader(false);

      }
    })

  }
}
