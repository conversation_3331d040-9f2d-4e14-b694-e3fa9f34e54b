<div *ngIf="Result === 'CAPTURED'"class='container mx-auto px-4 md:px-32 lg:px-52 pt-4 pb-8 '>
    <div
        class="bg-white rounded-md px-3 py-3  xl:px-4 2xl:px- min-h-[40rem] flex items-center justify-center flex-col">
        <div class="h-16 w-16 mx-auto">
            <img class="block mx-auto h-full w-full" src="../../../assets/images/finish.webp" alt="">
        </div>
        <h1 class=" text-primary text-lg md:text-xl  lg:text-2xl font-medium text-center mt-3 mb-6">{{"Payment successful" | translate}}</h1>
        <h5 class=" text-base md:text-lg  lg:text-xl font-medium   text-white bg-primary py-3 px-3 sm:px-4 w-full md:w-4/5 xl:w-3/5 2xl:w-1/2">{{"Payment Summary" | translate}}</h5>
        <div class=" text-[11px] sm:text-sm border font-medium text-primary border-primary mt-3  mb-8  px-3 sm:px-4  w-full md:w-4/5  xl:w-3/5 2xl:w-1/2 overflow-hidden">
            <ul class="w-full border-b border-primary flex py-3 justify-between">
                <li>{{"Payment ID" | translate}}</li>
                <li class="text-[#5a5a5a] ">{{PaymentID}}</li>
            </ul>
            <ul class="w-full border-b border-primary flex py-3 justify-between">
                <li>{{"Payment Amount" | translate}}</li>
               
                <li class="text-[#5a5a5a] ">{{amount | number:'0.3'}}/{{'KD'|translate}}</li>

            </ul>
            <ul class="w-full border-b border-primary flex py-3 justify-between">
                <li>{{"Captured on" | translate}}</li>
                <li class="text-[#5a5a5a]">{{PostDate}}</li>
            </ul>
            <ul class="w-full border-b border-primary flex py-3 justify-between">
                <li>{{"Reference ID" | translate}}</li>
                <li class="text-[#5a5a5a] ">{{Ref}}</li>
            </ul>
            <ul class="w-full border-b border-primary flex py-3 justify-between">
                <li>{{"Track ID" | translate}}</li>
                <li class="text-[#5a5a5a] ">{{TrackID}}</li>
            </ul>
            <ul class="w-full   flex py-3 justify-between">
                <li>{{"Transaction ID" | translate}}</li>
                <li class="text-[#5a5a5a] ">{{TranID}}</li>
            </ul>
        </div>

        
        <button *ngIf="UDF7 ==='1'" (click)="post('PT',1)"
            class="block px-7 py-3 bg-accent hover:bg-accentDark text-white  md:text-lg  lg:text-xl rounded-md">
            {{"Continue to post property" | translate}}</button>
        <button *ngIf="UDF7==='3'" (click)="post('PT',3)"
            class="block px-7 py-3 bg-accent hover:bg-accentDark text-white md:text-lg  lg:text-xl rounded-md">
            {{"Continue to post property" | translate}}</button>
        <button *ngIf="UDF7 ==='4'" (click)="post('PR',4)"
            class="block px-7 py-3 bg-accent hover:bg-accentDark text-white md:text-lg  lg:text-xl rounded-md">
            {{"Continue to post project" | translate}}</button>
        <button *ngIf="UDF6 ==='PT'" [routerLink]="['/my-account/my-property-listing']"
            class="block px-7 py-3 bg-accent hover:bg-accentDark text-white md:text-lg  lg:text-xl rounded-md">
            {{"Check all my properties" | translate}}</button>
        <button *ngIf="UDF6 ==='PJ'" [routerLink]="['/my-account/my-projects']"
            class="block px-7 py-3 bg-accent hover:bg-accentDark text-white md:text-lg  lg:text-xl rounded-md">
            {{"Check all my projects" | translate}}</button>
    </div>
</div>
<div *ngIf="Result !== 'CAPTURED'" class='container mx-auto lg:px-52 pt-4 pb-8 '>
    <div
        class="bg-white rounded-md mx-4 px-12 py-3 xl:px-4 2xl:px-12 min-h-[40rem] flex items-center justify-center flex-col">
        <div class="h-16 w-16 mx-auto">
            <img class="block mx-auto h-full w-full" src="../../../assets/icons/error-icon.png" alt="">
        </div>
        <h1 class="text-primary text-lg md:text-xl  lg:text-2xl font-medium text-center mt-3 mb-6">{{"Payment failed" | translate}}</h1>
        <button [routerLink]="['/my-account/my-property-listing']"
            class="block px-7 py-3 bg-accent hover:bg-accentDark text-white md:text-lg  lg:text-xl rounded-md">{{"Try Again" |
            translate}}</button>
    </div>
</div>