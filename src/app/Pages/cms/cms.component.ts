import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, ParamMap, Router } from '@angular/router';
import { CmsService } from 'src/app/Services/Cms/cms.service';

@Component({
  selector: 'app-cms',
  templateUrl: './cms.component.html',
  styleUrls: ['./cms.component.scss']
})
export class CmsComponent implements OnInit {

  content?: string;
  title: any
  id?: any;
  teams: any
  constructor(
    private activateRoute: ActivatedRoute,
    private router: Router,
    private cmsService: CmsService,
  ) { }

  ngOnInit(): void {

    this.activateRoute.paramMap.subscribe((params: ParamMap) => {
      let type = params.get('type');
      switch (type) {
        case 'about-us': this.id = 'A'; break;
        case 'terms-and-condition': this.id = 'TC'; break;
        case 'privacy-policy': this.id = 'PP'; break;
        default: this.router.navigate(['/home']); break;
      }
      this.getCms();
    });
  }

  getCms() {

    const parmas = {
      type: this.id
    }

    this.cmsService.getCms(parmas).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.content = response.body.data.content.content_en
            this.title = response.body.data.content.title_en
            this.teams = response.body.data.teams
          }
        }

      },
      error: (err) => {
      }
    })
  }
}
