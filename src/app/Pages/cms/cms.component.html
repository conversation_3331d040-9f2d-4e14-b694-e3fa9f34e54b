
<div class="fw container mx-auto px-6  md:px-28 lg:px-10 xl:px-40 2xl:px-36 py-8 font-oxygen text-white min-h-vh70 " >
    <h1 [innerHTML]="title" class="text-2xl mb-4"></h1>
    <p [innerHTML]="content"></p>
    <div  *ngIf="id == 'A'" class="border-0.5 border-[#707070] rounded-sm bg-[#1936548a] px-10 2xl:px-40 text-center py-16 my-20">
        <h5 class="text-white text-4xl xl:text-7xl font-oxygen font-normal py-4 pb-20 text-center">{{"Our Team" | translate}}</h5>
        <div class="grid grid-cols-12 gap-y-20 lg:gap-20">
          
            <div class="col-span-12 lg:col-span-6 bg-white rounded-3xl py-5 xl:py-0" *ngFor="let team of teams">
                <div class="relative h-0 bottom-16 block ">
                    <img src="{{team?.image}}" alt="" class="mx-auto object-cover w-44 xl:w-60 aspect-square rounded-full">
                </div>
                <div class="pt-36 md:pt-60 lg:pt-52  pb-8">    
                    <h5 class="text-primary text-xl xl:text-3xl font-oxygen font-bold py-2">{{team?.name}}</h5>
                    <p class="text-gray-400 text-lg xl:text-2xl ">{{team?.designation}}</p>
                </div>  
            </div>                      
        </div>
    </div>
</div>

<!-- <section>

    <div class="container mx-auto px-4 sm:px-14 md:px-24 py-8 font-oxygen" >
        <div>


            </div>
            
        </div>
       
    </div>


   
</section> -->