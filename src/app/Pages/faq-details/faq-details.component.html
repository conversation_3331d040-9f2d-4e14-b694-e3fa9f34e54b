<div class="container mx-auto px-6 sm:px-14 md:px-24 xl:px-48 pb-16">
    <h2 class="text-white text-center text-4xl md:text-4xl lg:text-5xl xl:text-6xl py-5">
        {{"Frequently Asked Questions" | translate}}</h2>
    <h4 class="text-white font-semibold text-2xl mt-5">
        {{"How can we help you?"| translate}}</h4>
    <div class="grid grid-cols-12 gap-4 mt-5 ">
        <div class="col-span-8 lg:col-span-10">
            <input type="text"
                class="h-12 w-full rounded-md focus:outline-none text-xl placeholder:text-slate-400 placeholder:text-sm px-5"
                placeholder="{{'Search your query here...…..' | translate}}" name="" id="">
        </div>
        <div class="col-span-4 lg:col-span-2">
            <button type="button"
                class="rounded-md bg-accent h-12 w-full text-white text-xl text-center flex justify-center items-center"><img
                    src="../../../assets/icons/search.svg" class="w-5 mx-1.5 relative " alt=""> 
                    {{"Search" | translate}}
                </button>
        </div>
    </div>

    <div *ngIf="faq" class="py-14">
        <p class="text-sm text-slate-400"><span class="text-[#2979CF] "><span class="cursor-pointer"
                    [routerLink]="['/faq']">{{"FAQs" | translate}}</span></span> > {{faq?.question}}</p>
    </div>

    <h1 *ngIf="faq" class="text-white text-3xl font-semibold pb-20">{{faq.question}}</h1>
    <p *ngIf="faq" class="text-[#B1BBC6] tracking-widest text-sm pb-16">{{faq?.answer}}</p>
    <div class="mt-28">
        <h2 class="text-white text-4xl lg:text-7xl text-center mb-12">{{"Related Topics" | translate}}</h2>
        <div class="grid grid-cols-12 gap-0 lg:gap-20">
            <div class="col-span-12 lg:col-span-6">
                <div class="">
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">{{"How do I login/signup?" | translate}}</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">{{"How do I create a new account?" | translate}}</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">{{"How do I reset my password?" | translate}}</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">{{"How do I subscribe from Find Property SMS...." | translate}}</p>
                    </div>
                </div>
            </div>
            <div class="col-span-12 lg:col-span-6">
                <div class="">
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">
                            {{"How do I login/signup?" | translate}}</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">
                            {{"How do I create a new account?" | translate}}</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">
                            {{"How to post a listing on findproperty.com?" | translate}}</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">
                            {{"When will my listing be visible on the website?" | translate}}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>