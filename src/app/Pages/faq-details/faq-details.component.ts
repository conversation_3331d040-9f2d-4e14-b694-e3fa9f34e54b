import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { FaqsService } from 'src/app/Services/FAQs/faqs.service';

@Component({
  selector: 'app-faq-details',
  templateUrl: './faq-details.component.html',
  styleUrls: ['./faq-details.component.scss']
})
export class FaqDetailsComponent implements OnInit {

  constructor(
    private faqsService: FaqsService,
    private activatedRoute: ActivatedRoute,
  ) {

  }

  id: any
  faq: any

  ngOnInit(): void {
    this.id = this.activatedRoute.snapshot.paramMap.get('id')
    this.getFAQsDetails()
  }


  getFAQsDetails() {

    let getParams = {
      id: this.id
    }

    this.faqsService.getFAQsDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.faq = response.body.data

          }
        }
      },
      error: (err) => {
      }
    })
  }
}
