import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PropertyListingRoutingModule } from './property-listing-routing.module';
import { PropertyListingComponent } from './property-listing.component';
import { FormsModule } from '@angular/forms';
import { PropertyCardModule } from 'src/app/Components/property-card/property-card.module';
import { MapModule } from 'src/app/Components/map/map.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [PropertyListingComponent],
  imports: [
    CommonModule,
    FormsModule,
    PropertyListingRoutingModule,
    PropertyCardModule,
    MapModule,
    NgSelectModule, TranslateModule
  ],
  exports: [PropertyListingComponent]
})
export class PropertyListingModule { }
