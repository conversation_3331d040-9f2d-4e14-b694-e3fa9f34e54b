<div *ngIf="locations" class="w-full py-3 px-0  xl:py-3 xl:px-3 border-b border-borderColor border-r-0 border-l-0"
    style="background:#082a51;">
    <div class="block lg:hidden container mx-auto py-4 px-4 sm:px-14 md:px-24 z-10">
        <div (click)="showfilter()"
            class="border bg-[#082a51] rtl:w-3/5  ltr:w-[45%] rtl:sm:w-2/5 ltr:sm:w-1/3 md:w-1/4 border-[#ABBED4]  flex justify-center items-center ">
            <img _ngcontent-ksw-c106="" src="../../../assets/icons/filter-image.svg"
                class="h-4 w-4 rtl:mr-2 rtl:ml-[unset] ml-2">
            <p *ngIf="showFilters == false"
                class=" bg-[#082a51] w-full focus:outline-none h-10  md:text-xs lg:text-xs align-middle py-2.5 md:py-3 xl:text-sm text-slate-100 px-2">
                {{'More Filter'| translate}}</p>
            <p *ngIf="showFilters == true"
                class=" bg-[#082a51] w-full focus:outline-none h-10  md:text-xs lg:text-xs xl:text-sm text-slate-100 align-middle py-2.5 md:py-3 px-2">
                {{'Short Filter'| translate}} </p>
            <img [ngClass]="showFilters ? '-scale-y-100' : ''" src="../../../assets/icons/property-listing-arrow.svg"
                class="w-3 rtl:ml-3 ltr:mr-3  z-10  ">
        </div>
    </div>
    <div *ngIf="showFilters" class="container mx-auto px-4 sm:px-14 md:px-24 z-10">
        <form #filterSearch="ngForm" (ngSubmit)="filterSearchFuc(filterSearch.value)">
            <div class="grid md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-7 gap-4 mb-2">
                <div class="cols-span-1">
                    <p class="text-base text-white mb-2">{{'State' | translate}}</p>

                    <div class="w-full  relative">
                        <div class="flex flex-col gap-1  flex-grow-2">

                            <div class="relative">
                                <ng-select name="areaid" class="property-listing-dropdown "
                                    placeholder="{{'Choose State'| translate}}" [(ngModel)]="areaID">
                                    <ng-option *ngFor="let item of locations" [value]="item.id">{{item.name}}
                                    </ng-option>
                                </ng-select>
                                <img src="../../../assets/icons/property-listing-arrow.svg"
                                    class="w-2.5 absolute rtl:left-3 rtl:right-[unset] right-3 top-1/2 -translate-y-1/2 z-10  ">
                            </div>
                        </div>

                    </div>
                </div>
                <div class="cols-span-1">
                    <p class="text-base text-white mb-2">{{"Property for" | translate}}</p>
                    <div class="relative">
                        <ng-select name="propertyForID" class="property-listing-dropdown"
                            placeholder="{{'Property for'| translate}}" [(ngModel)]="property_for">
                            <ng-option [value]="''">{{"All"| translate}}</ng-option>
                            <ng-option *ngFor="let item of allPropertyForType" [value]="item.id">{{item.name |
                                titlecase}}</ng-option>
                        </ng-select>
                        <img src="../../../assets/icons/property-listing-arrow.svg"
                            class="w-2.5 absolute rtl:left-3 rtl:right-[unset] right-3 top-1/2 -translate-y-1/2 z-10  ">
                    </div>
                </div>
                <div class="cols-span-1">
                    <p class="text-base text-white mb-2">{{"Category type" | translate}}</p>

                    <div class="relative">
                        <ng-select name="categoryid" class="property-listing-dropdown "
                            placeholder="{{'Type of category'| translate}}" [(ngModel)]="category_type">
                            <ng-option [value]="''">{{"All"| translate}}</ng-option>
                            <ng-option *ngFor="let item of allCategories" [value]="item.id">{{item.name |
                                titlecase}}</ng-option>
                        </ng-select>
                        <img src="../../../assets/icons/property-listing-arrow.svg"
                            class="w-2.5 absolute rtl:left-3 rtl:right-[unset] right-3 top-1/2 -translate-y-1/2 z-10  ">
                    </div>

                </div>
                <div class="cols-span-1">
                    <p class="text-base text-white mb-2">{{"Property type" | translate}}</p>
                    <div class="relative">
                        <ng-select name="propertyTypeID" class="property-listing-dropdown "
                            placeholder="{{'Property Type'| translate}}" [(ngModel)]="property_type">
                            <ng-option [value]="''">{{"All"| translate}}</ng-option>
                            <ng-option *ngFor="let item of allPropertyTypes" [value]="item.id">{{item.name |
                                titlecase}}</ng-option>
                        </ng-select>
                        <img src="../../../assets/icons/property-listing-arrow.svg"
                            class="w-2.5 absolute rtl:left-3 rtl:right-[unset] right-3 top-1/2 -translate-y-1/2 z-10  ">
                    </div>
                </div>
                <div class="cols-span-1">
                    <p class="text-base text-white mb-2">{{"Price" | translate}}</p>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <input placeholder="{{'Min'| translate}} {{'KD' | translate}}" [(ngModel)]="min_price"
                                type="text" name="minPrice" id=""
                                class="border border-[#ABBED4] bg-[#082a51] w-full focus:outline-none h-10  md:text-xs lg:text-xs xl:text-sm text-slate-100 px-2">
                        </div>
                        <div>
                            <input placeholder="{{'Max'| translate}} {{'KD' | translate}}" [(ngModel)]="max_price"
                                type="text" name="maxPrice" id=""
                                class="border border-[#ABBED4] bg-[#082a51] w-full focus:outline-none h-10  md:text-xs lg:text-xs xl:text-sm text-slate-100 px-2">
                        </div>
                    </div>
                </div>
                <div class="cols-span-1">
                    <p class="text-base text-white mb-2">{{"Beds" | translate}}</p>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="border border-[#ABBED4]  flex justify-center items-center ">
                            <input placeholder="{{'Min'| translate}}" [(ngModel)]="min_beds" type="text"
                                name="minBeds" id=""
                                class="bg-[#082a51] w-full focus:outline-none h-10  md:text-xs lg:text-xs xl:text-sm text-slate-100 px-2"><img
                                _ngcontent-ksw-c106="" src="../../../assets/icons/rooms.svg"
                                class="h-4 w-4 rtl:ml-2 rtl:mr-[unset] mr-2">
                        </div>
                        <div class="border border-[#ABBED4]  flex justify-center items-center ">
                            <input placeholder="{{'Max'| translate}}" [(ngModel)]="max_beds" type="text"
                                name="maxBeds" id=""
                                class=" bg-[#082a51] w-full focus:outline-none h-10  md:text-xs lg:text-xs xl:text-sm text-slate-100 px-2">
                            <img _ngcontent-ksw-c106="" src="../../../assets/icons/rooms.svg"
                                class="h-4 w-4 rtl:ml-2 rtl:mr-[unset] mr-2">
                        </div>

                    </div>
                </div>
                <div class="cols-span-2">
                    <p class="mb-3">&nbsp;</p>
                    <button class="bg-accent h-10 w-full grid justify-center items-center"><img
                            src="../../../assets/icons/search.svg" alt="" class="w-4 h-4 text-center block"></button>

                </div>
            </div>
        </form>
    </div>
</div>
<!-- (window:resize)="onResize($event)" -->
<div (window:resize)="onResize($event)" class="container mx-auto px-4 sm:px-14 md:px-24">
    <div *ngIf="total_properties" class="grid grid-cols-12 gap-4 mt-10">
        <div class="col-span-12 lg:col-span-7 px-2">
            <p class="text-sm text-white mt-3">{{total_properties}} {{"Properties"| translate}}
                <span *ngIf="rentOrSale">{{'for'| translate}} {{rentOrSale}} {{'in Kuwait' | translate}}</span>
            </p>
        </div>
        <div class="col-span-12 lg:col-span-5 px-1">
            <div class="grid grid-cols-12 gap-4">
                <div class="col-span-8">
                    <div class="grid grid-cols-2 gap-4 h-10 justify-center items-center px-1 sm:px-2  md:px-4 lg:px-1 xl:px-4"
                        style="background:#1b4574;">
                        <div (click)="showMap = false" class="flex items-center text-right">
                            <span *ngIf="!showMap " class="rtl:pt-2 py-1 "><img src="../../../assets/icons/list-yellow.svg" alt=""
                                    class="w-5"></span>
                            <span *ngIf="showMap " class="rtl:pt-2 py-1 "><img src="../../../assets/icons/list-white.svg" alt=""
                                    class="w-5"></span>
                            <button type="button" [ngClass]="!showMap ? 'text-accent' : ''"
                                class="text-white text-xs xl:text-sm text-right font-semibold px-1.5 sm:px-2">
                                {{"List View" | translate}}</button>
                        </div>
                        <div (click)="showMap = true" class="text-right flex items-center">
                            <span *ngIf="showMap" class="rtl:pt-1"><img src="../../../assets/icons/location-icon-yellow.svg" alt=""
                                    class="w-4 "></span>
                            <span *ngIf="!showMap" class="rtl:pt-1"><img src="../../../assets/icons/location-icon-white.svg" alt=""
                                    class="w-4 "></span>
                            <button type="button" [ngClass]="showMap ? 'text-accent' : ''"
                                class="text-white text-xs xl:text-sm font-semibold text-right px-1.5 sm:px-2">
                                {{"Map View" | translate}}</button>
                        </div>
                    </div>
                </div>
                <div class="col-span-4 relative ">
                    <div class="flex group">
                        <button (click)="sortDropdown = !sortDropdown"
                            class="hover:text-accent relative duration-150 flex justify-center text-white text-xs xl:text-sm items-center w-full hover:border-accent h-10 border border-accent px-1 xl:px-2">
                            {{"Sort By" | translate}}</button>
                        
                    </div>
                    <div *ngIf="sortDropdown === true"
                        class="border border-[#ABBED4] w-full absolute mt-1 bg-primary z-10">
                        <ul>
                            <li *ngFor="let item of allSortOptions"
                                class="text-white p-2 hover:bg-gray-700 cursor-pointer" (click)="onSortBy(item.id)">
                                {{item.name}}
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="pt-4 px-2 h-[34rem]" *ngIf="showMap && propertiesArr?.length > 0">
        <app-map [showSearch]="showSearch" [fitBounds]="true">
        </app-map>
    </div>

    <div class="">
        <div class="">
            <div *ngIf="propertiesArr?.length   > 0"
                class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-5 lg:space-y-0 mt-10">
                <div class="mx-1.5 py-2" *ngFor="let property of propertiesArr">


                    <app-property-card [property]="property" [property_for]="property_for"></app-property-card>


                </div>

            </div>

            <div *ngIf="propertiesArr?.length == 0" class="h-vh60 flex justify-center items-center text-xl text-white">
                {{"No properties available" | translate}}
            </div>

            <div *ngIf="propertiesArr?.length > 0 && propertiesArr?.length != total_properties"
                class="container mx-auto mt-10 mb-8 lg:mb-24">
                <button (click)="loadMore()"
                    class="flex text-sm items-center gap-3 mx-auto border border-offwhite text-offwhite px-9 py-3 hover:border-accent hover:bg-accent hover:text-white duration-300 ease-in-out">
                    {{"LOAD MORE" | translate}}
                </button>

            </div>
        </div>

    </div>

    <div (click)="sortDropdown = !sortDropdown" *ngIf="sortDropdown === true" class="fixed bg-primary opacity-10 inset-0 top-0 bottom-0"></div>