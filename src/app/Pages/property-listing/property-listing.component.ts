import { HttpClient } from '@angular/common/http';
import { compileClassMetadata } from '@angular/compiler';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { PropertyListingService } from 'src/app/Services/Properties/property-listing.service';

@Component({
  selector: 'app-property-listing',
  templateUrl: './property-listing.component.html',
  styleUrls: ['./property-listing.component.scss'],
})
export class PropertyListingComponent implements OnInit {
  constructor(
    private toastr: ToastrService,
    private propertyListingService: PropertyListingService,
    private configSettings: configSettings,
    private activatedRoute: ActivatedRoute,
  ) {
    // this.isSmallDevice = window.innerWidth < 1020;
    this.isMobile = window.innerWidth;

    if (window.innerWidth < 1024) {
      this.showFilters = false
      this.perpage = 14
    } else {
      this.showFilters = true
      this.perpage = 15
    }

  }

  showFilters: boolean = true;
  isSmallDevice: any

  lang: any
  // filter variables
  property_for: any

  countryID: any
  stateID: any
  areaID: any = ''; //area

  category_type: any = ''
  property_type: any = ''

  min_price: any;
  max_price: any;

  min_beds: any;
  max_beds: any;

  sort_by: any = 'ASC'

  total_properties: number = 0;

  total_pages: number = 0;

  recentSearchID: any = null

  menuSearchID: any

  propertiesArr: any;
  filters: any;

  is_searched: number
  allCategories: any
  allPropertyTypes: any
  allPropertyForType: any
  allSortOptions: any
  sortDropdown = false

  // map variables
  showMap: boolean = false
  showSearch = false


  userID = this.configSettings.getUserDetails() ? this.configSettings.getUserDetails().user_id : ''


  // property listing api
  perpage:number

  inital: boolean = true


  ngOnDestroy() {
    this.configSettings.clearSearch()
    this.configSettings.setMapDetails([])
  }

  ngOnInit(): void {


    this.configSettings.tiggerNotificationAPi()

    this.configSettings.setShowLoader(true)

    this.lang = this.configSettings.getLang()

    this.activatedRoute.queryParams.subscribe(params => {

      // console.log(params)

      this.property_for = params?.propertyFor == undefined ? '' : params?.propertyFor
      this.countryID = params?.countryID == undefined ? '' : params?.countryID
      this.stateID = params?.stateID == undefined ? '' : params?.stateID
      this.areaID = params?.areaID == undefined ? null : +params?.areaID
      this.category_type = params?.categoryType == undefined ? '' : params?.categoryType
      this.min_price = params?.min == undefined ? '' : params?.min
      this.max_price = params?.max == undefined ? '' : params?.max
      this.recentSearchID = params?.recentSearchID == '' ? '' : params?.recentSearchID

      // console.log(this.property_for, this.countryID, this.stateID, this.areaID, this.category_type, this.min_price, this.max_price)

      // to check if user searched from home page or not
      this.configSettings.getSearchType().subscribe(res => {
        this.is_searched = res
      })

      // checks which footer menu showFilters clicked (by default the res will be '')
      this.configSettings.getMenuType().subscribe(res => {
        this.menuSearchID = res
        // if (res === '') {
        // console.log(this.menuSearchID)
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: 'smooth'
        })
        this.initialSearch()
        // } else {
        // this.search() ////////////////////////////////////////////////
        // }
      })

      // this.initialSearch()



    })
  }

  rentOrSale: any = ''

  initialSearch() {

    this.configSettings.setShowLoader(true)

    if (this.property_for == 'S') {
      this.rentOrSale = "sale"
    } else if (this.property_for == 'R') {
      this.rentOrSale = "rent"
    } else {
      this.rentOrSale = ''
    }

    const getParams = {
      lang: this.lang,
      page: 1,
      per_page: this.perpage,
    };

    const postParams = {
      country_id: this.countryID,
      location_id: this.areaID != undefined ? this.areaID : '',
      property_for: this.property_for != undefined ? this.property_for : '',
      property_category_id: this.category_type != undefined ? this.category_type : '',
      min_price: this.min_price != undefined ? this.min_price : '',
      max_price: this.max_price != undefined ? this.max_price : '',
      min_beds: this.min_beds != undefined ? this.min_beds : '',
      max_beds: this.max_beds != undefined ? this.max_beds : '',
      sort_by: this.sort_by,
      menu_search_id: this.menuSearchID,
      recent_search_id: this.recentSearchID,
      property_type: this.property_type,

      is_search: this.is_searched,
      user_id: this.userID,

    }

    this.propertyListingService
      .postProperties(getParams, postParams)
      .subscribe({
        next: (response) => {
          if (response.status === 200) {
            if (response.body.status == 200) {
              this.configSettings.setShowLoader(false)
              this.configSettings.setSearchType(0)
              // this.configSettings.setMenuType('')
              this.propertiesArr = response?.body?.data?.properties;
              this.configSettings.setMapDetails(this.propertiesArr)
              this.total_properties = response?.body?.data?.totalItemCount

              if (this.inital == true) {
                this.filters = response?.body?.data?.filters;
                this.locations = this.filters.area
                this.allPropertyForType = this.filters.property_fors
                this.allCategories = this.filters.property_categories
                this.allPropertyTypes = this.filters.property_types
                this.allSortOptions = this.filters.sort
                this.inital = false
              }

            } else {
              this.toastr.warning('', response.body.message);
            }
          } else {
            this.toastr.error('', 'Something went wrong');
          }
        },
        error: (err) => {
          this.toastr.error('', err.error.message);
        }
      })
    this.menuSearchID = ''
  }





  // filters
  locations: any
  locationString: any

  onSortBy(id: any) {
    this.sort_by = id
    this.sortDropdown = false
    this.initialSearch()
    // this.search() /////////////////////////////////////////////////////////////////
  }

  loadMore() {
    this.perpage += 10
    this.initialSearch()
    // this.search() /////////////////////////////////////////////////////////////////
  }


  filterSearchFuc(data: any) {
    this.configSettings.setShowLoader(true);
    this.recentSearchID = ''
    this.menuSearchID = ''
    this.category_type = data.categoryid
    this.min_price = data.minPrice
    this.max_price = data.maxPrice
    this.min_beds = data.minBeds
    this.max_beds = data.maxBeds
    this.property_for = data.propertyForID
    this.property_type = data.propertyTypeID
    this.initialSearch()
    // this.search() //////////////////////////////////////////////////////////
  }



  showfilter() {
    if (this.showFilters == true) {
      this.showFilters = false
    } else {
      this.showFilters = true
    }
  }
  isMobile: any;
  onResize(e: any) {
    this.isMobile = e.target.innerWidth;
    // console.log(this.isMobile,'is mobile');

    if (this.isMobile < 1024) { this.showFilters = false; } else { this.showFilters = true; }

  }


  // search() {
  //   const getParams = {
  //     lang: this.lang,
  //     page: 1,
  //     per_page: this.perpage,
  //   };

  //   const postParams = {
  //     country_id: this.countryID,
  //     property_for: this.property_for != undefined ? this.property_for : '',
  //     location_id: this.areaID != undefined ? this.areaID : '',
  //     property_category_id: this.category_type != undefined ? this.category_type : '',
  //     min_price: this.min_price != undefined ? this.min_price : '',
  //     max_price: this.max_price != undefined ? this.max_price : '',
  //     min_beds: this.min_beds != undefined ? this.min_beds : '',
  //     max_beds: this.max_beds != undefined ? this.max_beds : '',
  //     sort_by: this.sort_by,
  //     menu_search_id: this.menuSearchID,
  //     property_type: this.property_type,
  //     is_search: this.is_searched,
  //     user_id: this.userID,

  //   }


  //   this.propertyListingService
  //     .postProperties(getParams, postParams)
  //     .subscribe({
  //       next: (response) => {
  //         if (response.status === 200) {
  //           if (response.body.status == 200) {
  //             this.configSettings.setShowLoader(false)

  //             this.propertiesArr = response?.body?.data?.properties;
  //             this.total_properties = response?.body?.data?.totalItemCount
  //             this.configSettings.setMapDetails(this.propertiesArr)
  //             // this.configSettings.setMenuType('')
  //             this.menuSearchID = ''
  //             window.scrollTo({
  //               top: 0,
  //               left: 0,
  //               behavior: 'smooth'
  //             })
  //           } else {
  //             this.toastr.warning('', response.body.message);
  //           }
  //         } else {
  //           this.toastr.error('', 'Something went wrong');
  //         }
  //       },
  //       error: (err) => {
  //         this.toastr.error('', err.error.message);
  //       }
  //     })
  //   this.menuSearchID = ''

  // }




}
