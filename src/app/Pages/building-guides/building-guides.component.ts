import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BuildingGuideService } from 'src/app/Services/Building-guides/building-guide.service';
import { configSettings } from 'src/app/Config/config.settings';
@Component({
  selector: 'app-building-guides',
  templateUrl: './building-guides.component.html',
  styleUrls: ['./building-guides.component.scss']
})
export class BuildingGuidesComponent implements OnInit {

  constructor(
    private buildingGuideService: BuildingGuideService,
    private toastr: ToastrService,
    private configSettings: configSettings
  ) { }
  // countryID: any
  countryIDArr: any = []
  // countryName: any
  countryNameArr: any = []
  countryPlaceholder: any = "Choose a Country"
  countriesDropdown: any

  countryIdFilter: any
  searchFilter: any = ''

  buildings: any
  chooseCountryDropbox = false

  @ViewChild('searchForm') searchForm?: ElementRef;

  ngOnInit(): void {
    this.configSettings.tiggerNotificationAPi()
    this.getBuildingGuides('', '')
  }

  chooseCity() {
    this.chooseCountryDropbox = !this.chooseCountryDropbox
  }

  clickedOutside() {
    this.chooseCountryDropbox = false
  }

  getBuildingGuides(countryIdFilter: any, searchFilter: any) {

    this.countryIdFilter = countryIdFilter
    this.searchFilter = searchFilter
    const getParams = { per_page: 9999 }
    const postParams = {
      country_id: this.countryIdFilter,
      search: this.searchFilter
    }



    this.buildingGuideService.getBuildingGuides(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.buildings = response.body.data.guides

            this.countriesDropdown = response.body.data.filters.countries
            this.countryNameArr = []
            for (let [key, value] of Object.entries(this.countriesDropdown)) {
              this.countryNameArr.push(value)
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
        this.buildings = []
      }
    })
  }

  option(e: any) {
    e.stopPropagation();
    this.countryPlaceholder = e.target.innerText
    this.countryIdFilter = e.target.id
    this.getBuildingGuides(this.countryIdFilter, this.searchFilter)
    this.chooseCountryDropbox = false
    // console.log(e)

    // this.getBuildingGuides(e.target.firstChild.id)
  }


  search() {
    this.searchFilter = this.searchForm?.nativeElement.value
    this.getBuildingGuides(this.countryIdFilter, this.searchFilter)
  }
}
