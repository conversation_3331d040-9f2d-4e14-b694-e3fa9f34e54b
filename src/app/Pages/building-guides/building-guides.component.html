<div>
    <div class="top-banner">
        <div>
            <img src="../../../assets/images/buliding-guides-baner.png"
                class="w-full aspect-[2/1] md:aspect-[3/1] xl:aspect-[4/1]" alt="">
        </div>
        <div class="container mx-auto px-4 sm:px-14 md:px-24 relative block h-0 bottom-40 md:bottom-56  lg:bottom-72 ">
            <div class=" mx-auto px-0 xl:px-16">
                <p class="text-white text-4xl md:text-6xl font-medium text-center pb-10 md:pb-16">{{"Building Guides" |
                    translate}}</p>
                <div class="grid grid-cols-12 gap-4 ">
                    <div class="col-span-8 lg:col-span-10">
                        <input #searchForm type="text"
                            class="h-10  md:h-10 lg:h-14 w-full rounded-md focus:outline-none text-xl placeholder:text-slate-400 placeholder:text-base px-5"
                            placeholder="{{'Search Location' | translate}}" name="" id="" (keyup.enter)="search()">
                    </div>
                    <div class="col-span-4  lg:col-span-2">
                        <button type="button"
                            class="rounded-md bg-accent h-10  md:h-10 lg:h-14 w-full text-white lg:text-xl text-center flex justify-center items-center gap-1.5"
                            (click)="search()"><img src="../../../assets/icons/search.svg"
                                class="w-4 md:w-5 rtl:mt-1  relative " alt="">
                            {{"Search" | translate}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-16">
        <div class="container mx-auto sm:px-14 md:px-24">
            <div class="text-center w-fit mx-auto">
                <div (click)="chooseCity()" clickedOutside (clickedOutside)="clickedOutside()"
                    class=" border border-zinc-400 rounded-sm text-gray-300 text-2xl relative cursor-pointer">
                    <div class="flex items-center justify-between gap-3  py-2 px-6 font-medium w-72">
                        <span>{{countryPlaceholder | translate}}</span><img src="../../../assets/icons/Polygon 4.svg"
                            alt="" class="w-5">
                    </div>
                </div>
                <div *ngIf="chooseCountryDropbox"
                    class="text-start z-20 w-[290px] font-medium border border-zinc-400 rounded-sm text-gray-300 text-xl absolute bg-primary transition-all duration-150">
                    <ul>
                        <li class="border-b border-zinc-400 hover:bg-blue-900">
                            <span id="0" class="px-6 py-2 w-full block capitalize"
                                (click)="option($event)">{{"All" | translate}}</span>
                        </li>
                        <li [id]="country.id" *ngFor="let country of countryNameArr" (click)="option($event)"
                            class="border-b border-zinc-400 hover:bg-blue-900 px-6 py-2 w-full block capitalize">
                            {{country.name | uppercase}}
                        </li>
                    </ul>
                </div>
                <!-- <p class=" flex  py-2 px-6 font-medium border border-zinc-400 rounded-sm text-gray-300 text-2xl">KUWAIT
                    CITY <span class="px-3 py-2"><img src="../../../assets/icons/Polygon 4.svg" alt=""
                            class="w-5"></span></p> -->
                <P class="justify-center  flex font-medium text-gray-300 text-2xl py-10 "><span class="px-2"><img
                            src="../../../assets/icons/buildings.svg" alt="" class="w-8"></span>{{buildings?.length}}
                    {{"Building(s)" | translate}}</P>
            </div>
        </div>
    </div>



    <!-- Grid Container -->
    <div class="grid grid-cols-12 md:grid-cols-9 lg:grid-cols-10 gap-4 
        px-2 md:px-24 lg:px-16 xl:px-40 2xl:px-52 pb-16">

        <!-- Building guide card component -->
        <div *ngFor="let building of buildings" class="col-span-6 md:col-span-3 lg:col-span-2">
            <div [routerLink]="['/building-details', building.id]"
                class="bg-gradient-to-t from-[#3d62b1]  to-[#eceaea] relative cursor-pointer">
                <div class="overflow-hidden h-92">
                    <img class="w-full h-full object-cover mix-blend-multiply hover:scale-105 transition duration-500"
                        src="{{building.thumbnail}}">
                </div>
                <div class="from-neutral-300 h-0">
                    <div class="p-2 block absolute bottom-0   ">
                        <!-- old styles relative  bottom-32 -->
                        <!-- <img class="w-12 md:w-14 lg:w-12 rounded-full " src="../../../assets/images/architectslogo.jpg"
                            alt=""> -->
                        <h3 class="md:text-sm lg:text-base font-semibold text-white uppercase">{{building.title}}</h3>
                        <!-- <div class="flex items-center gap-2 ">
                            <img src="../../../assets/icons/pin.png" class="w-2">
                            <p class="text-slate-300 text-sm ">Jaber Ahmad Block-3</p>
                        </div> -->
                        <!-- <h5 class="text-accent text-sm text-right">4000KD / Month</h5> -->
                    </div>
                </div>
            </div>
        </div>


        <!-- end of grid -->
    </div>






</div>