import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BuildingGuidesRoutingModule } from './building-guides-routing.module';
import { BuildingGuidesComponent } from './building-guides.component';
import { ClickedOutsideModule } from 'src/app/Directives/clicked-outside/clicked-outside.module';
import { ToastrModule } from 'ngx-toastr';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [BuildingGuidesComponent],
  imports: [
    CommonModule,
    BuildingGuidesRoutingModule,
    ClickedOutsideModule,
    ToastrModule,
    TranslateModule
  ]
})
export class BuildingGuidesModule { }
