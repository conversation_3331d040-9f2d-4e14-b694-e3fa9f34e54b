import { ViewportScroller } from '@angular/common';
import { AfterViewChecked, Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { AreaGuidesService } from 'src/app/Services/Area-guides/area-guides.service';
import { TranslateService } from '@ngx-translate/core';



@Component({
  selector: 'app-area-guides',
  templateUrl: './area-guides.component.html',
  styleUrls: ['./area-guides.component.scss']
})
export class AreaGuidesComponent implements OnInit, AfterViewChecked {

  // VARIABLES
  areaGuides: any
  areaDetails: any
  areaSpecifications: any
  areaLifeStyles: any
  propertiesLength: any

  areaGuideID: any = ''
  searchInput: any = ''
  areaGuideSearchList: any = []
  saerch: any = ''


  // map
  showSearch = false
  lat: any
  lng: any

  googleLatLon: any
  // scroll
  section: any
  lists: any
  lifeStyle: any;
  lang: string = this.configSettings.getLang()
  constructor(

    private areaGuidesService: AreaGuidesService,
    private toastr: ToastrService,
    private viewportScroller: ViewportScroller,
    private configSettings: configSettings,
    private route: ActivatedRoute,
    public translateService: TranslateService

  ) { }
  ngAfterViewChecked(): void {
    this.section = document.querySelectorAll('.section');
    this.lists = document.querySelectorAll('.list');
    // console.log(this.section)
  }

  ngOnInit(): void {
    this.configSettings.setShowLoader(true);
    this.configSettings.tiggerNotificationAPi()

    this.route.params.subscribe(params => {
      this.areaGuideID = params['id']
    })

    this.configSettings.setShowLoader(true);
    this.getAreaGuides(this.saerch)

    if (this.lang === 'ar') {
      // this.customOptions = { ...this.customOptions, rtl: true }
    }
  }

  customOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: false,
    pullDrag: false,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    margin: 25,
    rtl: false,
    autoWidth: true,
    responsive: {
      0: {
        items: 1,
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 3
      },
      1280: {
        items: 3
      },
      1536: {
        items: 3
      }
    },
    nav: false
  }

  featuredPropertyOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    autoWidth: false,
    // margin: 25,
    center: false,
    rtl: false,
    responsive: {
      0: {
        items: 1
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 3
      },
      1280: {
        items: 5
      },
      1536: {
        items: 5
      }
    },
    nav: false
  }



  getAreaGuides(search: any) {

    const getParams = {
      per_page: 9999
    }
    const postParams = {
      search: search,
    }

    this.areaGuidesService.areaGuides(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.areaGuides = response.body.data.areaGuides
            this.configSettings.setShowLoader(false);

            if (this.areaGuides.length < 3) {
              this.customOptions = { ...this.customOptions, loop: false }
            }
            if (this.areaGuideID === undefined) {
              this.getAreaDetails(this.areaGuides[0].id)
            } else {

              this.getAreaDetails(this.areaGuideID)
            }

            // this.configSettings.setShowLoader(false);

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }

  getAreaDetails(id: any) {
    this.configSettings.setShowLoader(true);

    const getParams = {

      area_guide_id: id
    }

    this.areaGuidesService.areaDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.configSettings.setShowLoader(false);
            this.areaDetails = response.body.data
            let latlonArr = this.areaDetails.latlon.split(',')
            this.googleLatLon = latlonArr.toString()
            this.lat = Number(latlonArr[0])
            this.lng = Number(latlonArr[1])
            this.areaSpecifications = response.body.data.areaSpecification
            this.areaLifeStyles = this.areaDetails.areaLifestyle
            this.areaLifeStyles.forEach((el: any) => {
              if (el.type == 'P') {
                this.propertiesLength = el.properties.length
                if (this.propertiesLength < 6) {
                  this.propertyGuideCarousal = { ...this.propertyGuideCarousal, loop: false }
                }
              }
            })
          }
        }
      },
      error: (err) => {
        this.configSettings.setShowLoader(false);
        this.toastr.error('', err.error.message);
      }
    })

  }

  showGuideDetails(id: any) {
    this.configSettings.setShowLoader(true);
    this.getAreaDetails(id)
  }


  propertyGuideCarousal: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    autoWidth: true,
    margin: 10,
    center: false,
    rtl: false,
    responsive: {
      0: {
        items: 1
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 5
      },
      1280: {
        items: 5
      },
      1536: {
        items: 5
      }
    },
    nav: false
  }











  scrollTo(el: any, currentTab: any) {
    el.scrollIntoView({ behavior: 'smooth', block: 'start' });
    this.currentActive = currentTab
  }

  @ViewChild('about') aboutElement: ElementRef;
  @ViewChild('lifestyle') lifestyleElement: ElementRef;
  @ViewChild('location') locationElement: ElementRef;
  // @ViewChild('about') aboutElement: ElementRef;

  public currentActive = 1;
  public aboutOffset: any;
  public locationOffset: any;
  public lifestyleOffset: any;
  // public aboutOffset: any;

  ngAfterViewInit() {
    this.aboutOffset = this.aboutElement?.nativeElement.offsetTop;
    this.lifestyleOffset = this.lifestyleElement?.nativeElement.offsetTop;
    this.locationOffset = this.locationElement?.nativeElement.offsetTop;
    // this.aboutOffset = this.aboutElement.nativeElement.offsetTop;
  }

  activeLink(li: any) {
    this.lists.forEach((item: { classList: { remove: (arg0: string) => any; }; }) => item.classList.remove('active'));
    li.classList.add('active');
  }

  // @HostListener('window:scroll', ['$event'])
  // checkScroll() {
  //   this.section.forEach((sec: { offsetTop: any; offsetHeight: any; getAttribute: (arg0: string) => any; }) => {
  //     let top = window.scrollY;
  //     let offset = sec.offsetTop;
  //     let height = sec.offsetHeight;
  //     let id = sec.getAttribute('id');

  //     // console.log(id)

  //     if (top >= offset && top < offset + height) {
  //       // const target = document.querySelector(`[href='#${id}']`)?.parentElement;
  //       let target = document.querySelector(`[href='#${id}']`);
  //       this.activeLink(target);
  //     }
  //   })
  // }


  @HostListener('window:scroll', ['$event'])
  checkOffsetTop() {
    if (window.pageYOffset / 2 >= this.aboutOffset && window.pageYOffset / 2 < this.lifestyleOffset) {
      this.currentActive = 1;
    } else if (window.pageYOffset / 2 >= this.lifestyleOffset && window.pageYOffset / 2 < this.locationOffset) {
      this.currentActive = 2;
    }
    // else if (window.pageYOffset >= this.newOffset && window.pageYOffset < this.aboutOffset) {
    //   this.currentActive = 3;
    // } 
    else if (window.pageYOffset / 2 >= this.locationOffset) {
      this.currentActive = 3;
    } else {
      this.currentActive = 1;
    }
    // console.log(window.pageYOffset, this.aboutOffset)
  }
  // (keyup)="searchAreaGuide($event)"

  // searchInput: any
  // areaGuideSearchList: any = []
  // isLoading: boolean = false
  searchAreaGuide(search: any) {

    if (this.searchInput == '') {
      // this.toastr.warning('', 'Project description in Arabic cannot be empty')
      this.translateService.get('Enter keywords to search').subscribe((res: string | undefined) => {
        this.toastr.error('', res);
        return
      })
    }

    this.configSettings.setShowLoader(true);
    this.searchInput = search

    //   console.log(getParams)

    this.getAreaGuides(this.searchInput)

  }


  clearSearch() {
    this.searchInput = ''
    this.getAreaGuides(this.searchInput)
  }

}
