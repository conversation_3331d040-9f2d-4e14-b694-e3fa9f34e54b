import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AreaGuidesRoutingModule } from './area-guides-routing.module';
import { AreaGuidesComponent } from './area-guides.component';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { MapModule } from 'src/app/Components/map/map.module';
import { ApartmentsForRsModule } from 'src/app/Components/apartments-for-rs/apartments-for-rs.module';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [AreaGuidesComponent],
  imports: [
    CommonModule,
    AreaGuidesRoutingModule,
    CarouselModule,
    MapModule,
    ApartmentsForRsModule,
    FormsModule,
    NgSelectModule,
    TranslateModule
  ]
})
export class AreaGuidesModule { }
