<div>
    <div class="top-banner">
        <div>
            <img src="../../../assets/images/area-guides-banner.jpg"
                class="w-full aspect-[2/1] md:aspect-[3/1] xl:aspect-[4/1] object-cover " alt="">
        </div>
        <div
            class="container mx-auto px-4 sm:px-14 md:px-24 relative block h-0 bottom-40 md:bottom-[19rem] lg:bottom-96 ">
            <div class="md:py-20 lg:py-24 mx-auto md:px-0 xl:px-16">
                <p class="text-white text-4xl md:text-6xl font-medium text-center pb-10 md:pb-16">{{"Area Guides" |
                    translate}}</p>
                <div class="grid grid-cols-12 gap-4">
                    <div class="col-span-8 lg:col-span-10">
                        <div class=" relative">
                            <input type="text"
                                class="h-10 xl:h-14 lg:h-14 w-full rounded-md focus:outline-none text-xl placeholder:text-slate-400 placeholder:text-base px-5"
                                placeholder="{{'Search Location' | translate}}" [(ngModel)]="searchInput" name="" id=""
                                (keyup.enter)="searchAreaGuide(searchInput)">
                            <img (click)="clearSearch()" *ngIf="searchInput != ''"
                                src="../../../assets/icons/close-button.svg"
                                class="w-3 aspect-square absolute rtl:left-5 ltr:right-5 top-1/2 -translate-y-1/2 cursor-pointer"
                                alt="">
                        </div>
                        <!-- <ng-select class="home-dropdown" placeholder="{{'Search location' | translate}}"
                            [(ngModel)]="searchInput" (search)="searchAreaGuide($event)" notFoundText="Enter Keywords"
                            [loading]="isLoading">
                            <ng-option *ngFor="let item of areaGuideSearchList" [value]="item.id">{{item.name}}
                            </ng-option>
                        </ng-select> -->
                    </div>
                    <div class="col-span-4 lg:col-span-2">
                        <button (click)="searchAreaGuide(searchInput)" type="button"
                            class="rounded-md bg-accent h-10  md:h-10 lg:h-14 w-full text-white text-xl text-center flex justify-center items-center"><img
                                src="../../../assets/icons/search.svg" class="w-4 md:w-5 rtl:mt-1 mx-1.5 relative "
                                alt="">
                            {{"Search" | translate}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="mt-5 lg:my-16">
        <div class="container mx-auto  ">
            <div class="mb-12">
                <h1 class="text-center uppercase text-white text-2xl">{{"Discover" | translate}}</h1>
            </div>


            <div *ngIf="areaGuides?.length === 0">
                <p class="text-white text-center text-2xl">There are no guides with the given search</p>
            </div>

            <div *ngIf="areaGuides?.length>0"
                class="justify-center items-center relative container mx-auto px-4 sm:px-14 md:px-24">

                <div class="">
                    <!-- <div
                        class="flex justify-end rtl:justify-start rtl:flex-row-reverse  gap-5 mb-8 container mx-auto md:hidden">
                        <p (click)="homeAds.prev()" class="h-5 w-5 hover:cursor-pointer">
                            <img src="../../../assets/icons/arrow-semi-left.svg">
                        </p>
                        <p (click)="homeAds.next()" class="h-5 w-5 hover:cursor-pointer ">
                            <img src="../../../assets/icons/arrow-semi-right.svg">
                        </p>
                    </div> -->

                    <p (click)="homeAds.prev()"
                        class="block z-10 md:hidden hover:cursor-pointer absolute top-[40%]  left-2 bg-white w-9 h-9 rounded-full hover:scale-105">
                        <img class="h-7 w-7 my-1 mx-1" src="../../../assets/icons/semi-left-black.png">
                    </p>
                    <p (click)="homeAds.next()"
                        class="block z-10  md:hidden hover:cursor-pointer absolute top-[40%] right-2 bg-white w-9 h-9 rounded-full  hover:scale-105">
                        <img class="h-7 w-7 my-1 mx-1" src="../../../assets/icons/semi-right-black.png">
                    </p>

                    <div class="">
                        <owl-carousel-o [options]="customOptions" #homeAds class="searchCarousal ">
                            <ng-container *ngFor="let areaGuide of areaGuides">
                                <ng-template carouselSlide [width]="425" [id]="areaGuide.id">
                                    <img class="aspect-square object-cover  mr-auto hover:cursor-pointer"
                                        [src]="areaGuide.banner" (click)="showGuideDetails(areaGuide.id)">
                                </ng-template>
                            </ng-container>
                        </owl-carousel-o>
                    </div>

                    <div class="hidden md:block">
                        <p (click)="homeAds.prev()"
                            class="hover:cursor-pointer absolute top-1/2 left-12 hover:scale-105">
                            <img class="h-7 w-7" src="../../../assets/icons/arrow-left.png">
                        </p>
                        <p (click)="homeAds.next()"
                            class="hover:cursor-pointer absolute top-1/2 right-12 hover:scale-105">
                            <img class="h-7 w-7" src="../../../assets/icons/arrow-right.png">
                        </p>
                    </div>
                </div>

                <div *ngIf="areaGuides?.length == 0" class="text-white text-center">
                    {{"No Area guides were found" | translate}}
                </div>
            </div>
        </div>

        <div *ngIf="areaDetails" class="mt-16">
            <div class="container mx-auto px-4 sm:px-14 md:px-24">
                <div class="  mb-16 ">
                    <div class="bg-white  rounded-t-lg ">
                        <div class="p-5 xl:p-10">
                            <div class="mb-16">
                                <img [src]="areaDetails.banner_image"
                                    class="w-full aspect-[9/5] object-cover xl:aspect-auto lg:aspect-auto h-full lg:h-[37rem] xl:h-[37rem]"
                                    alt="">
                            </div>

                            <!-- nav -->
                            <div class="flex  items-center justify-center gap-10  xl:gap-24 sticky">
                                <div *ngIf="areaDetails?.about !==''" (click)="scrollTo(about,1)"
                                    class="nav a cursor-pointer ">
                                    <div class="flex items-center justify-center mb-3 gap-4 px-5">
                                        <img *ngIf="currentActive === 1" src="../../../assets/icons/building.svg"
                                            class="h-7 w-7">
                                        <img *ngIf="currentActive !== 1" src="../../../assets/icons/building-g.svg"
                                            class="h-7 w-7">
                                        <p [ngClass]="{'active': currentActive === 1}"
                                            class="hidden md:block link-nav text-lg font-bold text-mediumGray">{{"About"
                                            | translate}}
                                        </p>
                                    </div>
                                    <div [ngClass]="{'active': currentActive === 1}"
                                        class="link-bar  h-[3.75px] bg-white rounded-t-lg "></div>
                                </div>
                                <div *ngIf="areaLifeStyles?.length > 0" (click)="scrollTo(lifestyleSection,2)"
                                    class=" nav border-l-yellow-300 cursor-pointer ">
                                    <div class="flex items-center justify-center mb-3 gap-4 px-5">
                                        <img *ngIf="currentActive === 2" src="../../../assets/icons/restaurant-b.svg"
                                            class="h-7 w-7">
                                        <img *ngIf="currentActive !== 2" src="../../../assets/icons/restaurant.svg"
                                            class="h-7 w-7">
                                        <p [ngClass]="{'active': currentActive === 2}"
                                            class="hidden md:block link-nav text-lg font-bold text-mediumGray">
                                            {{"Lifestyle" |
                                            translate}}</p>
                                    </div>
                                    <div [ngClass]="{'active': currentActive === 2}"
                                        class="link-bar h-[3.75px] bg-white rounded-t-lg "></div>
                                </div>
                                <div *ngIf="areaDetails?.latlon !== ''" (click)="scrollTo(location,3)"
                                    class=" nav lo cursor-pointer">
                                    <div class="flex items-center justify-center mb-3  gap-4 px-5">
                                        <img *ngIf="currentActive === 3" src="../../../assets/icons/location-b.svg"
                                            class="h-7 w-7">
                                        <img *ngIf="currentActive !== 3" src="../../../assets/icons/location.svg"
                                            class="h-7 w-7">
                                        <p [ngClass]="{'active': currentActive === 3}"
                                            class=" hidden md:block link-nav text-lg font-bold text-mediumGray">
                                            {{"Location" |
                                            translate}}</p>
                                    </div>
                                    <div [ngClass]="{'active': currentActive === 3}"
                                        class="link-bar h-[3.75px] bg-white rounded-t-lg "></div>
                                </div>
                            </div>
                            <section id="about" #about class="section pt-6 scroll-mt-40">
                                <div *ngIf="areaDetails?.about !==''">
                                    <div>
                                        <h3 class="text-primary text-2xl font-bold  my-4">{{areaDetails?.title}}</h3>
                                        <p class="text-sm text-slate-600" [innerHTML]="areaDetails?.about"></p>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <div *ngIf="areaSpecifications?.length > 0" class=" px-10 pb-0">
                            <div class="bg-gradient-to-b from-[#E8F3FF] to-[#E8F3FF00] p-10">
                                <div class="flex justify-center flex-wrap gap-20">
                                    <div *ngFor="let specification of areaSpecifications" class="">
                                        <h2 class="text-center text-primary font-bold md:text-base lg:text-4xl">
                                            {{specification.value}}</h2>
                                        <p class="text-center text-[#383838] text-sm ">{{specification.title}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <section id="lifestyle" #lifestyleSection class="section scroll-mt-40 pb-1">

                            <div *ngIf="areaLifeStyles?.length > 0" class="pt-4 ">
                                <!-- *ngIf="areaDetails?.areaLifestyle.length > 0" -->
                                <h3 *ngIf="areaLifeStyles?.length  "
                                    class="text-primary text-2xl font-bold mb-8 mt-4 px-5 xl:px-10">
                                    {{'Lifestyle' | translate}}</h3>
                                <div *ngFor="let lifeStyle of areaLifeStyles; let i = index">
                                    <div class="bg-white px-5 xl:px-10">
                                        <div *ngIf="i%2===0 && lifeStyle.type === 'C'"
                                            class="grid grid-cols-12 gap-4 mb-14">
                                            <div class="col-span-12 lg:col-span-6">
                                                <img [src]="lifeStyle.image" class="w-full h-[350px] object-cover"
                                                    alt="">
                                            </div>
                                            <div class="col-span-12 lg:col-span-6">
                                                <h6 class="text-[#383838] font-semibold text-base mb-3"
                                                    [innerHTML]="lifeStyle.title"></h6>
                                                <p class="text-xs text-[#383838] leading-6"
                                                    [innerHTML]="lifeStyle.content">
                                                </p>
                                            </div>
                                        </div>
                                        <div *ngIf="i%2!==0 && lifeStyle.type === 'C'"
                                            class="grid grid-cols-12 gap-4 mb-14">
                                            <div class="col-span-12 lg:col-span-6">
                                                <h6 class="text-[#383838] font-semibold text-base mb-3"
                                                    [innerHTML]="lifeStyle.title"></h6>
                                                <p class="text-xs text-[#383838] leading-6"
                                                    [innerHTML]="lifeStyle.content">
                                                </p>
                                            </div>
                                            <div class="col-span-12 lg:col-span-6">
                                                <img [src]="lifeStyle.image" class="w-full h-[350px] object-cover"
                                                    alt="">
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div *ngFor="let lifeStyle of areaLifeStyles; let i = index">
                                    <div *ngIf="lifeStyle.type === 'P'"
                                        class="container mx-auto px-5 xl:px-10 bg-primary relative py-12">
                                        <div id="" class="flex justify-between items-center mb-8">
                                            <p
                                                class="md:text-3xl lg:text-4xl font-bold tracking-wide uppercase text-white">
                                                {{lifeStyle.title}}
                                            </p>
                                            <h1
                                                class="rtl:text-right ltr:text-left text-white text-2xl grow align-middle pt-1.5">
                                                {{"Recommended Properties" | translate}}</h1>
                                            <div class="flex rtl:flex-row-reverse  gap-5  ">


                                                <p (click)="areaGuidePropertyCarousalID.prev()"
                                                    class="h-6 w-6 hover:cursor-pointer hover:scale-105 ">
                                                    <img src="../../../assets/icons/arrow-semi-left.svg">
                                                </p>

                                                <p class="h-6 w-6 hover:cursor-pointer hover:scale-105 "
                                                    (click)="areaGuidePropertyCarousalID.next()">
                                                    <img src="../../../assets/icons/arrow-semi-right.svg">
                                                </p>






                                            </div>


                                        </div>

                                        <div class="mx-auto w-full">



                                            <!-- <div class="mt-16"
                                                class="justify-center items-center relative container mx-auto px-12 sm:px-14 md:px-24"> -->

                                            <owl-carousel-o [options]="propertyGuideCarousal"
                                                #areaGuidePropertyCarousalID class="areaGuidePropertyCarousal">

                                                <ng-container *ngFor="let property of lifeStyle.properties">
                                                    <ng-template carouselSlide [width]="245">
                                                        <app-apartments-for-rs [property]="property">
                                                        </app-apartments-for-rs>
                                                    </ng-template>
                                                </ng-container>
                                            </owl-carousel-o>
                                        </div>





                                    </div>
                                </div>
                            </div>


                            <!-- </div> -->

                        </section>

                    </div>


                    <section id="location" #location
                        class="section px-5 xl:px-10 py-14 bg-white rounded-b-lg scroll-mt-40">
                        <div *ngIf="areaDetails?.latlon !== ''">
                            <h3 class="text-primary text-2xl font-bold mb-5">{{"Location" | translate}}</h3>
                            <!-- Map -->
                            <div class=" mb-12 w-full">
                                <a target="_blank"
                                    [href]="'https://www.google.com/maps/search/?api=1&query='+googleLatLon+'&zoom=20'"
                                    class="map  h-[34rem] block cursor-pointer">
                                    <!-- <a class="map  h-[34rem] block cursor-pointer "> -->

                                    <app-map [showSearch]="showSearch" [lat]="lat" [lng]="lng" [zoom]="17"
                                        class="pointer-events-none"></app-map>

                                    <div class="group w-fit mx-auto">
                                        <div
                                            class="gm-style-iw-t w-fit mx-auto bottom-[25rem] max-w-[648px] max-h-20 min-w-0 relative hidden group-hover:block">
                                            <div
                                                class="flex gap-2 w-[17rem] h-24 cursor-pointer p-2  bg-white rounded-md">
                                                <div class="flex flex-col gap-1">
                                                    <p class="text-base tracking-tight font-bold text-primary">
                                                        {{areaDetails?.country}}</p>
                                                    <p class="text-gray-800 text-sm tracking-tight font-semibold ">
                                                        {{areaDetails?.state}} </p>
                                                    <p
                                                        class="text-xs text-[#383838]  tracking-tight font-medium mb-3 oneline-ellipsis">
                                                        {{areaDetails?.title}}</p>
                                                </div>
                                            </div>


                                            <div class="gm-style-iw-tc left-[45%]"></div>
                                        </div>

                                        <div class="map-center-overlay p-10">
                                            <img src="../../../assets/icons/location-pointer.svg" width="45"
                                                height="45">
                                        </div>
                                    </div>

                                </a>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
        <div *ngIf="(areaDetails | json) == '{}'" class="h-vh40 flex justify-center items-center text-xl text-white">
            {{"No Area Guide was Found" | translate}}
        </div>
    </div>
</div>