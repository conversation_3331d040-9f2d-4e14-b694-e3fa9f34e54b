.link-nav {
    color: #656565,
}

.link-bar {
    background-color: white;
}

.link-nav.active {
    color: #002349,
}

.link-bar.active {
    background-color: #002349;
}

.map {
    position: relative;
  }
  .property{
    padding-right: 200%;
  }
  
  .map-center-overlay {
    position: absolute;
    top: 46%; 
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    height: 50px;
  }

//   @media only screen and (max-width: 767px){
//     .mobileslider{
//         overflow-x: scroll;
//         width: 100%;
        

//         .mobilemenu{
//             min-width: 365px;
           
//         }

//     }
// }

.gm-style-iw-t .gm-style-iw-tc{
    background: white;
    clip-path: polygon(0% 0%,50% 100%,100% 0%);
    content: '';
    height: 12px;
    position: absolute;
    bottom: -25px;
    width: 25px;
}
