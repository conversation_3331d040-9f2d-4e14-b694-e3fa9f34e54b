import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FaqsService } from 'src/app/Services/FAQs/faqs.service';
@Component({
  selector: 'app-faq',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.scss']
})
export class FaqComponent implements OnInit {

  constructor(
    private faqsService: FaqsService,
    private router:Router
  ) { }

  categories: any
  items: any

  ngOnInit(): void {

    const getParams = {

    }

    this.faqsService.getFAQS(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            console.log(response.body.data)
            this.categories = response.body.data.categories
            this.items = response.body.data.items
          }
        }
      },
      error: (err) => {
      }
    })
  }
}
