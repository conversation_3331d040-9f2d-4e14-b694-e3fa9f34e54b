<div class="container mx-auto px-4 sm:px-14 md:px-24 lg:px-48 pb-16 min-h-vh75">


    <h2 class="text-white text-center font-medium text-3xl md:text-4xl lg:text-5xl xl:text-6xl py-5">
        {{"Frequently Asked Questions" | translate}}</h2>

    <h4 class="text-white font-semibold text-2xl mt-5">{{"How can we help you?" | translate}}</h4>
    <div class="grid grid-cols-12 gap-4 mt-5 ">
        <div class="col-span-8 lg:col-span-10">
            <input type="text"
                class="h-12 w-full rounded-md focus:outline-none text-xl placeholder:text-slate-400 placeholder:text-sm px-5"
                placeholder="{{'Search your query here...…..' | translate}}" name="" id="">
        </div>
        <div class="col-span-4 lg:col-span-2">
            <button type="button"
                class="rounded-md bg-accent h-12 w-full text-white text-xl text-center flex justify-center items-center"><img
                    src="../../../assets/icons/search.svg" class="w-5 mx-1.5 relative " alt=""> 
                    {{"Search" | translate}}</button>
        </div>
    </div>


    <div class="mt-24">
        <div *ngIf="categories" class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-5 gap-4 md:gap-7">
            <div *ngFor="let category of categories"
                class="bg-[#ffffff21] text-center rounded-2xl w-full py-8 lg:py-6 xl:py-10 cursor-pointer hover:shadow-lg hover:scale-[101%]">
                <img src="{{category?.icon}}" class="text-center mx-auto h-12 md:h-10 lg:h-10 xl:h-12" alt="">
                <p class="text-[#FCFCFC] text-base mt-7 ">{{category?.name}}</p>
            </div>
        </div>
    </div>

    <div class="mt-20 lg:mt-28 overflow-hidden">
        <div *ngIf="items" class="grid grid-cols-12 gap-y-10 lg:gap-20">
            <div *ngFor="let item of items" class="col-span-12 lg:col-span-6">
                <h1 class="text-white text-2xl md:text-3xl mb-8 font-semibold">{{item?.title}}</h1>
                <div *ngFor="let faq of item?.faqs" [routerLink]="['/faq-details', faq.id]"
                    class="border-b border-[FCFCFC] w-full pb-6 mt-6 flex justify-between align-middle items-center  cursor-pointer active:scale-[99.2%]">
                    <p class="text-[#FCFCFC] text-lg lg:text-lg w-[96%]">{{faq?.question}}</p>
                    <img src="../../../assets/icons/arrow-semi-right.svg" class="w-2.5 md:w-2 mx-1">
                </div>
            </div>
            <!-- <div class="col-span-12 lg:col-span-6 pt-20 md:pt-20 lg:pt-20">
                <h1 class="text-white text-3xl font-semibold">Home Owner / Landlord</h1>
                <div class="">
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I login/signup?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I create a new account?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How to post a listing on findproperty.com?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">When will my listing be visible on the website?
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-12 gap-0 lg:gap-20">
            <div class="col-span-12 lg:col-span-6">
                <h1 class="text-white text-3xl font-semibold">Agent</h1>
                <div class="">
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I login/signup?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I create a new account?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I reset my password?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I subscribe from Find Property SMS....</p>
                    </div>
                </div>
            </div>
            <div class="col-span-12 lg:col-span-6 pt-20 md:pt-20 lg:pt-0">
                <h1 class="text-white text-3xl font-semibold">On Demand</h1>
                <div class="">
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I login/signup?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How do I create a new account?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">How to post a listing on findproperty.com?</p>
                    </div>
                    <div class="border-b border-[FCFCFC] w-full pb-6 mt-14">
                        <p class="text-[#FCFCFC] text-xl lg:text-2xl">When will my listing be visible on the website?
                        </p>
                    </div>
                </div>
            </div>
        </div> -->
        </div>
    </div>