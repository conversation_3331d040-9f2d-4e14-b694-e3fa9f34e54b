import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { configSettings } from 'src/app/Config/config.settings';
import { UserService } from 'src/app/Services/User/user.service';

@Component({
  selector: 'app-my-account',
  templateUrl: './my-account.component.html',
  styleUrls: ['./my-account.component.scss']
})
export class MyAccountComponent implements OnInit {

  constructor(
    private router: Router,
    private userService: UserService,
    private configSettings: configSettings,
  ) { }

  userDetails: any;
  userTypes: any = []


  isOwner: any = false
  isArchitect: any = false
  isAgent: any = false

  ngOnInit(): void {

    this.configSettings.tiggerNotificationAPi()

    this.userDetails = this.configSettings.getUserDetails();

    if (!this.userDetails) {
      this.router.navigate(['/'])
    }

    this.userDetails.user_types.forEach((el: any) => {
      this.userTypes.push(el.name)
    })

    // console.log(this.userTypes)


    this.isOwner = this.userTypes.includes("Owner")
    this.isArchitect = this.userTypes.includes("Architect")
    this.isAgent = this.userTypes.includes("Agent")
    // console.log(this.isOwner, this.isArchitect, this.isAgent)
  }

  showLogoutModal: boolean = false

  openLogoutModal() {
    if(this.showLogoutModal = !this.showLogoutModal){
    document.body.style.overflow = "hidden";
    
    } else {
      document.body.style.overflow = "auto";
    }
    
  }

  logout() {
    localStorage.removeItem('userDetails')
    this.configSettings.setIsUserLoggedIn(false)
    this.userService.setUser(false)
    this.configSettings.setUserTypes()
    this.router.navigate(['/home'])
    document.body.style.overflow = "auto";
  }
}
