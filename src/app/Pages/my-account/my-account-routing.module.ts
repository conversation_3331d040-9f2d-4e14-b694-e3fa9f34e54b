import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MyAccountComponent } from './my-account.component';
import { MyPlansComponent } from './my-plans/my-plans.component';
import { MyProfileComponent } from './my-profile/my-profile.component';
import { SavedSearchesComponent } from './saved-searches/saved-searches.component';
import { MyPropertyListingComponent } from './my-property-listing/my-property-listing.component';
import { MyStoriesComponent } from './my-stories/my-stories.component';
import { MyArchitectProfileComponent } from './my-architect-profile/my-architect-profile.component';
import { MyCompanyProfileComponent } from './my-company-profile/my-company-profile.component';
import { MyAgentProfileComponent } from './my-agent-profile/my-agent-profile.component';
import { ChatsComponent } from './chats/chats.component';
import { SettingsComponent } from './settings/settings.component';
import { MyProjectsComponent } from './my-projects/my-projects.component';
import { FavoritesComponent } from './favorites/favorites.component';



const routes: Routes = [
  {
    path: '',
    component: MyAccountComponent,
    children: [
      { path: '', redirectTo: 'my-profile', pathMatch: 'full' },
      {
        path: 'my-profile',
        component: MyProfileComponent
      },
      {
        path: 'architech-profile',
        component: MyArchitectProfileComponent
      },
      
      {
        path: 'company-profile',
        component: MyCompanyProfileComponent
      },
      {
        path: 'agent-profile',
        component: MyAgentProfileComponent
      },
      {
        path: 'chats',
        component: ChatsComponent
      },
      {
        path: 'chats/:toID',
        component: ChatsComponent
      },
      {
        path: 'my-plans',
        component: MyPlansComponent
      },
      {
        path: 'saved-searches',
        component: SavedSearchesComponent
      },
      {
        path: 'my-property-listing',
        component: MyPropertyListingComponent
      },
      {
        path: 'my-projects',
        component: MyProjectsComponent
      },
      {
        path: 'my-stories',
        component: MyStoriesComponent
      },
      {
        path: 'settings',
        component: SettingsComponent
      },
      {
        path: 'favorites',
        component: FavoritesComponent
      },


    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MyAccountRoutingModule { }
