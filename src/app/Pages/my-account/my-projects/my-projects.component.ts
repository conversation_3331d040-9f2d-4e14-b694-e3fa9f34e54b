import { Component, OnInit, Renderer2 } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { FeaturedPlanListService } from 'src/app/Services/Featured-plan-list/featured-plan-list.service';
import { ProjectsService } from 'src/app/Services/Projects/projects.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-my-projects',
  templateUrl: './my-projects.component.html',
  styleUrls: ['./my-projects.component.scss']
})
export class MyProjectsComponent implements OnInit {

  //Variables
  myProjects: any = []
  featurePlanShowModal: boolean;
  featureThisProjectID: any;
  planChoosen: any
  plansList: any
  allPaymentMethods: any
  choosenPaymode: any = ''

  confirmationModal: boolean = false
  projectID: any


  constructor(

    private projectsService: ProjectsService,
    private configSettings: configSettings,
    private toastr: ToastrService,
    private renderer: Renderer2,
    private router: Router,
    private featuredPlanListService: FeaturedPlanListService,
    private translateService: TranslateService,

  ) {
    this.configSettings.setShowLoader(true);
  }

  ngOnInit(): void {
    this.getAllMyProjects()
    this.getFeaturedPlansDetails()
  }

  getAllMyProjects() {


    const getParams = {
      per_page: 99999
    }

    const postParams = {
      id: this.configSettings.getUserID(),
      sort_by: "DESC"
    }

    this.projectsService.myProjects(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.myProjects = response.body.data.items
            this.configSettings.setShowLoader(false);
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  deleteConfirmation(projectID: number) {
    // this.confirmationModal = true
    if (this.confirmationModal = true) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    this.projectID = projectID
  }

  closeConfirmation(){
    this.confirmationModal = false
    document.body.style.overflow = "auto";
  }


  deleteProject() {
    document.body.style.overflow = "auto";
    this.configSettings.setShowLoader(true);
    const postParams = {
      user_id: this.configSettings.getUserID(),
      project_id: this.projectID
    }

    this.projectsService.deleteProject({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.myProjects = response.body.data
            this.confirmationModal = false

            this.configSettings.setShowLoader(false);
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


  getFeaturedPlansDetails() {

    const getParams = {
      type: 'PR'
    }

    this.featuredPlanListService.featuredPlans(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.plansList = response.body.data.plan
            this.allPaymentMethods = response.body.data.payment_methods
            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', 'Something went wrong');
      }
    })
  }

  featureMyPropertyModal(id: any) {
    // this.featurePlanShowModal = true
    if (this.featurePlanShowModal = true) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    this.featureThisProjectID = id
  }


  closeFeatureMyProjectModal() {
    this.featurePlanShowModal = false
    document.body.style.overflow = "auto";
    this.planChoosen = null
    this.choosenPaymode = ''
    // activeCheck
  }


  choosePaymentType(e: any) {
    Array.from(document.querySelectorAll('.payment-method')).forEach(function (el) {
      el.classList.remove('activeBorder');
    });
    this.renderer.addClass(e.currentTarget, 'activeBorder')
    this.choosenPaymode = e.currentTarget.id
  }

  proceedAndPay() {
    document.body.style.overflow = "auto";
    if (this.planChoosen === undefined || this.planChoosen === null) {
      this.translateService.get('Select Featured Plan').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
      return
    }
    if (this.choosenPaymode == '') {
      this.toastr.error('', 'Select Payment method')
      return
    }
    const confirmURL = window.location.origin + this.router.createUrlTree(['/confirmation-page']);

    this.postProject(this.planChoosen.id, this.choosenPaymode, confirmURL)

  }


  postProject(planID: any, paymentMethod: any, url: any) {

    const postParams = {

      "post_type": "F",
      "as_featured": 1,
      "project_id": this.featureThisProjectID,
      "user_id": this.configSettings.getUserID(),
      "paymode": paymentMethod,
      "featured_plan_id": planID,
      "redirect_url": url,
    }

    this.projectsService.postProject({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            if (response.body.data.payment_url) {
              this.configSettings.setShowLoader(true)
              window.location.href = response.body.data.payment_url;
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }
}
