<div class="text-white">
    <h1 class="text-2xl font-bold text-center mb-10">{{"SETTINGS"| translate}}</h1>

    <div class="border-b border-borderColor pb-6 mb-64 flex justify-between">
        <div>
            <h2 class="font-bold text-sm mb-8">{{"NOTIFICATION"| translate}}</h2>
            <!-- <p class="text-xs text-[#ebeef0]">Lorem ipsum dolor, sit amet consectetur adipisicing elit. Deserunt iste
                atque
                et dolorum. Asperiores sequi</p> -->
        </div>
        <input class="notification" type="checkbox" id="notification" /><label for="notification">Toggle</label>

    </div>

    <!-- <div class="border-b border-borderColor pb-4 mb-5 flex justify-between" [ngClass]="isUser?'':'hidden'">
        <div>
            <h2 class="font-bold text-sm mb-3">{{"USER"| translate}}</h2>
        </div>
        <input class="switch-user" type="checkbox" id="switch-user" #user (click)="changeUserType($event,'UR')" /><label
            for="switch-user">Toggle</label>

    </div>
    <div class="border-b border-borderColor pb-4 mb-5 flex justify-between">
        <div>
            <h2 class="font-bold text-sm mb-3">{{"ARCHITECT"| translate}}</h2>
        </div>
        <input class="notification" type="checkbox" id="architect" #architect
            (click)="changeUserType($event,'AR')" /><label for="architect">Toggle</label>

    </div>

    <div class="border-b border-borderColor pb-4 mb-5 flex justify-between">
        <div>
            <h2 class="font-bold text-sm mb-3">{{"AGENT"| translate}}</h2>
        </div>
        <input class="switch-property" type="checkbox" id="switch-property" #agent
            (click)="changeUserType($event,'AG')" /><label for="switch-property">Toggle</label>
    </div> -->

</div>