import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { configSettings } from 'src/app/Config/config.settings';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {

  userData: any;
  isArchitect: boolean = false;
  isOwnerAgent: boolean = false;
  isUser: boolean = false;
  constructor(
    public configSettings: configSettings,
  ) {
  }


  ngOnInit(): void {



  }




}
