import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MyAccountRoutingModule } from './my-account-routing.module';
import { MyAccountComponent } from './my-account.component';
import { MyPlansComponent } from './my-plans/my-plans.component';
import { MyProfileComponent } from './my-profile/my-profile.component';
import { SavedSearchesComponent } from './saved-searches/saved-searches.component';
import { MyPropertyListingComponent } from './my-property-listing/my-property-listing.component';
import { MyStoriesComponent } from './my-stories/my-stories.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MyArchitectProfileComponent } from './my-architect-profile/my-architect-profile.component';
import { MyCompanyProfileComponent } from './my-company-profile/my-company-profile.component';
import { SettingsComponent } from './settings/settings.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { MyAgentProfileComponent } from './my-agent-profile/my-agent-profile.component';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { ChatsComponent } from './chats/chats.component';
import { TranslateModule } from '@ngx-translate/core';
import { MyProjectsComponent } from './my-projects/my-projects.component';
import { FavoriteCardModule } from 'src/app/Components/favorite-card/favorite-card.module';
import { FavoritesComponent } from './favorites/favorites.component';

@NgModule({
  declarations: [MyAccountComponent, MyPlansComponent, MyProfileComponent, SavedSearchesComponent, MyPropertyListingComponent, MyStoriesComponent, MyArchitectProfileComponent, MyCompanyProfileComponent, SettingsComponent, MyAgentProfileComponent, ChatsComponent, MyProjectsComponent, FavoritesComponent],
  imports: [
    CommonModule,
    FormsModule,
    CarouselModule,
    ReactiveFormsModule,
    MyAccountRoutingModule,
    TranslateModule,
    NgSelectModule,
    FavoriteCardModule
  ]
})
export class MyAccountModule { }
