import { Component, OnInit, Renderer2 } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { FeaturedPlanListService } from 'src/app/Services/Featured-plan-list/featured-plan-list.service';
import { MyAccountService } from 'src/app/Services/My-account/my-account.service';
import { EditPropertyService } from 'src/app/Services/Properties/edit-property.service';
import { PostPropertyService } from 'src/app/Services/Properties/post-property.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-my-property-listing',
  templateUrl: './my-property-listing.component.html',
  styleUrls: ['./my-property-listing.component.scss']
})
export class MyPropertyListingComponent implements OnInit {


  myPropertiesArr: any =[]

  // Modal
  featurePlanShowModal: boolean = false
  planChoosen: any
  plansList: any
  allPaymentMethods: any
  choosenPaymode: any = ''

  featureThisPropertyID: any

  confirmationModal: boolean = false

  markSoldModal: boolean = false

  propertyID: any
  property: any

  constructor(
    private myAccountService: MyAccountService,
    private toastr: ToastrService,
    private configSettings: configSettings,
    private editPropertyService: EditPropertyService,
    private renderer: Renderer2,
    private router: Router,
    private featuredPlanListService: FeaturedPlanListService,
    private postPropertyService: PostPropertyService,
    private translateService: TranslateService,


  ) { }

  ngOnInit(): void {

    this.myProperties()
    this.getFeaturedPlansDetails()

  }



  myProperties() {
    this.configSettings.setShowLoader(true)

    const getParams = {

      user_id: this.configSettings.getUserDetails().user_id,
      // user_id: 36
      per_page: 9999
    }

    this.myAccountService.getMyProperties(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.myPropertiesArr = response.body.data.properties
            console.log(this.myPropertiesArr.length);
            
            this.configSettings.setShowLoader(false)

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


  deleteConfirmation(propertyID: number) {
    // this.confirmationModal = true
    if (this.confirmationModal = true) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    this.propertyID = propertyID
  }

  closeConfirmation(){
    this.confirmationModal = false
    document.body.style.overflow = "auto";
  }



  deleteProperty() {
    document.body.style.overflow = "auto";
    this.configSettings.setShowLoader(true)

    const postParams = {
      user_id: this.configSettings.getUserDetails().user_id,
      property_id: this.propertyID
    }

    this.editPropertyService.deleteProperty({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.myPropertiesArr = response.body.data
            console.log(this.myPropertiesArr.length);

            this.confirmationModal = false
            
            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }




  markSoldConfirmation(property: any) {
    // this.markSoldModal = true
    if (this.markSoldModal = true) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    this.property = property
  }

  closeSoldpopup(){
    this.markSoldModal = false
    document.body.style.overflow = "auto";
  }

  markAsSold(property: any) {
    document.body.style.overflow = "auto";
    this.configSettings.setShowLoader(true)



    const getParams = {}
    const postParams = {
      "user_id": this.configSettings.getUserID(),
      "property_id": property.id
    }

    this.editPropertyService.markPropertySold(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            property.is_sold = 1
            this.configSettings.setShowLoader(false)
            this.markSoldModal = false
            
            
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
        this.configSettings.setShowLoader(false)
      }
    })

  }

  featureMyPropertyModal(id: any) {
    // this.featurePlanShowModal = true
    if (this.featurePlanShowModal = true) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    this.featureThisPropertyID = id
  }

  closeFeatureMyPropertyModal() {
    this.featurePlanShowModal = false
    document.body.style.overflow = "auto";
    this.planChoosen = null
    this.choosenPaymode = ''
    // activeCheck
  }


  getFeaturedPlansDetails() {

    const getParams = {
      type: 'PT'
    }

    this.featuredPlanListService.featuredPlans(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.plansList = response.body.data.plan
            this.allPaymentMethods = response.body.data.payment_methods
            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', 'Something went wrong');
      }
    })
  }


  choosePaymentType(e: any) {
    Array.from(document.querySelectorAll('.payment-method')).forEach(function (el) {
      el.classList.remove('activeBorder');
    });
    this.renderer.addClass(e.currentTarget, 'activeBorder')
    this.choosenPaymode = e.currentTarget.id
  }

  proceedAndPay() {
    document.body.style.overflow = "auto";
    if (this.planChoosen === undefined || this.planChoosen === null) {
      this.translateService.get('Select Featured Plan').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if (this.choosenPaymode == '') {
      this.toastr.error('', 'Select Payment method')
      return
    }
    const confirmURL = window.location.origin + this.router.createUrlTree(['/confirmation-page']);

    this.postProperty(this.planChoosen.id, this.choosenPaymode, confirmURL)

  }

  postProperty(planID: any, paymentMethod: any, url: any) {
    const getParams = {}
    const postParams = {
      // post_type: this.selectedPostType,
      // featured_plan_id: planID,
      // paymode: paymentMethod,
      // redirect_url: url,

      "post_type": "F",
      "as_featured": 1,
      "property_id": this.featureThisPropertyID,
      "user_id": this.configSettings.getUserID(),
      "paymode": paymentMethod,
      "featured_plan_id": planID,
      "redirect_url": url,

    }

    // console.log(postParams)

    this.postPropertyService.postProperties(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            if (response.body.data.payment_url) {
              window.location.href = response.body.data.payment_url;
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

}
