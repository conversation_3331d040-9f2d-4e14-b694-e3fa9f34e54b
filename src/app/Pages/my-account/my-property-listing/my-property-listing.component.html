<div>
    <h2 class="text-white text-center text-2xl font-bold mb-10">{{"MY PROPERTY LISTING"| translate}}</h2>
    <div *ngIf="myPropertiesArr?.length > 0" class="grid grid-cols-12 gap-4">
        <div *ngFor="let property of myPropertiesArr" class="col-span-6 xl:col-span-3 lg:col-span-3">
            <div class=" bg-gradient-to-t from-[#3d62b1]  to-[#eceaea] relative overflow-hidden" [routerLink]="">
                <img class="w-full aspect-[4/5] object-cover mix-blend-multiply" [src]="property.thumbnail" alt="">
                <div class="from-neutral-300 h-0">
                    <div class="p-2 block relative h-0 bottom-32 md:bottom-32 lg:bottom-40 ">
                        <div class="flex gap-2 items-end  mb-2">
                            <img class="w-12 h-12 md:h-12 md:w-12 lg:h-14 lg:w-14 rounded-full" [src]="property.logo">
                            <h3 *ngIf="property.is_featured == 1" class="text-[0.7rem] bg-purple-800 px-2 text-white">
                                {{"FEATURED" | translate}}</h3>
                        </div>
                        <h3 class="text-xs md:text-sm lg:text-sm font-semibold mb-1 text-white uppercase truncate  ">
                            {{property.title}}</h3>
                        <div class="flex items-center gap-2 mb-2">
                            <img src="../../../assets/icons/pin.png" class="w-2 lg:w-3">
                            <p class="text-slate-300 text-xs lg:text-sm truncate">{{property.address}}</p>
                        </div>
                        <h5 class="text-accent text-xs lg:text-sm text-right font-semibold">{{property.price}}
                            {{'KD'|translate}}
                            <span *ngIf="property.property_for =='R'">/{{property.rental_frequency}}</span>
                        </h5>
                    </div>
                </div>
                <div *ngIf="property.is_sold == 1"
                    class="absolute top-7 -right-10 font-bold bg-accent rotate-45 text-white px-16 py-2">{{'SOLD'|translate}}</div>
                <div *ngIf="property.status == 'Expired'"
                    class="text-sm absolute top-2 left-2 bg-red-500 px-2 text-white">
                    {{'Expired'}}
                </div>
                <div *ngIf="property.status == 'Active'"
                    class="text-sm absolute top-2 left-2 bg-green-500 px-2 text-white">
                    {{'Active'|translate}}
                </div>
                <div [routerLink]="['/property-details/' + property.id]"
                    class="absolute inset-0 hover:opacity-100 opacity-0 duration-100 transform cursor-pointer">
                    <button *ngIf="property.is_sold == 0" (click)="$event.stopPropagation()"
                        [routerLink]="['/post-property/edit/' + property.id]"
                        class="absolute right-3 top-3 cursor-pointer">
                        <img src="../../../../assets/icons/edit-icon.svg" class="h-7 w-7">
                    </button>
                    <button (click)="$event.stopPropagation()" (click)="deleteConfirmation(property.id)"
                        class="absolute left-3 top-3 cursor-pointer">
                        <img src="../../../../assets/icons/trash-can.svg" class="h-7 w-7">
                    </button>
                    <div class="absolute left-1/2 bottom-[5%] -translate-x-1/2 flex flex-col gap-2 px-4 w-full">
                        <button *ngIf="property.is_sold == 0" (click)="$event.stopPropagation()"
                            (click)="markSoldConfirmation(property)"
                            class="cursor-pointer bg-accent text-white active:bg-accentDark px-2 py-3">
                            <span class="text-xs sm:text-sm">{{"Mark as Sold" | translate}}</span>
                        </button>
                        <button *ngIf="property.is_featured == 0 && property.is_sold == 0"
                            (click)="$event.stopPropagation()" (click)="featureMyPropertyModal(property.id)"
                            class="cursor-pointer bg-primary text-white px-2 py-3 card">
                            <span class=" text-xs sm:text-sm">{{"Feature my property" | translate}}</span>
                        </button>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <div *ngIf="myPropertiesArr.length == 0" class="h-vh40 flex justify-center items-center text-xl text-white">
        {{"No property was posted"| translate}}
    </div>
</div>

<div *ngIf="featurePlanShowModal === true">
    <div *ngIf="featurePlanShowModal" (click)="closeFeatureMyPropertyModal()" class="bg-overlay fixed inset-0 z-[99]">
    </div>
    <div *ngIf="featurePlanShowModal"
        class="z-[100] py-5 mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2  bg-white text-black w-[96%] md:w-[28rem] rounded-xl">
        <div (click)="closeFeatureMyPropertyModal()" class="px-8 py-3 cursor-pointer text-xs flex items-center gap-1">
            <img _ngcontent-whc-c94="" src="../../../assets/icons/gray-left-arrow.png" alt="">
            <span>{{"Back" | translate}}</span>
        </div>
        <h2 class="capitalize text-xl text-center text-primary font-semibold mb-3">{{"featured post" | translate}}</h2>
        <p class="text-justify text-[.7rem] mb-7 px-11">
            {{"If you choose to post your property with the featured, the property will display on the top navigation in
            the visitors/user's app. Featured property can easy to navigate and more chance to connect with the buyers."
            | translate}}
        </p>
        <div class="px-20  mb-5">
            <label for="" class="uppercase text-[.7rem] ">{{"SELECT DURATION" | translate}}</label>
            <span class=" downarrow z-[2]">
                <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                    class="bottom-1 rtl:left-2.5 rtl:right-[unset]"></span>
            <ng-select [(ngModel)]="planChoosen" class="featured-post-dropdown placeholder:text-sm"
                placeholder="{{'Select'|translate}}">
                <ng-option *ngFor="let plan of plansList" [value]="plan">
                    {{plan.days}} {{"Days" | translate}}</ng-option>
            </ng-select>
        </div>
        <div *ngIf="planChoosen"
            class="px-24 mb-10 flex justify-between items-center text-primary font-bold tracking-tight">
            <p class="text-[.7rem] ">{{"Price Structure" | translate}}:</p>
            <!-- <p *ngIf="!planChoosen">- - -</p> -->
            <p *ngIf="planChoosen">{{planChoosen.price | number:'1.0'}} {{'KD'|translate}}/<span
                    class="text-sm font-thin">
                    {{planChoosen.days}} {{"days" | translate}}</span></p>
        </div>

        <p class="mb-3 text-primary text-base text-center font-bold">{{"Select a payment method" | translate}}:</p>
        <div class="flex justify-center items-center gap-8 mb-5">
            <div [id]="paymentType.id" class="payment-method  border-2 relative"
                *ngFor="let paymentType of allPaymentMethods" (click)="choosePaymentType($event)">
                <img [ngClass]="choosenPaymode == paymentType.id ? 'activeCheck' : ''"
                    src="../../../assets/icons/yellow-tick.svg" class="payment-method hidden">
                <img [src]="paymentType.img" class="w-16 h-10 lg:w-12 lg:h-8 cursor-pointer">
            </div>
        </div>
        <button (click)="proceedAndPay()" class="w-72 py-2 mx-auto  block bg-primary text-white uppercase">
            {{"proceed & pay" | translate}}
        </button>
    </div>
</div>

<!-- <div *ngIf="confirmationModal" (click)="confirmationModal = false" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="confirmationModal"
    class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white  rounded-xl text-sm text-white">
    <h1 class="mb-7 text-black">{{'Are you sure you want to delete this property?'| translate}}</h1>
    <div class="flex justify-around">
        <button (click)="deleteProperty()" class="bg-green-500 px-6 py-2">
            {{'Yes' | translate}}
        </button>
        <button (click)="confirmationModal = false" class="bg-red-500 px-6 py-2">
            {{'No' | translate}}
        </button>
    </div>
</div> -->
<div (click)="closeConfirmation()" *ngIf="confirmationModal" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="confirmationModal" class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 
-translate-y-1/2 p-10 bg-white w-[22rem] sm:w-[26rem] rounded-xl">

    <h1 class="text-center  text-black">{{"Are you sure" | translate}}</h1>
    <h1 class="mb-7 text-center  text-black">{{"you want to delete this property?" | translate}}</h1>
    <div class="flex justify-center gap-2">
        <button (click)="deleteProperty()" class="block text-white bg-primary py-3 px-8">{{"Yes"| translate}}</button>
        <button (click)="closeConfirmation()" class="block text-white bg-red-500 py-3 px-8">{{"No"| translate}}</button>
    </div>
</div>
<!-- 
<div *ngIf="markSoldModal" (click)="markSoldModal = false" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="markSoldModal"
    class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white  rounded-xl text-sm text-white">
    <h1 class="mb-7 text-black">{{'Are you sure you want to mark this property as sold?'}}</h1>
    <div class="flex justify-around">
        <button (click)="markAsSold(property)" class="bg-green-500 px-6 py-2">
            {{'Yes' | translate}}
        </button>
        <button (click)="markSoldModal = false" class="bg-red-500 px-6 py-2">
            {{'No' | translate}}
        </button>
    </div>
</div> -->

<div (click)="closeSoldpopup()" *ngIf="markSoldModal" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="markSoldModal" class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 
-translate-y-1/2 p-5 bg-white w-[22rem] sm:w-[26rem] rounded-xl">

    <h1 class="text-center  text-black">{{"Are you sure" | translate}}</h1>
    <h1 class="mb-7 text-center  text-black">{{"you want to mark this property as sold?" | translate}}</h1>
    <div class="flex justify-center gap-2">
        <button (click)="markAsSold(property)" class="block text-white bg-primary py-3 px-8">{{"Yes"|
            translate}}</button>
        <button (click)="closeSoldpopup()" class="block text-white bg-red-500 py-3 px-8">{{"No"| translate}}</button>
    </div>
</div>

<div *ngIf="(myPropertiesArr | json) == ' '" class="h-vh40 flex justify-center items-center text-xl text-white">
    {{'No properties were posted' | translate}}
</div>