<div class="text-white container mx-auto px-5">
            <h2 class="text-white text-center text-2xl font-bold mb-10 uppercase ">{{"Favorites"| translate}}</h2>

    <div *ngIf="favProperties.length > 0">
        <div class="mb-24">
            <div class="grid sm:grid-cols-2 md:grid-cols-3 ls:grid-cols-4 grid-flow-row gap-3 lg:gap-5">
                <div *ngFor="let favorite of favProperties">
                    <app-favorite-card (updateFavs)="getUsersFavs()" [favorite]="favorite"></app-favorite-card>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="favProperties.length == 0" class="h-vh40 flex justify-center items-center text-xl text-white">
        {{"No favorites added"| translate}}
    </div>
</div>