import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { FavoritesService } from 'src/app/Services/Favorites/favorites.service';
@Component({
  selector: 'app-favorites',
  templateUrl: './favorites.component.html',
  styleUrls: ['./favorites.component.scss']
})
export class FavoritesComponent implements OnInit {

 // VARIABLES
 userID: any
 favProperties: any
 is_fav: any








 constructor(
   private favoritesService: FavoritesService,
   private configSettings: configSettings,
   private toastr: ToastrService
 ) {
   this.configSettings.setShowLoader(true)
 }

 ngOnInit(): void {


   this.userID = this.configSettings.getUserID()

   this.getUsersFavs()
 }


 getUsersFavs() {

   let getParams = {
     user_id: this.userID
   }

   this.favoritesService.favoriteListing(getParams).subscribe({
     next: (response) => {
       if (response.status === 200) {
         if (response.body.status === 200) {
           this.favProperties = response.body.data.properties
           this.configSettings.setShowLoader(false)
         }
       }
     },
     error: (err) => {
       this.toastr.error('', err.error.message);
       this.configSettings.setShowLoader(true)
     }
   })
 }

 // toggleFav(num: number, event: any, id:any) {
 //   event.stopPropagation();
 //   const getParams = {}
 //   const postParams = {
 //     "user_id": this.userID,
 //     "type": "P",
 //     "type_id": id,
 //     "like": num
 //   }


 //   this.favoriteService.toggleFavorite(getParams, postParams).subscribe({
 //     next: (response) => {
 //       if (response.status === 200) {
 //         if (response.body.status === 200) {
 //           this.is_fav === 0 ? this.is_fav = 1 : this.is_fav = 0
 //         }
 //       }
 //     },
 //     error: (err) => {
 //       this.toastr.error('', err.error.message);
 //     }
 //   })
 // }





 // customOptions: OwlOptions = {
 //   loop: true,
 //   mouseDrag: true,
 //   touchDrag: true,
 //   pullDrag: true,
 //   dots: false,
 //   lazyLoad: true,
 //   navSpeed: 700,
 //   margin: 10,
 //   responsive: {
 //     0: {
 //       items: 1,
 //     },
 //     640: {
 //       items: 2
 //     },
 //     768: {
 //       items: 2
 //     },
 //     896: {
 //       items: 2
 //     },
 //     1024: {
 //       items: 3
 //     },
 //     1280: {
 //       items: 4
 //     },
 //     1536: {
 //       items: 4
 //     }
 //   },
 //   nav: false
 // }

}
