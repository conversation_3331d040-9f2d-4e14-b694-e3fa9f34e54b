<div>
    <form [formGroup]="architectProfileForm" class="mb-9">
        <div class="grid grid-cols-2 gap-10 gap-y-5 mb-9">
            <h2 class="col-span-full text-accent text-2xl font-semibold block">
                {{"COMPANY DETAILS"| translate}}</h2>
            <!-- <h2 *ngIf="userType.name === 'Agent'" class="col-span-full text-accent text-2xl font-semibold block">AGENT DETAILS</h2> -->
                <div class=" col-span-full flex  items-center gap-9 text-white ">
                    <img [src]="companyLogo" alt="" class=" w-36 aspect-square rounded-full ">
                    <div>
                        <div class="flex gap-8">
                            <button (click)="selectImg()" class="bg-accent text-black text-sm font-bold px-8">
                                {{"CHOOSE FILE"| translate}}</button>
                            <input type="file" (change)="onFileChanged($event, 'cl')" #imageSelect id='imageSelect' hidden>
                            <div class="text-xs tracking-wider text-steelBlue leading-none">
                                <p class="mb-3">{{"Acceptance formats JPG, PNG only"| translate}}</p>
                                <p>{{"Max size is 500 kb and min size is 70 kb"| translate}}</p>
                            </div>
                        </div>
                    </div>
                </div>

            <div>
                <label for="" class="text-white text-sm ">{{"Name Of The Company/ Owner Name"| translate}}</label>
                <input type="text" formControlName="company_name"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <!-- <div *ngIf="userType.name === 'Agent'">
                <label for="" class="text-white text-sm ">Name Of The Company/ Owner Name</label>
                <input type="text" formControlName="company_name"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div> -->
            <div>
                <label for="" class="text-white text-sm ">{{"Expertise"| translate}}</label>
                <ng-select formControlName="expertise" (add)="addExpertise($event)" (remove)="removeExpertise($event)"
                    (clear)="listOfUserExpertise=[]" [multiple]="true" [closeOnSelect]="false" name=""
                    class="mt-3 text-black architect-expertise" placeholder="{{'Choose Expertise'| translate}}">
                    <ng-option class="text-black" *ngFor="let expertise of allExpertise"
                        [value]="expertise.id">{{expertise.name_en}}
                    </ng-option>
                </ng-select>
            </div>
            <div>
                <label for="" class="text-white text-sm mb-3">{{"Select Country"| translate}}</label>
                <select formControlName="country_id" (click)="getAllCountries()" (change)="getAllStates()"
                    class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2 h-10 placeholder-gray-400 focus:outline-none mt-3">
                    <!-- <option value="" disabled>Choose your country</option> -->
                    <option class="text-sm  " [ngValue]="country.country_id" *ngFor="let country of countries">
                        {{country.name}}</option>
                </select>
            </div>
            <div>
                <label for="" class="text-white text-sm ">{{"Select State"| translate}}</label>
                <select #stateEle formControlName="state_id" (change)="getAllAreas()"
                    class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2 h-10 placeholder-gray-400 focus:outline-none mt-3">
                    <option value="" selected>
                        Choose City
                    </option>

                    <option [ngValue]="state.state_id" *ngFor="let state of states">{{state.name}}
                    </option>
                </select>
            </div>
            <div *ngIf="areas.length > 0">
                <label for="" class="text-white text-sm mb-3">{{"Select City"| translate}}</label>
                <select #areaEle formControlName="area_id"
                    class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2 h-10 placeholder-gray-400 focus:outline-none mt-3">
                    <option [value]="null" *ngIf="architectProfileForm.controls.area_id.value !== null"><span>
                        Choose Area"| translate}}</span></option>
                    <option [ngValue]="area.area_id" *ngFor="let area of areas">{{area.name}}</option>
                </select>
            </div>
        </div>

        <div class="grid grid-cols-2 gap-10 gap-y-5 mb-9">
            <h2 class="col-span-full text-accent text-2xl font-semibold block">{{"Address Details"| translate}}</h2>
            <div>
                <label for="" class="text-white text-sm ">{{"Street Name/Street No"| translate}}</label>
                <input type="text" formControlName="street"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div>
                <label for="" class="text-white text-sm ">{{"Flat/Block No"| translate}}</label>
                <input type="text" formControlName="flat"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div>
                <label for="" class="text-white text-sm ">{{"Building"| translate}}</label>
                <input type="text" formControlName="building"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div>
                <label for="" class="text-white text-sm ">{{"Zip Code"| translate}}</label>
                <input type="text" formControlName="zip_code"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
        </div>

        <!-- <div class="grid grid-cols-2 gap-10 gap-y-5 ">
            <h2 class="col-span-full text-accent text-2xl font-semibold block">Upload Document</h2>
            <div>
                <label for="" class="text-white text-sm mb-2 block">Upload Your Photo Or Company Logo</label>
                <input type="file" (change)="onFileChanged($event,'cl')" formControlName="company_logo"
                    class="text-white block">
            </div>
            <div>
                <label for="" class="text-white text-sm mb-2 block">Upload Project Portfolio (PDF)</label>
                <input type="file" (change)="onFileChanged($event,'p')" formControlName="portfolio"
                    class="text-white block">
            </div>
            <div>
                <label for="" class="text-white text-sm mb-2 block">Upload Your Cover Project Photo</label>
                <input type="file" (change)="onFileChanged($event,'cp')" formControlName="project_photo"
                    class="text-white block">
            </div>
        </div> -->

    </form>
    <div class="grid grid-cols-2 gap-10">
        <button (click)="editProfile()" type="button" class="h-10 bg-accent w-full font-bold">{{"SAVE CHANGES"| translate}}</button>
    </div>



</div>