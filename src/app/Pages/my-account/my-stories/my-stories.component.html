<div>
    <h2 class="text-white text-center text-2xl font-bold mb-10">
        <span class="float-left cursor-pointer" *ngIf="!showList" (click)="showList = true">
            <img class="w-7" src="assets/icons/backArrow.svg" alt="">
        </span>
        {{"MY STORIES"| translate}}
    </h2>

    <div *ngIf="myStoriesArr.length > 0">
        <div class="grid grid-cols-12 gap-4" *ngIf="showList">
            <div *ngFor="let story of myStoriesArr" class="col-span-6 xl:col-span-3 lg:col-span-3">
                <div class="cursor-pointer bg-gradient-to-t from-[#3d62b1]  to-[#eceaea]" [routerLink]=""
                    (click)="setStroy(story?.images)">
                    <img class="w-full aspect-[4/5] object-cover mix-blend-multiply"
                        src="{{setThumbnail(story?.images)}}">
                    <div class="from-neutral-300 h-0 ">
                        <div class="p-2 block relative h-0 bottom-24 md:bottom-32 lg:bottom-28 ">
                            <img class="w-12 h-12 md:h-12 md:w-12 lg:h-14 lg:w-14 rounded-full mb-2"
                                src="{{story?.company_logo}}" alt="">
                            <h3 class="text-xs md:text-sm lg:text-sm font-semibold mb-1 text-white uppercase">
                                {{story?.title}}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-full" *ngIf="!showList">
            <!-- carousal -->
            <div class="justify-center items-center relative container ">
                <div class="h-full">
                    <owl-carousel-o [options]="storyOptions" #gallery class="galleryCarousal ">
                        <ng-container *ngFor="let story of storyArr">
                            <ng-template carouselSlide>
                                <img class="aspect-[2/5] object-cover " [src]="story?.image">
                            </ng-template>
                        </ng-container>
                    </owl-carousel-o>
                </div>
                <p (click)="gallery.prev()" class="hover:cursor-pointer absolute top-1/2 left-2 hover:scale-105 z-10">
                    <img class="h-7 w-7" src="assets/icons/arrow-left.png">
                </p>
                <p (click)="gallery.next()" class="hover:cursor-pointer absolute top-1/2 right-2 hover:scale-105 z-10">
                    <img class="h-7 w-7 rotate-180" src="assets/icons/arrow-left.png">
                </p>
            </div>
        </div>
    </div>

    <div *ngIf="myStoriesArr.length ==0" class="h-vh40 flex justify-center items-center text-xl text-white">
        {{"No stories were added"| translate}}
    </div>
</div>