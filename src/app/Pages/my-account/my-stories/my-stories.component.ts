import { Component, OnInit } from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { MyAccountService } from 'src/app/Services/My-account/my-account.service';
@Component({
  selector: 'app-my-stories',
  templateUrl: './my-stories.component.html',
  styleUrls: ['./my-stories.component.scss']
})
export class MyStoriesComponent implements OnInit {

  myStoriesArr: any = []
  thumbnail: string = ''
  showList: boolean = true

  userID: any
  constructor(
    private myAccountService: MyAccountService,
    private toastr: ToastrService,
    private configSettings: configSettings

  ) {
    this.configSettings.setShowLoader(true)
  }

  ngOnInit(): void {

    this.userID = this.configSettings.getUserID()
    this.myStories()
    console.log(this.userID)
  }

  // story carousal
  storyOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: true,
    lazyLoad: false,
    navSpeed: 700,
    rtl: false,
    autoHeight: true,
    autoWidth: true,
    center: false,
    margin: 25,
    responsive: {
      0: {
        items: 1,
      },
      640: {
        items: 1
      },
      768: {
        items: 1
      },
      896: {
        items: 1
      },
      1024: {
        items: 1,
      },
      1280: {
        items: 1,
      },
      1536: {
        items: 1
      }
    },
    nav: false
  }


  myStories() {
    const getParams = {

      // user_id: this.configSettings.getUserDetails().user_id
      user_id: this.userID,
      per_page: 9999
    }

    this.myAccountService.getMyStories(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.myStoriesArr = response.body.data.items
            this.configSettings.setShowLoader(false)

            this.myStoriesArr.map((items: any) => {
              // this.setThumbnail(items?.images)
              // console.log(this.setThumbnail(items?.images),'this.setThumbnail(items?.images)');

            })

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


  setThumbnail(value: any) {
    let thumbNail
    value.forEach((e: any) => {
      if (e.is_thumbnail == 1) {
        thumbNail = e?.image
      }
    })
    return thumbNail
  }

  storyArr: any = []
  setStroy(value: any) {
    this.showList = false
    this.storyArr = value

  }


}
