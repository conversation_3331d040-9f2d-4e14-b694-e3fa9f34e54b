<section>
    <div class="container mx-auto px-4 sm:px-12 md:px-16 xl:px-24 py-5 lg:py-16 font-normal">
        <div class="flex flex-col lg:flex-row gap-9 my-2">
            <div
                class="hidden lg:flex flex-row lg:flex-col w-full lg:w-[30%] xl:w-[24.5%]  bg-white rounded-none  xl:rounded-xl lg:rounded-xl lg:mb-2 lg:self-start">
                <div class="hidden xl:block lg:block text-center bg-[#ABBED4] rounded-t-md py-4 mb-2">
                    <h5 class="text-primary font-semibold lg:font-extrabold text-base lg:text-xl">
                        {{"MY ACCOUNT OVERVIEW"| translate}}</h5>
                </div>
                <div [routerLink]="['my-profile']" [routerLinkActive]="'text-accent'" #profile="routerLinkActive"
                    class="mobilemenu min-w-[160px] md:min-w-[230px] lg:min-w-full text-base  px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="profile.isActive == false" src="../../../assets/icons/Account.svg" alt=""
                            class="w-4">
                        <img *ngIf="profile.isActive == true" src="../../../assets/icons/Account-icon-active.svg" alt=""
                            class="w-4">
                        <span class="px-4 xl:text-sm  lg:text-sm text-base">{{"My Profile"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-36 rtl:md:right-48 ltr:left-36 ltr:md:left-48">|</span>
                </div>
                
                <!--  -->
                <div *ngIf="isAgent === true" [routerLink]="['agent-profile']" [routerLinkActive]="['text-accent']"
                    #agentProfile="routerLinkActive"
                    class="mobilemenu min-w-[250px] md:min-w-[275px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="agentProfile.isActive == false" src="../../../assets/icons/Account.svg" alt=""
                            class="w-4">
                        <img *ngIf="agentProfile.isActive == true" src="../../../assets/icons/Account-icon-active.svg"
                            alt="" class="w-4">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Agent Profile"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-52 rtl:md:left-64 ltr:left-52 ltr:md:left-64">|</span>
                </div>
                <div [routerLink]="'my-property-listing'" [routerLinkActive]="['text-accent']"
                    #propertyLisiting="routerLinkActive"
                    class="mobilemenu min-w-[170px] md:min-w-[230px] lg:min-w-full  text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="propertyLisiting.isActive == false" src="../../../assets/icons/Propertie.svg" alt=""
                            class="w-5">
                        <img *ngIf="propertyLisiting.isActive == true"
                            src="../../../assets/icons/Properties-icon-active.svg" alt="" class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Property"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-40 rtl:md:right-48 ltr:left-40 ltr:md:left-48">|</span>
                </div>
                <div *ngIf="isArchitect === true" [routerLink]="['architech-profile']"
                    #architectProfile="routerLinkActive" [routerLinkActive]="'text-accent'"
                    class="mobilemenu min-w-[250px] md:min-w-[275px]  lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="architectProfile.isActive == false" src="../../../assets/icons/Account.svg" alt=""
                            class="w-4">
                        <img *ngIf="architectProfile.isActive == true"
                            src="../../../assets/icons/Account-icon-active.svg" alt="" class="w-4">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Architect Profile"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-52 rtl:md:left-64 ltr:left-52 ltr:md:left-64">|</span>
                </div>
                <div *ngIf="isArchitect === true" [routerLinkActive]="'text-accent'" [routerLink]="['my-projects']"
                    #architectProjects="routerLinkActive"
                    class="mobilemenu min-w-[180px] md:min-w-[230px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="architectProjects.isActive == false" src="../../../assets/icons/Propertie.svg"
                            alt="" class="w-5">
                        <img *ngIf="architectProjects.isActive == true"
                            src="../../../assets/icons/Properties-icon-active.svg" alt="" class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Projects"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-40 rtl:md:right-48 ltr:left-40 ltr:md:left-48">|</span>
                </div>
                <div *ngIf="isOwner === true" [routerLink]="['company-profile']" [routerLinkActive]="['text-accent']"
                    #ownerProfile="routerLinkActive"
                    class="mobilemenu min-w-[180px] md:min-w-[230px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="ownerProfile.isActive == false" src="../../../assets/icons/Account.svg" alt=""
                            class="w-4">
                        <img *ngIf="ownerProfile.isActive == true" src="../../../assets/icons/Account-icon-active.svg"
                            alt="" class="w-4">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Company Profile"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-40 rtl:md:right-48 ltr:left-40 ltr:md:left-48">|</span>
                </div>
                <div routerLink="chats" [routerLinkActive]="['text-accent']" #chats="routerLinkActive"
                    class="mobilemenu rtl:min-w-[220px] rtl:md:min-w-[250px] ltr:min-w-[180px] ltr:md:min-w-[230px]  lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="chats.isActive == false" src="../../../assets/icons/Chat.svg" alt="" class="w-5">
                        <img *ngIf="chats.isActive == true" src="../../../assets/icons/chat-icon-active.svg" alt=""
                            class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Chat"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-40 rtl:md:right-48 ltr:left-40 ltr:md:left-48">|</span>
                </div>
                <div routerLink="my-stories" [routerLinkActive]="['text-accent']" #stories="routerLinkActive"
                    class="mobilemenu min-w-[180px] md:min-w-[230px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="stories.isActive == false" src="../../../assets/icons/My Stories.svg" alt=""
                            class="w-5">
                        <img *ngIf="stories.isActive == true" src="../../../assets/icons/My-Stories-icon-active.svg"
                            alt="" class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Stories"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-40 rtl:md:right-48 ltr:left-40 ltr:md:left-48">|</span>
                </div>

                <div routerLink="my-plans" [routerLinkActive]="'text-accent'" #myPlans="routerLinkActive"
                    class="mobilemenu  min-w-[180px] md:min-w-[230px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="myPlans.isActive == false" src="../../../assets/icons/My Plans.svg" alt=""
                            class="w-5">
                        <img *ngIf="myPlans.isActive == true" src="../../../assets/icons/My-Plans-icon-active.svg"
                            alt="" class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"My Plans"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-40 rtl:md:right-48 ltr:left-40 ltr:md:left-48">|</span>
                </div>
                <div routerLink="saved-searches" [routerLinkActive]="['text-accent']" #savedSearches="routerLinkActive"
                    class="mobilemenu min-w-[250px] md:min-w-[275px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="savedSearches.isActive == false" src="../../../assets/icons/View-Searches.svg"
                            alt="" class="w-5">
                        <img *ngIf="savedSearches.isActive == true"
                            src="../../../assets/icons/View-Searches-icon-saved.svg" alt="" class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"Saved Searches"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-52 rtl:md:right-64 ltr:left-52 ltr:md:left-64">|</span>
                </div>
                <div [routerLink]="['favorites']" [routerLinkActive]="['text-accent']" #favorites="routerLinkActive"
                    class="mobilemenu min-w-[180px] md:min-w-[230px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="favorites.isActive == false" src="../../../assets/icons/favorite-blue.svg" alt="" class="w-5">
                        <img *ngIf="favorites.isActive == true" src="../../../assets/icons/favorite-accent.svg" alt="" class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"Favorites"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 rtl:right-40 rtl:md:right-48 ltr:left-40 ltr:md:left-48">|</span>
                </div>
                <div routerLink="settings" [routerLinkActive]="['text-accent']" #settings="routerLinkActive"
                    class="mobilemenu min-w-[180px] md:min-w-[230px] lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer">
                    <div class="flex text-base  font-medium py-3 px-3 border-b-2 border-gray-300">
                        <img *ngIf="settings.isActive == false" src="../../../assets/icons/Settings.svg" alt=""
                            class="w-5">
                        <img *ngIf="settings.isActive == true" src="../../../assets/icons/Settings-icon-active.svg"
                            alt="" class="w-5">
                        <span class="px-4 xl:text-sm lg:text-sm text-base">{{"Settings"| translate}}</span>
                    </div>
                    <span class="block  h-0 lg:hidden text-lg text-primary  relative bottom-10 left-40">|</span>
                </div>
                <div class="mobilemenu rtl:min-w-[200px] rtl:lg:min-w-full text-base px-0 xl:px-3 lg:px-3 text-primary hover:text-accent cursor-pointer ">
                    <div class="flex text-base   font-medium py-3 px-3" (click)="openLogoutModal()">
                        <img [ngClass]="showLogoutModal ? 'hidden' : ''" src="../../../assets/icons/Logout.svg" alt="" class="w-5">
                        <img *ngIf="showLogoutModal" src="../../../assets/icons/Log out-accent.svg" alt="" class="w-5">
                        <span [ngClass]="showLogoutModal? 'text-accent':''" class="px-4 xl:text-sm lg:text-sm text-base">{{"Logout"| translate}}</span>
                    </div>
                </div>
            </div>

            <div class="w-full lg:w-[70%] xl:w-[75.5%]">
                <router-outlet></router-outlet>

            </div>


        </div>
    </div>
</section>
<div *ngIf="showLogoutModal" (click)="openLogoutModal()" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="showLogoutModal" class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 
-translate-y-1/2 p-5 bg-white w-[22rem] rounded-xl">

    <h1 class="mb-4 text-center text-base font-medium ">{{"Are you sure you want to log out?"| translate}}</h1>
    <div class="flex justify-center gap-2">
        <button (click)="logout()" class="block text-white bg-primary py-3 px-8">{{"Yes"| translate}}</button>
        <button (click)="openLogoutModal()" class="block text-white bg-red-500 py-3 px-8">{{"No"| translate}}</button>
    </div>
</div>