import { AfterViewChecked, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Client, Conversation, Message, ConversationUpdateReason, } from "@twilio/conversations";
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { from } from 'rxjs';
import { ChatService } from 'src/app/Services/Chat/chat.service';
import { ActivatedRoute, Router } from '@angular/router';
// declare const Twilio: any;

@Component({
  selector: 'app-chats',
  templateUrl: './chats.component.html',
  styleUrls: ['./chats.component.scss']
})
export class ChatsComponent implements OnInit, AfterViewChecked {

  contacts: boolean = true

  constructor(
    private chatService: ChatService,
    private toastr: ToastrService,
    private configSettings: configSettings,
    private activiatedRoute: ActivatedRoute,
    private route: Router,
  ) { }

  token: any = ''
  sid: any
  userID = this.configSettings.getUserID()


  chatClient: Client;
  conversation: Conversation
  currentTime: any


  channel: any


  fromID: number = Number(this.configSettings.getUserID())
  toID: number | undefined = undefined
  // text box
  message: any = ''
  @ViewChild('sendMessageEle') sendMessageEleRef: ElementRef
  @ViewChild('scrollMe') scrollMeEleRef: ElementRef
  conversationArr: any = []
  conversationArrToUpdate: any = []
  scrolltop: number = 0;


  currentPlaceholderName: string
  // ////////////////////////////////////////////////////////////////////////////////////////////////
  ngOnInit() {

    this.getContactsApi()

    this.activiatedRoute.params.subscribe(params => {
      this.chatService.setConversation([])
      if (params.toID) {
        this.configSettings.setShowLoader(true)
        this.toID = Number(params.toID)
        this.activiatedRoute.queryParams.subscribe(params => {          
          this.currentPlaceholderName = params.name
        })
        this.connect()

      }
    })

    // console.log(this.fromID)
  }

  ngAfterViewChecked(): void {
    this.scrollMeEleRef.nativeElement.scrollTop = this.scrollMeEleRef?.nativeElement.scrollHeight

  }


  connect() {
    const postParams = {
      from_id: this.fromID,
      to_id: this.toID
    }


    this.chatService.getToken({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.token = response.body.data.token
            this.sid = response.body.data.channel_id
            // console.log(this.sid)
            this.chatClient = new Client(this.token);
            this.chatClient.getSubscribedConversations().then(
              res => {
                console.log(res.items[0].lastMessage)
              }
            )
            this.listenToEvents();

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

    this.chatService.getConversation().subscribe({
      next: (response) => {
        this.conversationArr = response
        // setTimeout(() => {
          // this.scrollMeEleRef.nativeElement.scrollTop = this.scrollMeEleRef?.nativeElement.scrollHeight
          // this.scrollMeEleRef.nativeElement.scrollTo({left: 0 , top: this.scrollMeEleRef.nativeElement.scrollHeight, behavior: 'smooth'})
        // }, 0);
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }

  listenToEvents() {
    this.chatClient.on('stateChanged', (state) => {
      if (state === 'initialized') {
        console.log('initialized');
        this.getChannnel(this.sid).then((channel: any) => {
          console.log('channel', channel)
          this.channel = channel
          channel.getMessages(30, 99999, 'backwards').then((m: any) => {
            // console.log(m.items[5].state.body)
            // console.log(m.items)

            console.log(this.chatClient.getSubscribedConversations())
            m.items.forEach((el: any) => {
              let body = el.state.body
              let author = el.state.author
              let time = el.state.timestamp
              this.conversationArrToUpdate.push({ body, author, time })
            });
            this.chatService.setConversation(this.conversationArrToUpdate)
            this.configSettings.setShowLoader(false)
          })
        }).catch((e: any) => {
          console.log(e)
        })
      }
    });

    this.chatClient.on('conversationAdded', (conv: Conversation) => {
      // if (conv.dateCreated > this.currentTime) {
      // console.log('new chat created', conv);
      // }
    });

    this.chatClient.on('messageAdded', (msg: Message) => {
      console.log('message added', msg);

      this.getLastMessage(this.channel)
    });

    this.chatClient.on('connectionError', (err) => {
      console.log('Connection not made, please refresh the page.', err);
    });
  }

  // twilio functions
  getChannnel(sid: string): any {
    return this.chatClient.getConversationBySid(sid);
  }

  getChannelChat(channel: any, index: any) {
    let size = 30;
    return channel.getMessages(size, index);
  }

  createPrivateChannel(friendlyName: string) {
    return this.chatClient.createConversation({ friendlyName });
  }

  async getLastMessage(channel: Conversation) {
    let response = await channel.getMessages(1);
    console.log('response', response, response.items[0].dateCreated, response.items[0].author, response.items[0].body)
    let body = response.items[0].body
    let author = response.items[0].author
    let time = response.items[0].dateCreated
    this.conversationArrToUpdate.push({ body, author, time })
    this.chatService.setConversation(this.conversationArrToUpdate)
    return response.items.length > 0 ? response.items[0].body : '';
  }

  async getUnconsumedMessage(channel: Conversation) {
    let response = await channel.getUnreadMessagesCount();
    if (response == null) {
      let lastMsg = await channel.getMessages(1);
      response = lastMsg && lastMsg.items.length > 0 ? lastMsg.items[0].index + 1 : 0;
    }
    return response;
  }



  sendMessage() {
    if (this.message === '') {
      this.toastr.warning('', 'Enter text to send')
      return
    }
    this.channel.sendMessage(this.message)
    // this.channel.prepareMessage()
    // .setBody('Hello!')
    // .setAttributes({foo: 'bar'})
    // .addMedia(media2)
    // .build()
    // .send();
    this.message = ''
  }

  enterKeyWords(e: any) {
    if (e.key === "Enter") {
      this.sendMessageEleRef.nativeElement.click()
      e.preventDefault();
    }
  }


  // //////////////////////////////////////////////////////////////////////////////////////////////

  chatWith(ID: number, name: string) {
    this.route.navigate(['/my-account/chats', ID], { queryParams: { name: name } })
  }




  contactList: any = []

  getContactsApi() {

    const getParams = {
      user_id: this.configSettings.getUserID()
    }

    this.chatService.getContacts(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.contactList = response.body.data
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }
}
