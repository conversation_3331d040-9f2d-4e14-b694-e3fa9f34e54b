<div class="text-white">
    <h2 class="text-white text-2xl text-center font-bold mb-10">{{"MY CHAT" | translate}}</h2>
    <div *ngIf="contactList?.length > 0">
        <div *ngIf="toID === undefined" class="w-full min-h-vh60 bg-[#1a395c]">
            <div class=" bg-[#48617d] h-[5.5rem] flex justify-end items-center px-7">
                <div class="w-3/5 h-11 bg-[#6a7e94] rounded-full text-xs flex gap-3 items-center px-7">
                    <img src="../../../../assets/icons/search.svg" class="w-4 h-20" alt="">
                    <input type="text" placeholder="{{'Search here...' | translate}}"
                        class="bg-transparent w-full outline-none" name="" id="">
                </div>
            </div>
            <div *ngFor="let contact of contactList"
                class=" h-24 border-b border-borderColor flex items-center px-7 cursor-pointer">
                <a [routerLink]="['/my-account/chats', contact.to_id]" [queryParams]="{name: contact.to_name}"  class="flex items-center gap-6 w-full">
                    <img [src]="contact.to_img" class="w-16 aspect-square rounded-full" alt="">
                    <div class="flex flex-col gap-1 w-full">
                        <div class="flex justify-between w-full">
                            <p class="font-bold">{{contact.to_name}}</p>
                            <!-- <span class="text-xs text-[#8da3bb]">3 Mins</span> -->
                        </div>
                        <p class="text-sm">{{contact.message}}</p>
                    </div>
                </a>
            </div>
        </div>
        <!--  h-vh60 -->
        <div *ngIf="toID !== undefined" class="w-full h-[40rem] bg-[#1a395c] flex flex-col relative ">
            <div class=" bg-[#48617d] h-[5.5rem] min-h-[5.5rem] flex items-center px-7 ">
                <div class="flex items-center gap-5">
                    <span [routerLink]="['/my-account/chats']" class="cursor-pointer"><img
                            src="../../../../assets/icons/arrow-semi-left.svg" class="w-7 h-7"></span>
                    <p>{{currentPlaceholderName}}</p>
                </div>
            </div>
            <!-- <div class=" h-24 flex items-center px-7 cursor-pointer">
        </div> -->
            <div id="chatbox" #scrollMe [scrollTop]="scrollMe.scrollHeight"
                class="flex-grow h-full p-10 flex flex-col gap-4 overflow-y-auto">

                <div *ngFor="let item of conversationArr" class="text-sm max-w-[45%]"
                    [ngClass]="item.author == fromID ? ' self-end' : ' self-start'">
                    <p class="mb-1"
                        [ngClass]="item.author == fromID ? 'text-left rounded-t-lg p-3 rounded-l-lg bg-primary ':'text-left bg-[#abbed4] rounded-t-lg p-3 rounded-r-lg ' ">
                        {{item.body}}</p>
                    <p [ngClass]="item.author == fromID ? 'text-right': 'text-left'" class="text-xs">{{ item.time |
                        date:'medium'
                        }}</p>
                </div>

            </div>
            <div class=" flex items-center rounded-full overflow-hidden mx-2 lg:mx-10 my-7 p-2 bg-[#6a7e94]">
                <!-- <button class=""><img src="../../../../assets/icons/attach-icon.png" class="w-8 aspect-square"
                        alt=""></button> -->
                <input [(ngModel)]="message" name="textBox" type="text" placeholder="{{'Type message....' | translate}}"
                    (keydown)="enterKeyWords($event)" class="py-5 px-2 outline-none w-full bg-transparent text-white">
                <button #sendMessageEle class="" (click)="sendMessage()"><img
                        src="../../../../assets/icons/chat-send-icon.svg" class="w-10 aspect-square" alt=""></button>
            </div>
        </div>
    </div>

    
    
</div>
<div *ngIf="contactList?.length == 0" class="h-vh40 flex justify-center items-center text-xl text-white">
    {{"No conversations were found" | translate}}
</div>