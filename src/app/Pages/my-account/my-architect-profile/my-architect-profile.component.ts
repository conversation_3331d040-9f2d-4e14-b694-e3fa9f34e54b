import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { UserAuthenticationService } from 'src/app/Services/user-authentication.service';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { AddressService } from 'src/app/Services/Address/address.service';
import { ExpertiseService } from 'src/app/Services/Expertise/expertise.service';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-my-architect-profile',
  templateUrl: './my-architect-profile.component.html',
  styleUrls: ['./my-architect-profile.component.scss'],
})
export class MyArchitectProfileComponent implements OnInit {
  name: any;
  constructor(
    private fb: FormBuilder,
    private toastr: ToastrService,
    private userAuthenticationService: UserAuthenticationService,
    private configSettings: configSettings,
    private addressService: AddressService,
    private expertiseService: ExpertiseService,
    private translateService: TranslateService,
    private route: ActivatedRoute,
  ) {
    // this.configSettings.setShowLoader(true);
  }

  userDetails: any;
  userType: any;
  @ViewChild('stateEle') stateEleRef: any;
  @ViewChild('areaEle') areaEleRef: any;

  ngOnInit(): void {
    this.getExpertise();
    this.getCountry();
    this.getUserTypes()
    this.userDetails.expertise.forEach((element: any) => {
      this.listOfUserExpertise.push(element.id)
    });


    this.architectProfileForm.patchValue({
      architect_name_en: this.userDetails?.architect_name_en,
      architect_name_ar: this.userDetails?.architect_name_en,
      country_id: this.userDetails?.country_id,
      area_id: this.userDetails?.area_id,
      state_id: this.userDetails?.state_id,

      street: this.userDetails?.street,
      flat: this.userDetails?.flat,
      building: this.userDetails?.building,
      zip_code: this.userDetails?.zipcode,
      address_line_en: this.userDetails?.address_line_en,
      address_line_ar: this.userDetails?.address_line_ar,
      aboutUsEn: this.userDetails?.about_en,
      aboutUsAr: this.userDetails?.about_ar,
      expertise: this.listOfUserExpertise

    });

    this.companyLogo = this.userDetails?.company_logo

  }

  fileName: any;
  url: any;
  type: any;

  companyLogoName: any;
  companyLogo: any;

  portfolioName: any;
  portfolio: any;

  projectName: any;
  project: any;
  lang: string = this.configSettings.getLang()
  langSet: boolean;


  countries: any = [];
  states: any = [];
  areas: any = [];

  oninit = true;

  allExpertise: any;
  listOfUserExpertise: any = [];
  @ViewChild('imageSelect') imageSelect: ElementRef;
  count: number = 0
  getUserTypes() {
    this.userDetails = this.configSettings.getUserDetails()
    let a = this.userDetails.user_types;
    for (let i = 0; i < a.length; i++) {
      this.count++;
      const element = a[i];
      this.userType = element.type;
    }
  }


  getExpertise() {
    const getParams = {};
    this.expertiseService.getExpertise(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.allExpertise = response.body.data;
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  addExpertise(e: any) {
    this.listOfUserExpertise.push(e);
  }

  removeExpertise(e: any) {
    this.listOfUserExpertise.splice(
      this.listOfUserExpertise.indexOf(e.value),
      1
    );
  }

  // getAllCountries() {
  //   this.oninit = false;
  //   // this.stateEleRef.nativeElement.value = ''
  //   // this.areaEleRef.nativeElement.value = ''
  // }

  getCountry() {
    const getParams = {};
    this.addressService.getCountry(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.countries = response?.body?.data;
            this.getAllStates();
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  getAllStates() {
    this.getState(this.architectProfileForm.controls['country_id'].value);
  }

  getState(value: any) {
    const getParams = {
      country_id: value,
    };
    this.addressService.getState(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.states = response?.body?.data;

            if (this.oninit == false) {
              this.architectProfileForm.patchValue({
                state_id: null,
                area_id: null,
              });
            } else {
              this.architectProfileForm.patchValue({
                state_id: this.userDetails?.state_id,
              });
            }

            this.getAllAreas();
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  getAllAreas() {
    this.getAreas(this.architectProfileForm.controls['state_id'].value);
  }

  getAreas(value: any) {
    const getParams = {
      state_id: value,
    };
    this.addressService.getAreas(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.configSettings.setShowLoader(false);
            this.areas = response?.body?.data;

            if (this.oninit == false) {
              this.architectProfileForm.patchValue({
                area_id: null,
              });
            } else {
              this.architectProfileForm.patchValue({
                area_id: this.userDetails?.area_id,
              });
              this.oninit = false;

            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  selectImg() {
    this.imageSelect.nativeElement.click();
  }



  onFileChanged(event: any, str: any) {
    if (event.target.files && event.target.files[0]) {

      if (event.target.files[0].type === 'image/jpeg' ||
        event.target.files[0].type === 'image/png' ||
        event.target.files[0].type === 'image/jpg') {
        const filesAmount = event.target.files.length;
        for (let i = 0; i < filesAmount; i++) {
          const reader = new FileReader();

          reader.onload = (event: any) => {
            if (str == 'cl') {
              this.architectProfileForm.patchValue({
                company_logo: event.target.result,
              });
              this.companyLogo = event.target.result
            } else if (str == 'p') {
              this.architectProfileForm.patchValue({
                portfolio: event.target.result,
              });
            } else if (str == 'cp') {
              this.architectProfileForm.patchValue({
                project_photo: event.target.result,
              });
            }
          };

          reader.readAsDataURL(event.target.files[i]);
        }
      }
    }
  }


  architectProfileForm = new FormGroup({
    architect_name_en: new FormControl(''),
    architect_name_ar: new FormControl(''),
    country_id: new FormControl(''),
    state_id: new FormControl(''),
    area_id: new FormControl(''),
    aboutUsEn: new FormControl(''),
    aboutUsAr: new FormControl(''),
    address_line_en: new FormControl(''),
    address_line_ar: new FormControl(''),
    street: new FormControl(''),
    flat: new FormControl(''),
    building: new FormControl(''),
    zip_code: new FormControl('', [Validators.pattern("^[0-9]{6,8}$")]),
    company_logo: new FormControl(''),
    portfolio: new FormControl(''),
    project_photo: new FormControl(''),
    expertise: new FormControl('', Validators.required)
  });

  editProfile() {

    if (
      (this.architectProfileForm.controls['architect_name_en'].value === null ||
        this.architectProfileForm.controls['architect_name_en'].value === '')
    ) {
      this.translateService.get('Architect name (English) is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['architect_name_ar'].value === null ||
        this.architectProfileForm.controls['architect_name_ar'].value === '')
    ) {
      this.translateService.get('Architect name (Arabic) is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['expertise'].value === null ||
      this.architectProfileForm.controls['expertise'].value === ' ')
    ) {
      this.translateService.get('Expertise is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
   
    if (
      this.architectProfileForm.controls['country_id'].value === null ||
      this.architectProfileForm.controls['country_id'].value === ''
    ) {
      this.translateService.get('Country is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }

    if (
      this.architectProfileForm.controls['state_id'].value === null ||
      this.architectProfileForm.controls['state_id'].value === ''
    ) {
      this.translateService.get('State is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }

    if (
      this.areas.length > 0 &&
      (this.architectProfileForm.controls['area_id'].value === null ||
        this.architectProfileForm.controls['area_id'].value === '')
    ) {
      this.translateService.get('Area is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['aboutUsEn'].value === null ||
        this.architectProfileForm.controls['aboutUsEn'].value === '')
    ) {
      this.translateService.get('About us in english is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['aboutUsAr'].value === null ||
        this.architectProfileForm.controls['aboutUsAr'].value === '')
    ) {
      this.translateService.get('About us in arabic is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['street'].value === null ||
        this.architectProfileForm.controls['street'].value === '')
    ) {
      this.translateService.get('Street name is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['flat'].value === null ||
        this.architectProfileForm.controls['flat'].value === '')
    ) {
      this.translateService.get('Flat number is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['building'].value === null ||
        this.architectProfileForm.controls['building'].value === '')
    ) {
      this.translateService.get('Building name is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['zip_code'].value === null ||
        this.architectProfileForm.controls['zip_code'].value === '')
    ) {
      this.translateService.get('Zipcode is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (!this.architectProfileForm.controls['zip_code'].valid) {
      this.translateService.get('Enter a valid zipcode').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['address_line_en'].value === null ||
        this.architectProfileForm.controls['aboutUsAr'].value === '')
    ) {
      this.translateService.get('About us in arabic is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }
    if (
      (this.architectProfileForm.controls['address_line_ar'].value === null ||
        this.architectProfileForm.controls['aboutUsAr'].value === '')
    ) {
      this.translateService.get('About us in arabic is required').subscribe(res => {
        this.toastr.error('', res);
      })
      return;
    }


    // if (
    //   (this.architectProfileForm.controls['company_name'].value === null ||
    //     this.architectProfileForm.controls['company_name'].value === '') &&
    //   this.userType.name === 'Owner'
    // ) {
    //   this.toastr.error('', 'Company Name/Owner name is required');
    //   return;
    // }



    this.configSettings.setShowLoader(true);

    const postParams = {
      user_id: this.userDetails.user_id,
      // company_name_en: this.architectProfileForm.controls['company_name'].value,
      // company_name_ar: this.architectProfileForm.controls['company_name'].value,

      architect_name_en:
        this.architectProfileForm.controls['architect_name_en'].value,
      architect_name_ar:
        this.architectProfileForm.controls['architect_name_ar'].value,

      expertise: this.listOfUserExpertise.toString(),

      country_id: this.architectProfileForm.controls['country_id'].value,
      state_id: this.architectProfileForm.controls['state_id'].value,
      area_id: this.architectProfileForm.controls['area_id'].value,
      street: this.architectProfileForm.controls['street'].value,
      flat: this.architectProfileForm.controls['flat'].value,
      building: this.architectProfileForm.controls['building'].value,
      zipcode: this.architectProfileForm.controls['zip_code'].value,
      company_logo: this.architectProfileForm.controls['company_logo'].value,
      portfolio: this.architectProfileForm.controls['portfolio'].value,
      project_photo: this.architectProfileForm.controls['project_photo'].value,
      about_en: this.architectProfileForm.controls['aboutUsEn'].value,
      about_ar: this.architectProfileForm.controls['aboutUsAr'].value,
    };


    this.userAuthenticationService.postEditProfile({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            window.scroll({
              top: 0,
              left: 0,
              behavior: 'smooth'
            });
            if (this.userType === 'UR') {
              this.toastr.success(response.body.message);
            }
            if (this.userType === 'AG') {
              this.translateService.get('Agent details successfully updated.').subscribe(res => {
                this.toastr.success('', res);
              })
            }
            else if (this.userType === 'AR') {
              this.translateService.get("Architect details successfully updated.").subscribe(res => {
                this.toastr.success('', res);
              })
            }
            this.configSettings.setUserDetails(response.body.data);
            this.configSettings.setShowLoader(false);
          }
        }
      },
      error: (err) => {
        this.toastr.error(err.error.message);
      },
    });
  }
}
