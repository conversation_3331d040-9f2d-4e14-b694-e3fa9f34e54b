<div>
    <form [formGroup]="architectProfileForm" class="mb-9">
        <div class="grid grid-cols-2 gap-10 gap-y-5 mb-9">
            <!-- <h2 *ngIf="userType.name === 'Agent'" class="col-span-full text-accent text-2xl font-semibold block">AGENT DETAILS</h2> -->
            <h2 class="col-span-full text-white text-center text-2xl font-semibold block">
                {{"ARCHITECT DETAILS"| translate}}</h2>

            <div class=" col-span-full flex  items-center gap-9 text-white ">
                <img [src]="companyLogo" alt="" class=" w-36 aspect-square rounded-full ">
                <div>
                    <div class="flex flex-col md:flex-row gap-8">
                        <button (click)="selectImg()" class="bg-accent text-black text-sm py-3 font-bold px-8">
                            {{"CHOOSE FILE"| translate}}</button>
                        <input type="file" (change)="onFileChanged($event, 'cl')" #imageSelect id='imageSelect' hidden>
                        <div class="text-xs tracking-wider text-steelBlue leading-none">
                            <p class="mb-3">{{"Acceptance formats JPG, PNG only"| translate}}</p>
                            <p>{{"Max size is 500 kb and min size is 70 kb"| translate}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div *ngIf="userType.name === 'Agent'">
                <label for="" class="text-white text-sm ">Name Of The Company/ Owner Name</label>
                <input type="text" formControlName="company_name"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div> -->
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Name Of The Architect"| translate}} ({{"English"| translate}})<span class="text-red-600">*</span></label>
                <input type="text" formControlName="architect_name_en"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Name Of The Architect"| translate}} ({{"Arabic"| translate}})<span class="text-red-600">*</span></label>
                <input type="text" dir="rtl" formControlName="architect_name_ar"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class="relative col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Expertise"| translate}}<span class="text-red-600">*</span></label>

                <ng-select formControlName="expertise" (add)="addExpertise($event)" (remove)="removeExpertise($event)"
                    (clear)="listOfUserExpertise=[]" [multiple]="true" [closeOnSelect]="false" 
                    class="mt-3 text-black architect-expertise" placeholder="{{'Choose Expertise' | translate}}">
                    <ng-option class="text-black" *ngFor="let expertise of allExpertise"
                        [value]="expertise.id">{{expertise['name_'+lang]}}
                    </ng-option>
                </ng-select>
                <span class="absolute  bottom-4 z-10 rtl:left-3 rtl:right-[unset] right-3 opacity-70">
                    <img src="../../../assets/icons/arrow-semi-down.svg" class="h-2 w-3 ">
                </span>
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm mb-3">{{"Country"| translate}}<span class="text-red-600">*</span></label>
                <!-- <select formControlName="country_id" (click)="getAllCountries()" (change)="getAllStates()"
                    class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2 h-10 placeholder-gray-400 focus:outline-none mt-3">
                    <option value="" selected >Select country</option>
                    <option class="text-sm  " ngDefault="country.country_id" *ngFor="let country of countries">
                        {{country.name}}</option>
                </select> -->
                <span class=" downarrow">
                    <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                        class="rtl:left-2.5 rtl:right-[unset]"></span>
                <ng-select formControlName="country_id" (change)="getState($event)"
                    class="mt-3 text-black architect-select" placeholder="{{'Choose Country' | translate}}">
                    <ng-option class="text-black" *ngFor="let country of countries"
                        [value]="country.country_id">{{country.name | uppercase}}
                    </ng-option>
                </ng-select>

            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"State" | translate}}<span class="text-red-600">*</span></label>
                <!-- <select #stateEle formControlName="state_id" (change)="getAllAreas()"
                    class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2 h-10 placeholder-gray-400 focus:outline-none mt-3">
                    <option value="" selected>
                        {{"Select area" | translate}}
                    </option>

                    <option [ngValue]="state.state_id" *ngFor="let state of states">{{state.name}}
                    </option>
                </select> -->
                <span class=" downarrow">
                    <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                        class="rtl:left-2.5 rtl:right-[unset]"></span>
                <ng-select formControlName="state_id" (change)="getAreas($event)"
                    class="mt-3 text-black architect-select" placeholder="{{'Choose State' | translate}}">
                    <ng-option class="text-black" *ngFor="let state of states" [value]="state.state_id" >{{state.name}}
                    </ng-option>
                </ng-select>
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm mb-3">{{"Area"| translate}}<span class="text-red-600">*</span></label>
                <!-- <select #areaEle formControlName="area_id"
                    class="block w-full rounded-sm text-sm border border-sky-700 bg-white px-2 py-2 h-10 placeholder-gray-400 focus:outline-none mt-3">
                    <option [value]="null" *ngIf="architectProfileForm.controls.area_id.value !== null"><span>
                            {{"Choose Area"| translate}}</span></option>
                    <option [ngValue]="area.area_id" *ngFor="let area of areas">{{area.name}}</option>
                </select> -->
                <span class=" downarrow">
                    <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                        class="rtl:left-2.5 rtl:right-[unset]"></span>
                <ng-select formControlName="area_id" class="mt-3 text-black architect-select" placeholder="{{'Choose Area' | translate}}"><span class="text-red-600">*</span>
                    <ng-option class="text-black" *ngFor="let area of areas" [value]="area.area_id">{{area.name}}
                    </ng-option>
                </ng-select>
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"About Us"| translate}} ({{"English"| translate}})<span class="text-red-600">*</span></label>
                <textarea cols="30" rows="5" type="text" formControlName="aboutUsEn" placeholder="In English"
                    class="bg-white w-full text-base border-0 h-28 mt-3 rounded-sm outline-none py-2 px-3"></textarea>
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"About Us"| translate}} ({{"Arabic"| translate}})<span class="text-red-600">*</span></label>
                <textarea cols="30" rows="5" type="text" dir="rtl" placeholder="العربية" formControlName="aboutUsAr"
                    class="bg-white w-full text-base border-0 h-28 mt-3 rounded-sm outline-none py-2 px-3"></textarea>
            </div>
        </div>

        <div class="grid grid-cols-2 gap-10 gap-y-5 mb-9">
            <h2 class="col-span-full text-accent text-2xl font-semibold block">{{"Address Details"| translate}}</h2>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Street Name/Street No"| translate}}<span class="text-red-600">*</span></label>
                <input type="text" formControlName="street"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Flat/Block No"| translate}}<span class="text-red-600">*</span></label>
                <input type="text" formControlName="flat"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Building"| translate}}<span class="text-red-600">*</span></label>
                <input type="text" formControlName="building"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Zip Code"| translate}}<span class="text-red-600">*</span></label>
                <input type="text" formControlName="zip_code"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3" maxlength="8">
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Address Line"| translate}}({{"English"| translate}})<span class="text-red-600">*</span></label>
                <input type="text" formControlName="address_line_en"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class=" col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Address Line"| translate}}({{"Arabic"| translate}})<span class="text-red-600">*</span></label>
                <input type="text" formControlName="address_line_ar" dir="rtl"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
        </div>

        <!-- <div class="grid grid-cols-2 gap-10 gap-y-5 ">
            <h2 class="col-span-full text-accent text-2xl font-semibold block">Upload Document</h2>
            <div>
                <label for="" class="text-white text-sm mb-2 block">Upload Your Photo Or Company Logo</label>
                <input type="file" (change)="onFileChanged($event,'cl')" formControlName="company_logo"
                    class="text-white block">
            </div>
            <div>
                <label for="" class="text-white text-sm mb-2 block">Upload Project Portfolio (PDF)</label>
                <input type="file" (change)="onFileChanged($event,'p')" formControlName="portfolio"
                    class="text-white block">
            </div>
            <div>
                <label for="" class="text-white text-sm mb-2 block">Upload Your Cover Project Photo</label>
                <input type="file" (change)="onFileChanged($event,'cp')" formControlName="project_photo"
                    class="text-white block">
            </div>
        </div> -->

    </form>
    <div class="grid grid-cols-2 gap-10">
        <button (click)="editProfile()" type="button" class="col-span-2 md:col-span-1 rounded-sm h-10 bg-accent w-full font-bold">{{"SAVE CHANGES"|
            translate}}</button>
    </div>



</div>