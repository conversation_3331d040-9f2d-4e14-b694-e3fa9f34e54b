import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { SavedSearchesService } from 'src/app/Services/Saved-searches/saved-searches.service';

@Component({
  selector: 'app-saved-searches',
  templateUrl: './saved-searches.component.html',
  styleUrls: ['./saved-searches.component.scss']
})
export class SavedSearchesComponent implements OnInit {
  constructor(
    private configSettings: configSettings,
    private savedSearchesService: SavedSearchesService,
    private toastr: ToastrService,
    private router: Router

  ) { }

  // variables
  userID: any
  recentSearchesList: any

  confirmationModal: boolean = false
  searchID: any = ''
  ngOnInit(): void {
    this.configSettings.setShowLoader(true)

    this.userID = this.configSettings.getUserDetails().user_id

    this.getRecentSearches()

    this.savedSearchesService.getSearchList().subscribe(res => {
      if (res === null) {
        return
      } else {
        this.recentSearchesList = res;
      }
    });
  }

  confirmDeletion(deletedID: any, event: any) {
    this.confirmationModal = true
    if (event == 'e') {

    } else {
      event.stopPropagation()
    }
    this.searchID = deletedID
  }

  getRecentSearches() {
    this.configSettings.setShowLoader(true)

    const getParams = {
      per_page: 9999

    }

    const postParams = {
      user_id: this.userID,
      search_id: this.searchID

    }

    // console.log(postParams)

    this.savedSearchesService.getRecentSearchListing(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            // this.savedSearchesService.setSearchList(response.body.data)
            this.recentSearchesList = response.body.data.recent_searches
            this.confirmationModal = false

            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }


  routeTo(value: any) {
    this.router.navigate(['/property-listing'], { queryParams: { recentSearchID: value } });
  }
}
