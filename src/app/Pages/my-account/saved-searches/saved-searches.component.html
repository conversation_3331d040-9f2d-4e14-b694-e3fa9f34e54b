<div class="text-white ">
    <h2 class="text-2xl font-bold text-center mb-9">{{"SAVED SEARCHES"| translate}}</h2>

    <div *ngIf="recentSearchesList?.length > 0">
        <div *ngFor="let recentSearch of recentSearchesList" class="">
            <div (click)="routeTo(recentSearch.id)"
                class="cursor-pointer flex gap-5 border-b border-borderColor pb-10 mb-10">
                <div class="p-2 rounded-full bg-[#667b92] self-start"><img
                        src="../../../../assets/icons/history-icon.svg" class="h-5 aspect-square">
                </div>
                <div class="w-full">
                    <!-- title section -->
                    <div class="flex justify-between items-center mb-5">
                        <p class="font-semibold">{{'Properties for '}} <span
                                *ngIf="recentSearch.property_type == 'S'">{{'Sale'}}</span><span
                                *ngIf="recentSearch.property_type == 'R'">{{'Rent'}}</span><span
                                *ngIf="recentSearch.country !== null">{{' in ' + recentSearch.country}}</span> </p>
                        <div (click)="confirmDeletion(recentSearch.id, $event)"
                            class="p-1 rounded-full bg-white cursor-pointer">
                            <img src="../../../../assets/icons/trash-can.svg" class="h-4 aspect-square">
                        </div>
                    </div>
                    <!-- time section -->
                    <div class="flex gap-3 mb-5">
                        <img src="../../../../assets/icons/clock.svg" class="h-5 aspect-square">
                        <p class="text-lightGray">{{recentSearch.searched_on}}</p>
                    </div>
                    <!-- more info section -->
                    <div class="grid grid-cols-3 ">
                        <div class="flex flex-col gap-2">
                            <p class="text-sm text-lightGray">{{"Purpose"| translate}}</p>
                            <p class="font-semibold"><span
                                    *ngIf="recentSearch.property_type == 'S'">{{'Sale'}}</span><span
                                    *ngIf="recentSearch.property_type == 'R'">{{'Rent'}}</span></p>
                        </div>
                        <!-- <div class="flex flex-col gap-2">
                            <p class="text-sm text-lightGray">{{"Frequency"| translate}}</p>
                            <p class="font-semibold">{{"Yearly/Monthly"| translate}}</p>
                        </div> -->
                        <div class="flex flex-col gap-2">
                            <p class="text-sm text-lightGray">{{"Type"| translate}}</p>
                            <p *ngIf="recentSearch.property_category !== null" class="font-semibold">
                                {{recentSearch.property_category}}</p>
                            <p *ngIf="recentSearch.property_category === null" class="font-semibold">{{"All" |
                                translate}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="recentSearchesList?.length <= 0" class="h-vh40 flex justify-center items-center text-xl text-white">
        {{"No searches were saved"| translate}}
    </div>
</div>

<div *ngIf="confirmationModal" (click)="confirmationModal = false" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="confirmationModal"
    class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white  rounded-xl text-sm text-white">
    <h1 class="mb-7 text-black">{{'Are you sure you want to delete this search?'}}</h1>
    <div class="flex justify-around">
        <button (click)="getRecentSearches()" class="bg-green-500 px-6 py-2">
            {{'Yes' | translate}}
        </button>
        <button (click)="confirmationModal = false" class="bg-red-500 px-6 py-2">
            {{'No' | translate}}
        </button>
    </div>
</div>