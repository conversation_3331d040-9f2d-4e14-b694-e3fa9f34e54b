<div *ngIf="editProfile === false" class="rounded-md lg:mb-2 text-center text-white ">
    <h5 class="font-extrabold text-2xl pb-10">{{"MY PROFILE"| translate}}</h5>
    <div class="font-extrabold text-xl">
        <img [src]="this.userDetails?.profile_img" alt="" class="mx-auto mb-5 w-40 aspect-square rounded-full">
        {{this.userDetails?.first_name}} {{this.userDetails?.last_name}}
    </div>
    <div class="py-4 text-primary">
        <button (click)="openEditProfile()" class="text-sm font-bold py-2 px-5 bg-accent rounded-sm">
            {{"EDIT PROFILE"| translate}}</button>
    </div>
    <div class="text-xl font-normal justify-center flex flex-col  mx-auto md:flex-row gap-6 lg:gap-16 mt-4 mb-9">
        <div class="flex justify-center  lg:px-0  gap-2">
            <img src="../../../assets/icons/phone_down_circle_fill.svg" alt="" class="w-6">
            <span class="">
                {{this.userDetails?.phone_code}} {{this.userDetails?.phone}}
            </span>
        </div>
        <div class="flex justify-center  lg:px-0  gap-2">
            <img src="../../../assets/icons/Message.svg" alt="" class="w-6">
            <span class="">
                {{this.userDetails?.email}}
            </span>
        </div>
    </div>
    <div class="flex justify-center items-center gap-3 xl:gap-7">
        <button *ngIf="(!isArchitect && !isAgent) && !isArchitect" (click)="routeToRegister('AR')" type="button"
            class="w-3/4 md:w-2/5 lg:w-1/5  bg-accent hover:bg-accentDark rounded-sm text-primary text-sm py-2 ">
            {{"Become an Architect"| translate}}
        </button>
        <button *ngIf="(!isArchitect && !isAgent) && !isAgent" (click)="routeToRegister('AG')" type="button"
            class="w-3/4 md:w-2/5 lg:w-1/5  bg-accent hover:bg-accentDark rounded-sm text-primary text-sm py-2 ">{{"Become an Agent"| translate}}</button>
    </div>

</div>
<div *ngIf="editProfile === true">
    <form [formGroup]="editProfileForm">
        <!-- <div class="flex items-center gap-5 mb-5">
            <img src="../../../../assets/icons/arrow-left.svg" class="h-5 w-5">
            <span class="text-white">Back</span>
        </div> -->
        <h5 class="font-extrabold text-2xl text-white text-center mx-auto mb-9">{{"EDIT PROFILE"| translate}}</h5>
        <div class="grid grid-cols-2 gap-10 gap-y-5">
            <div class=" col-span-full flex  items-center gap-9 text-white ">
                <img [src]="profilePic" alt="" class=" w-36 aspect-square rounded-full ">
                <div>
                    <div class="flex flex-col md:flex-row gap-8">
                        <button (click)="selectImg()" class="bg-accent text-black text-sm py-3 font-bold px-8">
                            {{"CHOOSE FILE"| translate}}</button>
                        <input type="file" (change)="onFileChanged($event)" #imageSelect id='imageSelect' hidden>
                        <div class="text-xs tracking-wider text-steelBlue leading-none">
                            <p class="mb-3">{{"Acceptance formats Jpg, png only"| translate}}</p>
                            <p>{{"Max size is 500 kb and min size is 70 kb"| translate}}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"First Name"| translate}}</label>
                <input type="text" formControlName="first_name"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class="col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm mb-3">{{"Last Name"| translate}}</label>
                <input type="text" formControlName="last_name"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
            </div>
            <div class="col-span-2 md:col-span-1">
                <label for="" class="text-white text-sm ">{{"Email"| translate}}</label>
                <input type="text" formControlName="email"
                    class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3 text-gray-600 ">
            </div>
            <div class="col-span-2 md:col-span-1 pb-4">
                <label for="" class="text-white text-sm mb-3">{{"Mobile Number"| translate}}</label>
                <input type="text" formControlName="phone" minlength="8" maxlength="12"
                    class="bg-white w-full text-base border-0 h-10 mt-3  rounded-sm outline-none px-3">
            </div>

        </div>
        <div class="mt-6">
            <h2 class="text-white text-xl font-semibold block mb-3">{{"CHANGE PASSWORD"| translate}}</h2>
            <div class="grid grid-cols-2 gap-10 gap-y-5">
                <div class="col-span-2 md:col-span-1 row-span-1 md:row-span-2">
                    <label for="" class="text-white text-sm ">{{"Current Password"| translate}}</label>
                    <input formControlName="old_password" type="password"
                        class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
                </div>
                <div class="col-span-2 md:col-span-1">
                    <label for="" class="text-white text-sm ">{{"New Password"| translate}}</label>
                    <input formControlName="new_password" type="password"
                        class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
                </div>
                <div class="col-span-2 md:col-span-1">
                    <label for="" class="text-white text-sm mb-3">{{"Confirm New Password"| translate}}</label>
                    <input formControlName="confirm_password" type="password"
                        class="bg-white w-full text-base border-0 h-10 mt-3 rounded-sm outline-none px-3">
                </div>
            </div>
        </div>

        <div class="mt-5 lg:mt-10">
            <div class="grid grid-cols-2 gap-10 gap-y-5">
                <div class="col-span-2 md:col-span-1 rtl:mr-auto ltr:ml-auto text-right w-full">
                    <button (click)="discardChanges()" type="button"
                        class="border border-accent h-10 w-full text-white font-bold">{{"DISCARD CHANGES"|
                        translate}}</button>
                </div>
                <div class="col-span-2 md:col-span-1 rtl:ml-auto ltr:mr-auto w-full">
                    <button (click)="submitEditProfile()" type="button" class="h-10 bg-accent w-full font-bold">{{"SAVE
                        CHANGES"| translate}}</button>
                </div>
            </div>
        </div>

    </form>

</div>