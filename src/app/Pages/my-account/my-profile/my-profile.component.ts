import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { UserAuthenticationService } from 'src/app/Services/user-authentication.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-my-profile',
  templateUrl: './my-profile.component.html',
  styleUrls: ['./my-profile.component.scss']
})
export class MyProfileComponent implements OnInit {

  constructor(
    private fb: FormBuilder,
    private toastr: ToastrService,
    private configSettings: configSettings,
    private userAuthenticationService: UserAuthenticationService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private translateService: TranslateService,

  ) { }

  userDetails: any
  editProfile: boolean = false

  // file upload 
  fileName: any
  profilePic: string = ''
  selectedFile: any;


  @ViewChild('imageSelect') imageSelect: ElementRef;
  userTypes: any = []


  isOwner: any = false
  isArchitect: any = false
  isAgent: any = false


  ngOnInit(): void {
    this.getUserDetails()

    this.activatedRoute.fragment.subscribe((frag) => {
      if (frag) {
        this.openEditProfile()
      } else {
        this.editProfile = false
      }
    })
    console.log(this.userDetails);
    



    this.userDetails.user_types.forEach((el: any) => {
      this.userTypes.push(el.name)
    })

    // console.log(this.userTypes)


    this.isOwner = this.userTypes.includes("Owner")
    this.isArchitect = this.userTypes.includes("Architect")
    this.isAgent = this.userTypes.includes("Agent")

  }

  getUserDetails() {
    this.userDetails = this.configSettings.getUserDetails()
  }

  editProfileForm = this.fb.group({
    user_type_id: [''],
    first_name: [''],
    last_name: [''],
    email: [{ value: '', disabled: true }],
    phone: [''],
    password: [''],
    company_name: [''],
    country_id: [''],
    area_id: [''],
    state_id: [''],
    street: [''],
    flat: [''],
    building: [''],
    zip_code: [''],
    company_logo: [''],
    portfolio: [''],
    project_photo: [''],
    old_password: [''],
    new_password: [''],
    confirm_password: [''],
  })


  openEditProfile() {
    this.editProfile = true
    this.editProfileForm.patchValue({
      first_name: this.userDetails?.first_name,
      last_name: this.userDetails?.last_name,
      email: this.userDetails?.email,
      phone: this.userDetails?.phone,
    })
    this.profilePic = this.userDetails?.profile_img
  }

  submitEditProfile() {


    // if (!this.editProfileForm.invalid) {
    const getParams = {
      // lang: this.lang,
    }
    if(this.editProfileForm.controls['first_name'].value == ''){
      this.translateService.get('First Name Cannot Be Blank').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
    }
    if(this.editProfileForm.controls['last_name'].value == ''){
      this.translateService.get('Last Name Cannot Be Blank').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
      
    }
    if(this.editProfileForm.controls['phone'].value == ''){
      this.translateService.get('Mobile Number Cannot be Blank').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
     
    }
    if (this.editProfileForm.controls['old_password'].touched || this.editProfileForm.controls['new_password'].touched || this.editProfileForm.controls['confirm_password'].touched ) {
      if(this.editProfileForm.controls['old_password'].value == ''){
        this.translateService.get('Please Enter Current Password').subscribe(res =>{
          this.toastr.error('', res);
        })
        return
       
      }
      if(this.editProfileForm.controls['old_password'].value == this.editProfileForm.controls['new_password'].value){
        this.translateService.get('New password cannot be same as Old password').subscribe(res =>{
          this.toastr.error('', res);
        })
        return
        
      }
      if (this.editProfileForm.controls['old_password'].value != '' && this.editProfileForm.controls['new_password'].value === '') {
        this.translateService.get('New password field cannot be empty').subscribe(res =>{
          this.toastr.error('', res);
        })
        return
        
      }
  
      if (this.editProfileForm.controls['old_password'].value != '' && this.editProfileForm.controls['confirm_password'].value === '') {
        this.translateService.get('Confirm password field cannot be empty').subscribe(res =>{
          this.toastr.error('', res);
        })
        return
        
      }
     
     if(this.editProfileForm.controls['new_password'].value != this.editProfileForm.controls['confirm_password'].value){
      this.translateService.get('Confirm password does not match new password').subscribe(res =>{
        this.toastr.error('', res);
      })
      return
        
      }
      
    }

 



    const postParams = {
      user_id: this.userDetails?.user_id,
      first_name: this.editProfileForm.controls['first_name'].value,
      last_name: this.editProfileForm.controls['last_name'].value,
      email: this.editProfileForm.controls['email'].value,
      phone: this.editProfileForm.controls['phone'].value,
      old_password: this.editProfileForm.controls['old_password'].value,
      new_password: this.editProfileForm.controls['new_password'].value,
      profile_img: this.editedProfilePic

    }



    this.userAuthenticationService.postEditProfile(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.toastr.success(response?.body?.message)
            localStorage.removeItem('userDetails')
            this.configSettings.setLocalStorage('userDetails', response?.body?.data)
            this.getUserDetails()
            this.editProfileForm.patchValue({
              old_password: null,
              new_password: null,
              confirm_password: null,
            })
            this.editProfile = false
          }
          if (response.body.status == 201) {
            this.toastr.error(response?.body?.message)
            }
        }
      },
      error: (err) => {
        this.toastr.error(err.error.message)
      }
    })

    // }
  }
  goToTop() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }



  discardChanges() {
    if (!this.editProfileForm.touched) {
      this.editProfileForm 
      this.editProfile = false  
      this.goToTop() 
     }
    else{
      this.openEditProfile()
      this.editProfileForm.controls.old_password.reset()
      this.editProfileForm.controls.new_password.reset()
      this.editProfileForm.controls.confirm_password.reset()
    }
     this.goToTop()
}

  selectImg() {
    this.imageSelect.nativeElement.click();
  }

  ImageBaseData: any;
  url: any = ''
  editedProfilePic: any
  onFileChanged(event: any) {
    let file = event.target.files[0];
    // 70000 
    // 500000
    this.fileName = ''
    this.fileName = file.name;
    if (event.target.files && event.target.files[0]) {
      if (event.target.files[0].type === 'image/jpeg' ||
        event.target.files[0].type === 'image/png' ||
        event.target.files[0].type === 'image/jpg') {

        if (event.target.files[0].size > 0 && event.target.files[0].size < 500000) {
          var reader = new FileReader();

          reader.readAsDataURL(event.target.files[0]); // read file as data url

          reader.onload = (event) => { // called once readAsDataURL is completed
            this.url = event.target?.result;
            this.profilePic = this.url
            this.editedProfilePic = this.url
          }
        } else {
          this.toastr.warning('Max size is 500 kb and min size is 70 kb')
        }
      } else {
        this.toastr.warning('Acceptance formats Jpg, png only')
      }
    }

  }

  routeToRegister(num: any) {
    this.router.navigate(['/registration/' + num])
  }

}
