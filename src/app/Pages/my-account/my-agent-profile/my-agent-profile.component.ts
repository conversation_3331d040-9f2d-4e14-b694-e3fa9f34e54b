import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { UserAuthenticationService } from 'src/app/Services/user-authentication.service';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { AddressService } from 'src/app/Services/Address/address.service';
import { ExpertiseService } from 'src/app/Services/Expertise/expertise.service';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-my-agent-profile',
  templateUrl: './my-agent-profile.component.html',
  styleUrls: ['./my-agent-profile.component.scss']
})



export class MyAgentProfileComponent implements OnInit {
  constructor(
    private fb: FormBuilder,
    private toastr: ToastrService,
    private userAuthenticationService: UserAuthenticationService,
    private configSettings: configSettings,
    private addressService: AddressService,
    private expertiseService: ExpertiseService,
    private translateService: TranslateService,
  ) {
    this.configSettings.setShowLoader(true);
  }

  userDetails: any;
  lang?= this.configSettings.getLang();
  emailPattern: any = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,4}$/;

  numberRegex = /^[0-9][6,12]+$/
  
  getExpertiseLang=true;
  @ViewChild('stateEle') stateEleRef: any;
  @ViewChild('areaEle') areaEleRef: any;

  ngOnInit(): void {
    this.getExpertise();
    this.getCountry();
    this.getExpertiseLangfun()
     this.userDetails = this.configSettings.getUserDetails();

    this.userDetails.expertise.forEach((element: any) => {
      this.listOfUserExpertise.push(element.id)
    });

    this.architectProfileForm.patchValue({
      agency_name: this.userDetails?.agent_name_en,
      country_id: this.userDetails?.country_id,




      email: this.userDetails?.contact_email,
      phoneNumber: this.userDetails?.contact_number,
      wtspNumber: this.userDetails?.whatsapp,


      street: this.userDetails?.street,
      flat: this.userDetails?.flat,
      building: this.userDetails?.building,
      zip_code: this.userDetails?.zipcode,
      expertise: this.listOfUserExpertise,
      // state_id: this.userDetails?.state_id,
      aboutUsEn: this.userDetails?.about_en,
      aboutUsAr: this.userDetails?.about_ar,


    });
    this.companyLogo = this.userDetails?.company_logo

  }

  fileName: any;
  url: any;
  type: any;

  companyLogoName: any;
  companyLogo: any;

  portfolioName: any;
  portfolio: any;

  projectName: any;
  project: any;

  countries: any = [];
  states: any = [];
  areas: any = [];

  oninit = true;

  allExpertise: any;
  listOfUserExpertise: any = [];
  @ViewChild('imageSelect') imageSelect: ElementRef;
  getExpertiseLangfun(){
    if (this.lang =="ar") {
      this.getExpertiseLang=false;
      }
     }


  getExpertise() {
    const getParams = {};
    this.expertiseService.getExpertise(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.allExpertise = response.body.data;
            }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  addExpertise(e: any) {
    this.listOfUserExpertise.push(e);
  }

  removeExpertise(e: any) {
    this.listOfUserExpertise.splice(
      this.listOfUserExpertise.indexOf(e.value),
      1
    );
  }

  getAllCountries() {
    this.oninit = false;
    // this.stateEleRef.nativeElement.value = ''
    // this.areaEleRef.nativeElement.value = ''
  }

  getCountry() {
    const getParams = {};
    this.addressService.getCountry(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.countries = response?.body?.data;
            this.getAllStates();
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  getAllStates() {
    this.getState(this.architectProfileForm.controls['country_id'].value);
  }

  getState(value: any) {
    const getParams = {
      country_id: value,
    };
    this.addressService.getState(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.states = response?.body?.data;

            if (this.oninit == false) {
              this.architectProfileForm.get('state_id')?.reset();
              this.architectProfileForm.get('area_id')?.reset();
            } else {
              this.architectProfileForm.patchValue({
                state_id: this.userDetails?.state_id,
              });
            }

            this.getAllAreas();
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  getAllAreas() {
    this.getAreas(this.architectProfileForm.controls['state_id'].value);
  }

  getAreas(value: any) {
    const getParams = {
      state_id: value,
    };
    this.addressService.getAreas(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.configSettings.setShowLoader(false);
            this.areas = response?.body?.data;

            if (this.oninit == false) {
              this.architectProfileForm.get('area_id')?.reset();
            } else {
              this.architectProfileForm.patchValue({
                area_id: this.userDetails?.area_id,
              });
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  selectImg() {
    this.imageSelect.nativeElement.click();
  }


  onFileChanged(event: any, str: any) {
    if (event.target.files && event.target.files[0]) {

      if (event.target.files[0].type === 'image/jpeg' ||
        event.target.files[0].type === 'image/png' ||
        event.target.files[0].type === 'image/jpg') {
        const filesAmount = event.target.files.length;
        for (let i = 0; i < filesAmount; i++) {
          const reader = new FileReader();

          reader.onload = (event: any) => {
            if (str == 'cl') {
              this.architectProfileForm.patchValue({
                company_logo: event.target.result,
              });
              this.companyLogo = event.target.result
            } else if (str == 'p') {
              this.architectProfileForm.patchValue({
                portfolio: event.target.result,
              });
            } else if (str == 'cp') {
              this.architectProfileForm.patchValue({
                project_photo: event.target.result,
              });
            }
          };

          reader.readAsDataURL(event.target.files[i]);
        }
      }
    }
  }

  architectProfileForm = this.fb.group({
    agency_name: [''], // , Validators.required
    email: [''],
    phoneNumber: [''],
    wtspNumber: [''],
    country_id: [''],
    area_id: [''],
    state_id: ['', Validators.required],
    street: [''],
    flat: [''],
    building: [''],
    zip_code: [''],
    company_logo: [''],
    portfolio: [''],
    project_photo: [''],
    // test: [[1, 2, 3, 4]]
    expertise: [''],
    aboutUsEn: [''],
    aboutUsAr: [''],
  });

  goToTop() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }

  editProfile() {
    if (
      this.architectProfileForm.controls['agency_name'].value === null ||
      this.architectProfileForm.controls['agency_name'].value === ''
    ) {
      // this.toastr.error('', 'State is required');
      this.translateService.get('Agency name is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (
      this.architectProfileForm.controls['email'].value === null ||
      this.architectProfileForm.controls['email'].value === ''
    ) {
      // this.toastr.error('', 'State is required');
      this.translateService.get('Email is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (!this.architectProfileForm.controls.email.valid) {
      this.translateService.get('Enter a valid e-mail').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (
      this.architectProfileForm.controls['phoneNumber'].value === null ||
      this.architectProfileForm.controls['phoneNumber'].value === ''
    ) {
      // this.toastr.error('', 'State is required');
      this.translateService.get('Phone number is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (!this.architectProfileForm.controls['phoneNumber'].valid) {
      this.translateService.get('Enter a valid phone number').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (!this.architectProfileForm.controls['wtspNumber'].valid) {
      this.translateService.get('Enter a valid whatsapp number').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (
      this.architectProfileForm.controls['country_id'].value === null ||
      this.architectProfileForm.controls['country_id'].value === ''
    ) {
      // this.toastr.error('', 'State is required');
      this.translateService.get('Country is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (
      this.architectProfileForm.controls['state_id'].value === null ||
      this.architectProfileForm.controls['state_id'].value === ''
    ) {
      // this.toastr.error('', 'State is required');
      this.translateService.get('State is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }

    if (
      this.areas.length > 0 &&
      (this.architectProfileForm.controls['area_id'].value === null ||
        this.architectProfileForm.controls['area_id'].value === '')
    ) {
      // this.toastr.error('', 'Area is required');
      this.translateService.get('Area is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }

    if (
      (this.architectProfileForm.controls['agency_name'].value === null ||
        this.architectProfileForm.controls['agency_name'].value === '')
    ) {
      // this.toastr.error('', 'Agency name is required');
      this.translateService.get('Agency name is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      
      return;
    }
    if (
      (this.architectProfileForm.controls['street'].value === null ||
        this.architectProfileForm.controls['street'].value === '')
    ) {
      this.translateService.get('Street name is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      
      return;
    }
    if (
      (this.architectProfileForm.controls['flat'].value === null ||
        this.architectProfileForm.controls['flat'].value === '')
    ) {
      this.translateService.get('Flat number is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      
      return;
    }
    if (
      (this.architectProfileForm.controls['building'].value === null ||
        this.architectProfileForm.controls['building'].value === '')
    ) {
      this.translateService.get('Building name is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      
      return;
    }
    if (
      (this.architectProfileForm.controls['zip_code'].value === null ||
        this.architectProfileForm.controls['zip_code'].value === '')
    ) {
      this.translateService.get('Zipcode is required').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (!this.architectProfileForm.controls['zip_code'].valid) {
      this.translateService.get('Enter a valid zipcode').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }



    this.configSettings.setShowLoader(true);

    const postParams = {
      user_id: this.userDetails.user_id,
      agent_name_en: this.architectProfileForm.controls['agency_name'].value,
      agent_name_ar: this.architectProfileForm.controls['agency_name'].value,

      // architect_name_en:
      //   this.architectProfileForm.controls['architect_name'].value,
      // architect_name_ar:
      //   this.architectProfileForm.controls['architect_name'].value,

      expertise: this.listOfUserExpertise.toString(),

      country_id: this.architectProfileForm.controls['country_id'].value,
      state_id: this.architectProfileForm.controls['state_id'].value,
      area_id: this.architectProfileForm.controls['area_id'].value,
      street: this.architectProfileForm.controls['street'].value,
      flat: this.architectProfileForm.controls['flat'].value,
      building: this.architectProfileForm.controls['building'].value,
      zipcode: this.architectProfileForm.controls['zip_code'].value,
      company_logo: this.architectProfileForm.controls['company_logo'].value,
      portfolio: this.architectProfileForm.controls['portfolio'].value,
      project_photo: this.architectProfileForm.controls['project_photo'].value,


      contact_number: this.architectProfileForm.controls['phoneNumber'].value,
      whatsapp: this.architectProfileForm.controls['wtspNumber'].value,
      contact_email: this.architectProfileForm.controls['email'].value,

      desc_en: this.architectProfileForm.controls['aboutUsEn'].value,
      desc_ar: this.architectProfileForm.controls['aboutUsAr'].value,
    };


    this.userAuthenticationService.postEditProfile({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.goToTop()
            this.translateService.get('Agent details successfully updated').subscribe(res =>{
              this.toastr.success('', res);
            })
            
            // this.toastr.success(response.body.message);
            this.configSettings.setUserDetails(response.body.data);
            this.configSettings.setShowLoader(false);
          }
        }
      },
      error: (err) => {
        this.toastr.error(err.error.message);
      },
    });
  }
}
