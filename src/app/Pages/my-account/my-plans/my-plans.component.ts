import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { UserHistoryPlansService } from 'src/app/Services/User-history-plans/user-history-plans.service';

@Component({
  selector: 'app-my-plans',
  templateUrl: './my-plans.component.html',
  styleUrls: ['./my-plans.component.scss']
})
export class MyPlansComponent implements OnInit {

  constructor(
    private toastr: ToastrService,
    private configSettings: configSettings,
    private userHistoryPlansService: UserHistoryPlansService
  ) { }

  // Variables
  allPlans: any

  userPlans: any = []
  architectPlans: any = []
  agentPlans: any = []

  isUser: boolean = false
  isAgent: boolean = false
  isArchitect: boolean = false
  userDetails: any
  confirmationModal:boolean=false

  userID: any = this.configSettings.getUserID()

  ngOnInit(): void {
    this.configSettings.setShowLoader(true)

    this.userDetails = this.configSettings.getUserDetails()
    if (this.userDetails) {
      this.userDetails?.user_types.forEach((el: any) => {
        if(el.id === 1){
          this.isUser = true
        }
        else if(el.id === 3){
          this.isAgent = true
        }
        else if(el.id === 4){
          this.isArchitect = true
        }
      })
    }

    this.myPlans()
  }
 currentPlan : any

  onCancel(plan : any){
    this.confirmationModal=true;
    document.body.style.overflow = "hidden";
    this.currentPlan = plan
  }

  closePopup(){
    this.confirmationModal=false;
    document.body.style.overflow = "auto";
  }


  myPlans() {

    const getParams = {

      user_id: this.configSettings.getUserID(),
      per_page: 9999
    }

    this.userHistoryPlansService.plansHistory(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allPlans = response.body.data.items
            this.allPlans.forEach((el: any) => {
              if (el.user_type == 1) {
                this.userPlans.push(el)
              } else if (el.user_type == 3) {
                this.agentPlans.push(el)
              } else if (el.user_type == 4) {
                this.architectPlans.push(el)
              }
            });
            (this.userPlans, this.agentPlans, this.architectPlans)

            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })




  }


  cancelPlan(plan: any) {
    let planID = plan?.id
    console.log(plan);
    
    this.configSettings.setShowLoader(true)
    

    const postParams = {
      user_id: this.userID,
      user_subscription_id: planID
    }

    this.userHistoryPlansService.cancelPlan({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.configSettings.setShowLoader(false)
            this.confirmationModal=false;
            document.body.style.overflow = "auto";

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
        this.configSettings.setShowLoader(false)

      }
    })
  }
}
