<div *ngIf="allPlans" class="min-h-vh40">
  <!-- <div class="relative h-0 block text-right">


  </div> -->
  <h2 class="text-white text-center text-2xl font-bold mb-10">{{"MY PLANS" | translate}}</h2>

  <div *ngIf="isUser && !isArchitect" class="flex justify-between  mb-4" >
    <h1 class="rtl:text-xl rtl:sm:text-3xl text-3xl text-white font-bold">{{"User plans" | translate}}</h1>
    <button [routerLink]="['/choose-plan/UR']" type="button"
      class="self-center py-2 px-2 border border-accent text-white text-sm font-bold text-center rounded-md">
      {{"Buy New Plan" | translate}}</button>
  </div>
  <div *ngIf="userPlans?.length > 0" class="grid grid-cols-12 gap-5 gap-y-8 justify-center mb-10">
    <div *ngFor="let plan of userPlans" class="col-span-12 md:col-span-6 xl:col-span-4 ">
      <div class="rounded-lg p-4 bg-auto bg-no-repeat bg-center relative overflow-hidden bg"
        [ngStyle]="{'background-color': plan?.hex_code}">
        <!-- bg-cover bg-no-repeat  bg-center use this on real image time [ngStyle]="{'background-color': plan?.color_code}"-->
        <h6 class="text-white font-bold text-xl xl:text-lg 2xl:text-xl pb-4 text-center">
          {{plan.name |uppercase }}
        </h6>
        <div class="mb-5">
          <h6 class="text-white text-2xl font-bold">
            {{plan.price | number:'1.0' }} {{"KD" | translate}} /<span class="font-semibold text-lg">{{plan.billing_type_caption}}</span>
          </h6>
          <p class="text-white font-medium text-sm pb-2">{{"Billed" | translate}} {{plan.billing_type_caption |
            lowercase}}</p>
        </div>
        <div>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Max number posts of this plan" | translate}}</li>
            <li>{{plan.max_posts}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining number of posts" | translate}}</li>
            <li>{{plan.remaining_posts}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Max number of stories" | translate}}</li>
            <li>{{plan.max_stories}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining number of posts" | translate}}</li>
            <li>{{plan.remaining_stories}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Validity" | translate}}</li>
            <li>{{plan.duration}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining Days" | translate}}</li>
            <li>{{plan.remaining_days}} {{'Days'| translate}}</li>
          </ul>
          <!-- <ul *ngIf="plan.high_position != null" class="flex justify-between text-sm text-white pb-3">
            <li>{{"High position"| translate}}</li>
            <li>{{plan.high_position}} Day(s)</li>
          </ul> -->
          <ul class="flex justify-between text-sm text-white pb-3">
            <li *ngIf="plan.user_type==3 || plan.user_type==1">{{"Max.Photos Per Posts" | translate}}</li>
            <li *ngIf="plan.user_type==4">{{"Max.Photos Per Project"| translate}}</li>
            <li>{{plan.max_photos}} {{"Photos"| translate}}</li>
          </ul>
        </div>
        <div *ngIf="plan.is_expired == 1"
          class="absolute top-7 -right-10 font-bold bg-red-600 rotate-45 text-white px-16 py-2">{{"Expired"| translate}}
        </div>
      </div>

      <div class="flex gap-8 justify-between mt-3">
        <div class="w-full ">
          <button [routerLink]="['/choose-plan/UR']"
            *ngIf="plan.is_canceled == 1 || (plan.is_canceled == 0 && plan.is_expired == 1)" type="button"
            class="w-full h-10 text-sm font-bold bg-accent text-white rounded-md ">
            {{"Renew Plan" | translate}}</button>
        </div>
        <!-- <div class="w-full "> -->
        <!-- <button *ngIf="plan.is_canceled == 0" (click)="cancelPlan(plan.id)" type="button"
            class="w-full h-10 text-sm font-bold text-white border border-accent rounded-md">
            {{"Cancel Plan" | translate}}
          </button> -->
        <!-- </div> -->
        <div class="w-full ">
          <button *ngIf="plan.is_canceled == 0" type="button" (click)="onCancel(plan)"
            class="w-full h-10 text-sm font-bold text-white border border-accent rounded-md">
            {{"Cancel Plan"| translate}}
          </button>
        </div>
        <!-- <div *ngIf="confirmationModal"
          class="z-[100]    mx-auto fixed top-1/2 left-1/3 sm:left-1/2 sm:-translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white  rounded-xl text-sm text-white">
          <h1 class="text-center px-10 text-black">{{"Are you sure" | translate}}</h1>
          <h1 class="mb-7 text-center px-10 text-black">{{"you want to cancel your plan ?" | translate}}</h1>
          <div class="flex justify-center gap-6">
            <button (click)="cancelPlan(plan.id)" class="bg-primary w-1/3 border border-primar px-6 py-2">
              {{'Yes' | translate}}
            </button>
            <button (click)="confirmationModal = false"
              class="bg-white w-1/3 border text-primary border-primary px-6 py-2">
              {{'No' | translate}}
            </button>
          </div>
        </div> -->
      </div>

    </div>
  </div>


  <div *ngIf="isAgent" class="flex justify-between  mb-4">
    <h1 class="rtl:text-xl rtl:sm:text-3xl text-3xl text-white font-bold">{{"Agent plans" | translate}}</h1>
    <button [routerLink]="['/choose-plan/AG']" type="button"
      class="self-center py-2 px-2 border border-accent text-white text-sm font-bold text-center rounded-md">
      {{"Buy New Plan" | translate}}</button>
  </div>
  <div *ngIf="agentPlans?.length > 0" class="grid grid-cols-12 gap-5 gap-y-8 justify-center mb-10">
    <div *ngFor="let plan of agentPlans" class="col-span-12 md:col-span-6 xl:col-span-4 ">
      <div class="rounded-lg p-4 bg-auto bg-no-repeat bg-center relative overflow-hidden bg"
        [ngStyle]="{'background-color': plan?.hex_code}">
        <!-- bg-cover bg-no-repeat  bg-center use this on real image time [ngStyle]="{'background-color': plan?.color_code}"-->
        <h6 class="text-white font-bold text-xl xl:text-lg 2xl:text-xl pb-4 text-center">
          {{plan.name |uppercase }}
        </h6>
        <div class="mb-5">
          <h6 class="text-white text-2xl font-bold">
            {{plan.price | number:'1.0'}} {{"KD" | translate}}/<span
              class="font-semibold text-lg">{{plan.billing_type_caption}}</span>
          </h6>
          <p class="text-white font-medium text-sm pb-2">{{"Billed" | translate}} {{plan.billing_type_caption |
            lowercase}}</p>
        </div>
        <div>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Maximum posts of this plan" | translate}}</li>
            <li>{{plan.max_posts}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining number of posts" | translate}}</li>
            <li>{{plan.remaining_posts}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Max number of stories" | translate}}</li>
            <li>{{plan.max_stories}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining number of stories" | translate}}</li>
            <li>{{plan.remaining_stories}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Validity" | translate}}</li>
            <li>{{plan.duration}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining number of days" | translate}}</li>
            <li>{{plan.remaining_days}} {{'Days' | translate}}</li>
          </ul>
          <!-- <ul *ngIf="plan.high_position != null" class="flex justify-between text-sm text-white pb-3">
            <li>{{"High position"| translate}}</li>
            <li>{{plan.high_position}} Day(s)</li>
          </ul> -->
          <ul class="flex justify-between text-sm text-white pb-3">
            <li *ngIf="plan.user_type==3 || plan.user_type==1">{{"Max.Photos Per Posts" | translate}}</li>
            <li *ngIf="plan.user_type==4">{{"Max.Photos Per Project"| translate}}</li>
            <li>{{plan.max_photos}} {{"Photos"| translate}}</li>
          </ul>
        </div>
        <div *ngIf="plan.is_expired == 1"
          class="absolute top-7 -right-10 font-bold bg-red-600 rotate-45 text-white px-16 py-2">Expired</div>
      </div>

      <div class="flex gap-8 justify-between mt-3">
        <div class="w-full ">
          <button [routerLink]="['/choose-plan/AG']"
            *ngIf="plan.is_canceled == 1 || (plan.is_canceled == 0 && plan.is_expired == 1)" type="button"
            class="w-full h-10 text-sm font-bold bg-accent text-white rounded-md ">
            {{"Renew Plan" | translate}}</button>
        </div>
        <div class="w-full ">
          <button *ngIf="plan.is_canceled == 0" type="button" (click)="onCancel(plan)"
            class="w-full h-10 text-sm font-bold text-white border border-accent rounded-md">
            {{"Cancel Plan"| translate}}
          </button>
        </div>
        <!-- <div *ngIf="confirmationModal"
          class="z-[100]  mx-auto fixed top-1/2 sm:left-1/2 sm:-translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white  rounded-xl text-sm text-white">
          <h1 class="mb-7 text-black">{{'Are you sure you want to cancel your plan ?'}}</h1>
          <div class="flex justify-around">
            <button (click)="cancelPlan(plan.id)" class="bg-green-500 px-6 py-2">
              {{'Yes' | translate}}
            </button>
            <button (click)="confirmationModal = false" class="bg-red-500 px-6 py-2">
              {{'No' | translate}}
            </button>
          </div>
        </div> -->
      </div>

    </div>
  </div>

  <div *ngIf="isArchitect" class="flex justify-between   mb-4">
    <h1 class="rtl:text-xl rtl:sm:text-3xl text-3xl text-white font-bold">{{"Architect plans" | translate}}</h1>
    <button [routerLink]="['/choose-plan/AR']" type="button"
      class="self-center py-2 px-2 border border-accent text-white text-sm font-bold text-center rounded-md">
      {{"Buy New Plan" | translate}}</button>
  </div>
  <div *ngIf="architectPlans?.length > 0" class="grid grid-cols-12 gap-5 gap-y-8 justify-center mb-10">
    <div *ngFor="let plan of architectPlans" class="col-span-12 md:col-span-6 xl:col-span-4 ">
      <div class="rounded-lg p-4 bg-auto bg-no-repeat bg-center relative overflow-hidden bg"
        [ngStyle]="{'background-color': plan?.hex_code}">
        <!-- bg-cover bg-no-repeat  bg-center use this on real image time [ngStyle]="{'background-color': plan?.color_code}"-->
        <h6 class="text-white font-bold text-xl xl:text-lg 2xl:text-xl pb-4 text-center">
          {{plan.name |uppercase }}
        </h6>
        <div class="mb-5">
          <h6 class="text-white text-2xl font-bold">
            {{plan.price | number:'1.0'}} {{"KD" | translate}}/<span
              class="font-semibold text-lg">{{plan.billing_type_caption}}</span>
          </h6>
          <p class="text-white font-medium text-sm pb-2">{{"Billed"| translate}} {{plan.billing_type_caption |
            lowercase}}</p>
        </div>
        <div>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Number of Projects" | translate}}</li>
            <li>{{plan.max_projects}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining number of projects" | translate}}</li>
            <li>{{plan.remaining_projects}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Max number of stories" | translate}}</li>
            <li>{{plan.max_stories}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Validity" | translate}}</li>
            <li>{{plan.duration}}</li>
          </ul>
          <ul class="flex justify-between text-sm text-white pb-3">
            <li>{{"Remaining Days" | translate}}</li>
            <li>{{plan.remaining_days}} {{"Days"| translate}}</li>
          </ul>
          <!-- <ul *ngIf="plan.high_position != null" class="flex justify-between text-sm text-white pb-3">
            <li>{{"High position"| translate}}</li>
            <li>{{plan.high_position}} Day(s)</li>
          </ul> -->
          <ul class="flex justify-between text-sm text-white pb-3">
            <li *ngIf="plan.user_type==3 || plan.user_type==1">{{"Max.Photos Per Posts" | translate}}</li>
            <li *ngIf="plan.user_type==4">{{"Max.Photos Per Project"| translate}}</li>
            <li>{{plan.max_photos}} {{"Photos"| translate}}</li>
          </ul>
        </div>
        <div *ngIf="plan.is_expired == 1"
          class="absolute top-7 -right-10 font-bold bg-red-600 rotate-45 text-white px-16 py-2">Expired</div>
      </div>

      <div class="flex gap-8 justify-between mt-3">
        <div class="w-full ">
          <button [routerLink]="['/choose-plan/AR']"
            *ngIf="plan.is_canceled == 1 || (plan.is_canceled == 0 && plan.is_expired == 1)" type="button"
            class="w-full h-10 text-sm font-bold bg-accent text-white rounded-md ">
            {{"Renew Plan" | translate}}</button>
        </div>
        <div class="w-full ">
          <button *ngIf="plan.is_canceled == 0" type="button" (click)="onCancel(plan)"
            class="w-full h-10 text-sm font-bold text-white border border-accent rounded-md">
            {{"Cancel Plan"| translate}}
          </button>
        </div>
        <!-- <div *ngIf="confirmationModal"
          class="z-[100]  mx-auto w-fit fixed top-1/2  left-1/2 -translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white  rounded-xl text-sm text-white">
          <h1 class="mb-7 text-black">{{'Are you sure you want to cancel your plan ?'}}</h1>
          <div class="flex justify-center gap-12">
            <button (click)="cancelPlan(plan.id)" class="bg-primary w-1/3 border border-primar px-6 py-2">
              {{'Yes' | translate}}
            </button>
            <button (click)="confirmationModal = false" class="bg-white w-1/3 border text-primary border-primary px-6 py-2">
              {{'No' | translate}}
            </button>
          </div>
        </div> -->
      </div>

    </div>
  </div>
  

  <div (click)="closePopup()" *ngIf="confirmationModal" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="confirmationModal" class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 
-translate-y-1/2 p-10 bg-white w-[22rem] sm:w-[26rem] rounded-xl">

<h1 class="text-center  text-black">{{"Are you sure" | translate}}</h1>
<h1 class="mb-7 text-center  text-black">{{"you want to cancel your plan ?" | translate}}</h1>
    <div class="flex justify-center gap-2">
        <button (click)="cancelPlan(currentPlan)" class="block text-white bg-primary py-3 px-8">{{"Yes"| translate}}</button>
        <button (click)="closePopup()" class="block text-white bg-red-500 py-3 px-8">{{"No"| translate}}</button>
    </div>
</div>
  <!-- <div *ngIf="allPlans?.length == 0" class="h-vh40 flex justify-center items-center text-xl text-white">
    {{"No Plans available"| translate}}
  </div> -->
</div>