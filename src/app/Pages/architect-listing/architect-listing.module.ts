import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ArchitectListingRoutingModule } from './architect-listing-routing.module';
import {NgxPaginationModule} from 'ngx-pagination';
import { ArchitectListingComponent } from './architect-listing.component';
import { FeaturedArchitectsCardComponent } from 'src/app/Components/featured-architects-card/featured-architects-card.component';
import { FeaturedArchitectsCardModule } from 'src/app/Components/featured-architects-card/featured-architects-card.module';
import { SimilarArchitectsModule } from 'src/app/Components/similar-architects/similar-architects.module';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [ArchitectListingComponent],
  imports: [
    CommonModule,
    FormsModule,
    ArchitectListingRoutingModule,
    NgxPaginationModule,
    SimilarArchitectsModule,
    FeaturedArchitectsCardModule,
    TranslateModule
  ], exports : [ArchitectListingComponent]
})
export class ArchitectListingModule { }
