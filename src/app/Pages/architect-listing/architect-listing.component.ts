import { Component, OnInit } from '@angular/core';
import { ExpertiseHelper } from 'src/app/globalFunctions/expertiesHelper';
import { ActivatedRoute, Router } from '@angular/router';
import { ArchitectListingService } from 'src/app/Services/Architect-listing/architect-listing.service';
import { configSettings } from 'src/app/Config/config.settings';
import { ToastrService } from 'ngx-toastr';
import { ExpertiseService } from 'src/app/Services/Expertise/expertise.service';

@Component({
  selector: 'app-architect-listing',
  templateUrl: './architect-listing.component.html',
  styleUrls: ['./architect-listing.component.scss']
})
export class ArchitectListingComponent implements OnInit {

  constructor(
    private configSettings: configSettings,
    public expertiseHelper: ExpertiseHelper,
    private activatedRoute: ActivatedRoute,
    private toastr: ToastrService,
    private architectListingService: ArchitectListingService,
    private router: Router,
    private expertiseService: ExpertiseService
  ) {

    this.lang = this.configSettings.getLang()



  }

  list: any
  location: any = null
  propertyType: any
  expertise: any
  sort: any = 'ASC'
  listingType: string = ''

  lang: string = ''
  p: number = 1;
  numberOfPages: any
  totalItems: any
  itemsPerPage: any = 0
  fromArticle: any
  toArticle: any
  pageNumber: any = 1

  architectArr: Array<any> = []

  ngOnInit(): void {
    this.activatedRoute.queryParams.subscribe(params => {
      this.p = Number(params.page)


      if (!this.p) {
        this.configSettings.getLastCurrentPage().subscribe(res => {
          this.p = res
        })
      }

      this.architectListing()
    })
    this.getExpertise()

  }

  architectListing() {
    this.configSettings.setShowLoader(true)
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'auto'
    });

    let getParams = {
      per_page: 6 * this.p
    }

    let postParams = {
      location: (this.location != null || this.location != undefined) ? this.location : '',
      property_type: (this.propertyType != null || this.propertyType != undefined) ? this.propertyType : '',
      expertise: (this.expertise != null || this.expertise != undefined) ? this.expertise : '',
      sort_by: this.sort
    }

    this.architectListingService.architectListing(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {

            this.architectArr = response?.body?.data?.architects
            this.numberOfPages = response.body.data.totalCount
            this.totalItems = response.body.data.totalItemCount
            // this.itemsPerPage = Number(response.body.data.itemPerPage)
            this.fromArticle = response.body.data.offset + 1
            this.toArticle = response.body.data.offset + this.itemsPerPage
            this.configSettings.setShowLoader(false)
          }

        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message)
      }

    })

  }

  search() {
    // if (this.listingType == 'A') {
    this.architectListing()
    // } else {
    // this.featuredArchitectListing()
    // }
  }

  // featuredArchitectListing() {
  //   let getParams = {

  //   }

  //   let postParams = {
  //     "location": this.location,
  //     "property_type": this.propertyType,
  //     "expertise": this.expertise,
  //     "sort_by": this.sort
  //   }

  //   this.featuredArchitectListingMainAPi(getParams, postParams)

  // }

  changePage(pageNumber: any) {

    this.router.navigate(['.'], { relativeTo: this.activatedRoute, queryParams: { page: this.p } });

    this.configSettings.setLastCurrentPage(Number(pageNumber))

    this.pageNumber = pageNumber

    let getParams = {
      page: pageNumber

    }

    let postParams = {
      "location": this.location,
      "property_type": this.propertyType,
      "expertise": this.expertise,
      "sort_by": this.sort
    }

    this.architectListing()

  }

  sortArchitect() {

    this.sort === 'ASC' ? this.sort = 'DESC' : this.sort = 'ASC'

    this.p = 1

    let getParams = {
      page: 1

    }

    let postParams = {
      "location": this.location,
      "property_type": this.propertyType,
      "expertise": this.expertise,
      "sort_by": this.sort
    }

    this.architectListing()
  }

  expertises: any

  getExpertise() {
    this.expertiseService.getExpertise({}).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {

            this.expertises = response.body.data
          }

        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message)
      }

    })
  }
}
