<div class="w-full  py-3 border border-borderColor border-r-0 border-l-0">
    <div class="container mx-auto px-4 sm:px-14 md:px-24 xl:px-32 2xl:px-48">
        <div class="grid grid-cols-12 gap-4">
            <div class="col-span-6 lg:col-span-4">
                <p class="text-base text-white mb-2">{{"Location"| translate}}</p>
                <input type="text"
                    class="py-2 placeholder:text-slate-600 placeholder:md:text-xs placeholder:lg:text-xs placeholder:xl:text-base border w-full focus:outline-none h-10 rounded-md md:text-xs lg:text-xs xl:text-base bg-white text-slate-500 px-2"
                    placeholder="{{'Search Location'| translate}} ">
            </div>
            <!-- <div class="col-span-6 lg:col-span-3 relative">
                <p class="text-base text-white mb-2">{{"Property Type"| translate}}</p>
                <select [(ngModel)]="propertyType"
                    class="border-0 w-full focus:outline-none h-10 md:text-xs lg:text-xs xl:text-base rounded-md bg-white text-slate-500 px-2 appearance-none">
                    <option value="undefined" disabled class="bg-inherit text-black"> {{"Please Select Property Type"|
                        translate}} </option>
                    <option value="R" class="bg-inherit text-black"> {{"Rent"| translate}} </option>
                    <option value="S" class="bg-inherit text-black"> {{"Sale"| translate}} </option>

                </select>
                <img src="../../../assets/icons/arrow-semi-down.svg"
                    class="hidden sm:block w-2 absolute rtl:left-3 rtl:right-[unset]  right-3 top-[70%]  z-10  ">
            </div> -->
            <div class="col-span-6 lg:col-span-3 relative">
                <p class="text-base text-white mb-2">{{"Expertise"| translate}}</p>
                <select [(ngModel)]="expertise"
                    class="border-0 w-full focus:outline-none h-10 md:text-xs lg:text-xs xl:text-base rounded-md bg-white text-slate-500 px-2 appearance-none">
                    <option value="undefined" selected disabled class="bg-inherit text-black">
                        {{"Please Select Expertise"| translate}}</option>
                    <option [ngValue]="expertise?.id" *ngFor="let expertise of expertises"
                        class="bg-inherit text-black">
                        <span *ngIf="lang != 'ar'">{{expertise.name_en}}</span>
                        <span *ngIf="lang == 'ar'">{{expertise.name_ar}}</span>
                    </option>
                </select>
                <img src="../../../assets/icons/arrow-semi-down.svg"
                    class="hidden sm:block w-2 absolute rtl:left-3 rtl:right-[unset] right-3 top-[70%]  z-10  ">

            </div>
            <div class="col-span-6 lg:col-span-2">
                <p class="text-base text-white mb-2 hidden lg:block">&nbsp;</p>
                <button type="button" (click)="search()"
                    class="bg-accent h-10 w-full flex justify-center items-center font-semibold rounded-md text-white"><img
                        src="../../../assets/icons/search.svg" alt="" class="w-5 h-5 text-center block rtl:ml-2 mr-2">
                    {{"Search"| translate}}</button>
            </div>
        </div>
    </div>
</div>

<div class="container mx-auto px-4 sm:px-14 md:px-24 mt-10">
    <div class="flex justify-between items-center">
        <div class="">
            <p class="text-lg text-white">{{totalItems}} {{"Architect"| translate}}</p>
        </div>
        <div class="">
            <button
                class="text-white duration-150 h-10 border-accent hover:bg-accent hover:text-white border py-2 px-4 flex items-center gap-2"
                (click)="sortArchitect()">
                <span>{{"Sort By"| translate}}</span>
                <img *ngIf="sort === 'ASC'" class="h-4" src="../../../assets/icons/arrow-white-up.svg" alt="">
                <img *ngIf="sort === 'DESC'" class="h-4" src="../../../assets/icons/arrow-white-down.svg" alt="">
            </button>
        </div>
    </div>
    <div>

        <!-- ARCHITECT LISTING -->
        <div class="grid grid-cols-12 gap-7 mt-10">

            <div *ngFor="let item of architectArr | paginate: { itemsPerPage: 6, currentPage: p, totalItems:totalItems }"
                class="col-span-12 sm:col-span-12 md:col-span-12 lg:col-span-6 xl:col-span-6 text-white">
                <app-similar-architects [item]="item"></app-similar-architects>
            </div>



        </div>

        <!--FEATURED ARCHITECT LISTING -->
        <!-- 
        <div *ngIf="listingType == 'FA'" class="grid grid-cols-12 gap-7 mt-10" >
                <div *ngFor="let featuredArchitect of architectArr | paginate: { itemsPerPage: 6, currentPage: p,totalItems:totalItems }" class="col-span-6 sm:col-span-6 md:col-span-6 lg:col-span-6 xl:col-span-4 text-white" >
                    <app-featured-architects-card [featuredArchitect]='featuredArchitect'></app-featured-architects-card>
                </div>
        </div> -->

        <div class="py-8 mx-auto">
            <pagination-controls class="projects-pagination text-center mx-auto" (pageChange)="p = $event"
                (click)="changePage(p)">
            </pagination-controls>

            <p class="text-xs w-full block text-center text-blue-500 space-x-0">{{1+6*(p-1)}} {{"to"| translate}}
                {{architectArr.length}}
                {{"of"| translate}} {{totalItems}} {{"Project"| translate}}</p>
        </div>
    </div>
</div>