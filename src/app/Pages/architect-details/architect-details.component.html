<section *ngIf="architectDetails">
    <div class="container mx-auto px-4 sm:px-14 md:px-24  py-5 ">
        <div class=" bg-white px-4 pt-10 md:px-10 2xl:px-16 rounded-lg lg:rounded-xl">
            <div class="flex mb-9">

                <!-- <p class="text-blue-400 text- font-medium cursor-pointer" (click)="routeToPrev()"> {{"Architects"|
                    translate}} <span class="text-gray-600 text- px-2"> >
                    </span></p> -->
                <p class="text-blue-400 text- font-medium cursor-pointer" [routerLink]="['/architect-listing']">
                    {{"Architects"| translate}}<span class="text-gray-600 text- px-2"> >
                    </span>{{architectDetails?.name}}</p>
                <p class="text- font-medium text-gray-700">{{architectDetails?.project_data?.name}}</p>
            </div>
            <div class="grid grid-cols-12 gap-5 lg:gap-5 xl:gap-16 ">
                <!-- left -->
                <div class="col-span-12 xl:col-span-5 2xl:col-span-5">
                    <div class="  rounded-md">
                        <div class=" flex items-center gap-5 md:gap-10 lg:gap-10 mb-8">

                            <div class="aspect-square w-28 md:h-36 md:w-36 lg:h-32 lg:w-32 2xl:h-48 2xl:w-48">
                                <img src="{{architectDetails?.company_logo}}"
                                    class="aspect-[1/1] object-cover w-full rounded-full shadow-lg" alt="">
                            </div>
                            <div class="flex flex-col gap-8">
                                <ul class="flex gap-3">
                                    <li *ngIf="architectDetails?.email"
                                        class="bg-[#2979cf]  inline-block overflow-hidden rounded"><a
                                            href="mailto:{{architectDetails?.email}}" class=""><img
                                                class="block p-1 md:p-1 lg:p-2" src="../../../assets/icons/Message.svg"
                                                alt=""></a></li>
                                    <li *ngIf="architectDetails?.phone"
                                        class="bg-[#002349]  inline-block overflow-hidden rounded"><a
                                            href="tel:{{architectDetails?.phone}}" class=""><img
                                                class="block p-1 md:p-1 lg:p-2"
                                                src="../../../assets/icons/localphone.svg" alt=""></a>
                                    </li>
                                    <li *ngIf="architectDetails?.whatsapp"
                                        class="bg-[#0ca512]  inline-block overflow-hidden rounded"><a target="_blank"
                                            href="https://wa.me/{{architectDetails?.whatsapp}}" class=""><img
                                                class="block p-1 md:p-1 lg:p-2" src="../../../assets/icons/whatsapp.svg"
                                                alt=""></a>
                                    </li>
                                    <li class="bg-accent  inline-block overflow-hidden rounded">
                                        <a [routerLink]="['/my-account/chats/' + architectId]"
                                            *ngIf="architectId != userID"><img class="block p-1 md:p-1 lg:p-2"
                                                src="../../../assets/icons/chatbubbles.svg" alt=""></a>

                                    </li>
                                </ul>
                                <div class=" text-right ">
                                    <button (click)="WriteReview()"
                                        class=" text-sm text-blue-600 inline-flex border border-blue-300 py-2 px-2">
                                        <span class="w-10 flex align-center"><img
                                                src="../../../assets/icons/comment1.svg" alt="" class="w-6 pr-1"></span>
                                        <p class="">
                                            {{"Write a review"| translate}}
                                        </p>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mb-10">
                            <p class="text-primary text-xl lg:text-3xl font-bold my-2  flex items-center gap-2 ">
                                {{architectDetails?.name}}

                                <span class="bg-accent font-normal text-white text-sm px-1">&#9733;
                                    {{architectDetails?.rating | number:'1.0-1'}}</span>

                            </p>
                            <div class=" " *ngIf="architectDetails?.address?.length > 0">
                                <p class="text-sm text-primary font-medium flex items-center gap-2"><img
                                        src="../../../assets/icons/location-on.svg" alt=""
                                        class="w-4 pb-5"><span>{{architectDetails?.address}}</span></p>
                            </div>
                        </div>
                        <div class="text-lighterBlack">
                            <p class="text-sm text-primary font-semibold  mb-4"><span
                                    class="text-steelBlue   font-medium">{{"Delivered"| translate}} :</span>
                                {{architectDetails?.projects_delivered}} {{"Project"| translate}}</p>
                            <p class="text-sm text-primary font-medium  mb-4"><span class="text-steelBlue   font-medium">{{"Expertise"|
                                    translate}}
                                    :</span>{{architectDetails?.expertise}}</p>
                            <p *ngIf="architectDetails?.project_types?.length > 0" class="text-sm text-primary font-medium  mb-4"><span
                                    class="  text-steelBlue  font-medium">{{"Projects Type"| translate}} :</span>
                                {{architectDetails?.project_types}}</p>
                        </div>

                        <!-- <div class="">
                            <ul class="flex">
                                <li class="text-steelBlue text-sm md:text-base lg:text-base font-medium">Delivered :</li>
                                <li class="pl-4 text-lighterBlack  text-sm md:text-base font-semibold">{{architectDetails?.delivered}} Project</li>
                            </ul>
                            
                            <ul class="flex mt-2">
                                <li class="text-lighterBlack text-sm md:text-base font-medium">Expertise :</li>
                                <li class="pl-4 text-steelBlue text-sm md:text-base break-words font-medium">{{architectDetails?.expertise}}</li>
                            </ul>
                            <ul class="flex mt-2">
                                <li class="text-lighterBlack text-sm md:text-base font-medium">Projects Type :</li>
                                <li class="pl-4 text-steelBlue text-sm md:text-base break-words font-medium">{{architectDetails?.project_types}}</li>
                            </ul>
                        </div> -->


                    </div>
                </div>
                <!-- right -->
                <div *ngIf="architectDetails?.project_photo != ''"
                    class="col-span-12 xl:col-span-7 lg:py-6 pb-20 md:py-10">
                    <img [src]="architectDetails?.project_photo" class="w-full h-full aspect-video rounded mb-2" alt="">
                    <p class="text-[.7rem]">{{architectDetails?.project_data?.projects[0].address}}</p>
                </div>
            </div>
            <div class="pb-8 lg:py-10" *ngIf="architectDetails?.about != ''">
                <h5 class="text-primary font-semibold text-lg">{{"About"| translate}}</h5>
                <p class="text-lighterBlack font-normal text-sm leading-7">{{architectDetails?.about}}</p>
            </div>
            <hr class="text-gray-400">
            <div>
                <div class=" items-center justify-start xl:justify-center overflow-auto pt-6">
                    <div class="flex gap-3">
                        <div id="ourProjects" (click)="changeTab($event)"
                            class="tab tab1 active px-3 md:px-6 lg:px-0  py-3  cursor-pointer text-center text-sm xl:text-lg  font-semibold inline-flex text-gray-400  border-b-primary ">
                            <span class="px-0 md:px-2 lg:px-2 text-sm xl:text-lg mr-1"><img
                                    src="../../../assets/icons/bulding-icon.png" alt=""
                                    class="h-5 w-5 xl:h-7 xl:w-7"></span> {{"Our Project"| translate}} ({{totalItems}})
                        </div>
                        <div id="rateAndReviews" (click)="changeTab($event)"
                            class="tab tab2 px-3 py-3  cursor-pointer  text-center text-sm xl:text-lg  font-semibold inline-flex text-gray-400  border-b-primary">
                            <span class="px-0 md:px-2 lg:px-2 "><img src="../../../assets/icons/rating-icon.png" alt=""
                                    class="h-5 w-5 xl:h-7 xl:w-7 mr-1"></span> {{"Rate & Reviews"| translate}}
                            ({{architectDetails?.review?.length}})
                        </div>
                    </div>

                    <div class="">
                        <div *ngIf="activeTab === 'ourProjects'">


                            <div *ngIf="collection?.length > 0" class="grid grid-cols-12 gap-y-10 lg:gap-x-5 lg:gap-y-5 pt-6 relative">
                                <div *ngFor="let item of collection | paginate: { itemsPerPage: 5, currentPage: p,totalItems:totalItems }"
                                    class="col-span-12 md:col-span-6 2xl:h-vh70 xl:h-vh60 lg:h-vh50  lg:col-span-6 hover:cursor-pointer  hover:scale-102 duration-200 ease-in-out hover:shadow-xl ">
                                    <div (click)="openGallery(item?.images)"
                                        [routerLink]="['/project-details',item?.id ]">

                                        <img src="{{item?.thumbnail}}" alt="" class="rounded-md w-full aspect-[3/2]">
                                    </div>
                                    <div class="pt-2 px-1 group-hover:px-5 group-hover:py-5">
                                        <h5 class="text-lg md:text-lg lg:text-xl text-primary font-bold">
                                            {{item?.title}}
                                        </h5>
                                        <div class="flex items-center  pt-2 ">
                                            <span class="">
                                                <img src="../../../assets/icons/placeholder-filled-point.png" alt=""
                                                    class="w-5 lg:w-3.5 pb-5">
                                            </span>
                                            <p class="text-sm md:text-xs lg:text-sm text-primary font-medium pl-2">
                                                {{item?.address}}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="py-8 col-start-5 lg:col-start-1 col-span-4 lg:col-span-full mx-auto">
                                    <!-- <ul class="flex  justify-center gap-1">
                                        <li class="border  text-center bg-slate-300"><a class="py-1 px-3 block" (click)="changePage()">1</a>
                                        </li>
                                        <li class="border  text-center bg-slate-300"><a class="py-1 px-3 block" (click)="changePage()">2</a>
                                        </li>
                                    </ul> -->

                                    <pagination-controls class="projects-pagination" (pageChange)="p = $event"
                                        (click)="changePage(p)">
                                    </pagination-controls>

                                    <p class="text-xs text-center text-blue-500 space-x-0">{{1+6*(p-1)}} {{"to"|
                                        translate}} {{collection.length}}
                                        {{"of"| translate}} {{totalItems}} {{"Architects"| translate}}</p>

                                    <!-- <p class="text-xs w-full block text-center text-blue-500 space-x-0">{{1+6*(p-1)}}
                                        {{"to"| translate}}
                                        {{architectArr.length}}
                                        {{"of"| translate}} {{totalItems}} {{"Project"| translate}}</p> -->
                                </div>

                            </div>

                            <div *ngIf="collection?.length == 0" class="h-28 flex items-center justify-center">
                                <p class="text-center font-semibold text-xl text-gray-400">
                                    {{"No Projects"| translate}}
                                </p>
                            </div>





                            <!-- <div *ngIf="loader === true"
                                class="absolute h-full w-full inset-0 flex justify-center items-center"><img
                                    class="h-20 w-20" src="../../../assets/icons/loader.gif">
                                </div> -->
                        </div>



                        <div *ngIf="activeTab === 'rateAndReviews'" class=" pb-6">
                            <div *ngIf="reviewsArr?.length > 0">
                                <div *ngFor="let review of reviewsArr">
                                    <app-review-card [review]="review"></app-review-card>
                                </div>
                            </div>

                            <div *ngIf="reviewsArr?.length == 0" class="h-28 flex items-center justify-center">
                                <p class="text-center font-semibold text-xl text-gray-400">
                                    {{"No reviews"| translate}}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- similar architects -->
        <div class="justify-center items-center relative container mx-auto px-4 sm:px-14 md:px-24 mt-10"
            *ngIf="architectDetails?.similar_architects?.length > 0">

            <div class="">
                <div class="">
                    <p class="text-xl lg:text-2xl text-white  font-semibold px-4 md:px-0">Similar Architects</p>
                </div>

                <owl-carousel-o [options]="similarArchitectsOptions" #similarArchitects class="similarArchitects">
                    <ng-container *ngFor="let item of architectDetails?.similar_architects">
                        <ng-template carouselSlide>
                            <app-similar-architects [item]="item" [page]="'details'"></app-similar-architects>
                        </ng-template>
                    </ng-container>
                </owl-carousel-o>
            </div>

            <p (click)="similarArchitects.prev()" class="hover:cursor-pointer absolute top-1/2 left-12 hover:scale-105">
                <img class="h-7 w-7" src="../../../assets/icons/arrow-left.png">
            </p>
            <p (click)="similarArchitects.next()"
                class="hover:cursor-pointer absolute top-1/2 right-12 hover:scale-105"><img class="h-7 w-7"
                    src="../../../assets/icons/arrow-right.png"></p>
        </div>

    </div>

</section>

<div *ngIf="isReviewOpen">
    <app-write-a-review [data]="architectDetails" (reviewAdded)="getArchitectDetails()"
        (close)="isReviewOpen = false"></app-write-a-review>
</div>


<div *ngIf="showGallery">
    <app-gallery-image-viewer (closeGallery)="showGallery = false"></app-gallery-image-viewer>
</div>