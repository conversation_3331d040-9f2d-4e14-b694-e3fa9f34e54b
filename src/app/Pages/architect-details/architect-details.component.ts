import { Location } from '@angular/common';
import { Component, OnInit, Renderer2, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { ToastrService } from 'ngx-toastr';
import { GalleryImageViewerComponent } from 'src/app/Components/gallery-image-viewer/gallery-image-viewer.component';
import { configSettings } from 'src/app/Config/config.settings';
import { ArchitectDetailsService } from 'src/app/Services/Architect-details/architect-details.service';


@Component({
  selector: 'app-architect-details',
  templateUrl: './architect-details.component.html',
  styleUrls: ['./architect-details.component.scss']
})
export class ArchitectDetailsComponent implements OnInit {

  constructor(
    private configSettings: configSettings,
    private router: Router,
    private location: Location,
    private route: ActivatedRoute,
    private renderer: Renderer2,
    private architectDetailsService: ArchitectDetailsService,
    private toastr: ToastrService,


  ) {

    // to get params for route
    this.getRoutingParams()
  }

  user: any
  userId: any

  @ViewChild(GalleryImageViewerComponent) GalleryImageViewerComponent: GalleryImageViewerComponent

  ngOnInit(): void {
    this.user = this.configSettings.getUserDetails();
    this.userId = this.user?.user_id
    this.getArchitectDetails()
  }

  // to get params for route
  getRoutingParams() {
    this.route.params.subscribe(params => {
      // console.log(params.id);
      this.architectId = params?.id

    })
  }

  // lang = this.configSettings.getLang()
  a1 = true;
  architectDetails: any
  activeTab: string = "ourProjects"
  numberOfPages: any
  architectId: number = 0
  reviewsArr: any = []
  showGallery: boolean = false

  p: number = 1;
  collection: any = []
  totalItems: any
  fromArticle: any
  toArticle: any
  itemsPerPage: number
  loader: any

  isReviewOpen: boolean = false
  userID = this.configSettings.getUserID()

  similarArchitectsOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: true,
    navSpeed: 700,
    autoWidth: false,
    margin: 15,
    center: false,
    responsive: {
      0: {
        items: 1
      },
      640: {
        items: 1
      },
      768: {
        items: 1
      },
      896: {
        items: 1
      },
      1024: {
        items: 2
      },
      1280: {
        items: 2
      },
      1536: {
        items: 2
      }
    },
    nav: false
  }



  changeTab(event: any) {
    Array.from(document.querySelectorAll('.tab')).forEach(function (el) {
      el.classList.remove('active');
    });
    this.renderer.addClass(event.currentTarget, 'active')
    if (event.currentTarget.id !== this.activeTab) {
      this.activeTab = event.currentTarget.id
    }
  }

  WriteReview() {
    if (this.userId) {
      this.configSettings.setIsOTPVerified(true)
      this.isReviewOpen = true
      document.body.style.overflow = "hidden";
    } else {
      this.toastr.warning('Please Login First')
    }
  }

  getArchitectDetails() {

    this.configSettings.setShowLoader(true)

    const getParams = {
      // lang: this.lang,
      // id: this.architectId,
      id: this.architectId,
      per_page: 6 * this.p
      // per_page=2
      // per_page: 1
    }

    this.architectDetailsService.getArchitectDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.architectDetails = response.body.data
            this.architectId
            this.numberOfPages = this.architectDetails?.project_data?.projects?.totalCount
            this.collection = this.architectDetails.project_data?.projects
            this.reviewsArr = this.architectDetails?.review
            this.totalItems = this.architectDetails.project_data.totalItemCount
            this.itemsPerPage = this.architectDetails?.project_data?.itemPerPage
            this.fromArticle = this.architectDetails?.project_data?.offset + 1
            this.toArticle = this.architectDetails?.project_data?.offset + this.itemsPerPage
            this.configSettings.setShowLoader(false)

          }
        }
      },
      error: (err) => {
        this.toastr.error(err)
      }
    })
  }

  changePage(pageNumber: any) {

    const getParams = {
      // lang: this.lang,
      user_id: 47,
      page: pageNumber
    }

    this.loader = true

    this.architectDetailsService.getArchitectDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.loader = false
            this.collection = response.body.data[0].projects.items
            this.fromArticle = response.body.data[0].projects.offset + 1
            this.toArticle = response.body.data[0].projects.offset + this.itemsPerPage
          }
        }
      },
      error: (err) => {
        this.toastr.error(err)
      }
    })
  }

  openGallery(value: any) {
    this.a1 = false

    if (value?.length > 0) {
      this.showGallery = true
      this.GalleryImageViewerComponent.setImage(value)
    } else {
      this.toastr.error('No Images For this Project')
    }
  }


  routeToPrev() {
    this.location.back()
  }


  routeTochat() {
    if (this.architectDetails) {
      this.router.navigate(['/my-account/chats/' + this.architectId])
    } else {
      this.router.navigate(['/login'])
    }
  }
}
