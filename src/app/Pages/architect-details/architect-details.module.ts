import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ArchitectDetailsRoutingModule } from './architect-details-routing.module';
import { ArchitectDetailsComponent } from './architect-details.component';
import {NgxPaginationModule} from 'ngx-pagination';
import { SimilarArchitectsModule } from 'src/app/Components/similar-architects/similar-architects.module';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { WriteAReviewModule } from 'src/app/Components/write-a-review/write-a-review.module';
import { ReviewCardModule } from 'src/app/Components/review-card/review-card.module';
import { GalleryImageViewerModule } from 'src/app/Components/gallery-image-viewer/gallery-image-viewer.module';
import { TranslateModule } from '@ngx-translate/core';
import { ProjectDetailsModule } from '../project-details/project-details.module';

@NgModule({
  declarations: [ArchitectDetailsComponent],
  imports: [
    CommonModule,
    SimilarArchitectsModule,
    ArchitectDetailsRoutingModule,
    NgxPaginationModule,
    CarouselModule,
    ReviewCardModule,
    WriteAReviewModule,
    GalleryImageViewerModule,
    TranslateModule,
    ProjectDetailsModule
  ],exports: [ArchitectDetailsComponent]
})
export class ArchitectDetailsModule { }
