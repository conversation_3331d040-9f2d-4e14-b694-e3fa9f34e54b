import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PostPropertyRoutingModule } from './post-property-routing.module';
import { PostPropertyFormModule } from 'src/app/Components/post-property-form/post-property-form.module';
import { PostPropertyComponent } from './post-property.component';
import { FeatureMyPostModule } from 'src/app/Components/feature-my-post/feature-my-post.module';


@NgModule({
  declarations: [PostPropertyComponent],
  imports: [
    CommonModule,
    PostPropertyRoutingModule,
    PostPropertyFormModule,
    FeatureMyPostModule
  ]
})
export class PostPropertyModule { }
