import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-post-property',
  templateUrl: './post-property.component.html',
  styleUrls: ['./post-property.component.scss']
})
export class PostPropertyComponent implements OnInit {
  postPropertyParams: any
  constructor() { }
  showform: any = true
  ngOnInit(): void {
  }

  getPropertyParams(event: any) {
    this.postPropertyParams = event
    this.showform = false
    window.scrollTo({
      top: 0,
      left: 0,
    });
  }

  showOrHideForm() {
    this.showform = true
  }

}
