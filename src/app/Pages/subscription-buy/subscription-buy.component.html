<div class='container mx-auto lg:px-52 pt-4 pb-8 '>
    <div class="bg-white rounded-md px-12 py-3 xl:px-4 2xl:px-12">
        <div>
            <button class="text-gray-400">&#8592;<span class="ml-4 text-gray-800 font-semibold">Plan Details/pay</span></button>
        </div>
        <div class="grid grid-cols-12  gap-5 justify-center  py-8 px-32 xl:px-4 2xl:px-8 mx-auto">
            
            <div class=" xl:col-start-2 col-span-11 xl:col-span-5">
                <div class="border rounded-lg p-4" style="background-image: url(../../../assets/images/black-bg.jpg);">
                <!-- bg-cover bg-no-repeat  bg-center use this on real image time -->
                    <h6 class="text-primary font-bold text-2xl xl:text-xl 2xl:text-2xl pb-4 w-1/2">
                        GOLD MEMBER 
                    </h6>
                    <div>
                        <h6 class="text-primary text-2xl font-bold">
                            10 {{"KD" | translate}}/<span class="font-semibold text-lg">Month</span>
                        </h6>
                    </div>
                    <div>
                      <ul class="flex justify-between text-sm text-white pb-3">
                        <li>Number of Listings</li>
                        <li>01</li>
                      </ul>
                      <ul class="flex justify-between text-sm text-white pb-3">
                        <li>Validity</li>
                        <li>30 Days</li>
                      </ul>
                      <ul class="flex justify-between text-sm text-white pb-3">
                        <li>High position</li>
                        <li>04 Days</li>
                      </ul>
                      <ul class="flex justify-between text-sm text-white pb-3">
                        <li>Max.Photos</li>
                        <li>4 Photos</li>
                      </ul>
                    </div>
                </div>
                    
                    <div>
                        <p class="text-gray-600 font-medium pl-5">
                            Billed monthly
                            </p>
                    </div>  
                       
            </div>
            
            <div class="col-span-12 xl:col-span-5">
                <div class=" items-center justify-items-start bg-red-50 bg-opacity-30">

                <div class="flex">
                    <div id="resale" (click)="changeTab($event)"
                        class="tab tab1 active  py-3  cursor-pointer  text-accent text-center border-b-accent text-sm w-1/2">{{"DEBIT CARD"}}</div>
                    <div id="rent" (click)="changeTab($event)"
                        class="tab tab2  py-3  cursor-pointer  text-accent text-center text-sm w-1/2">{{"CREDIT CARD"}}</div>
                </div>
                <div *ngIf="activeTab === 'resale'"
                 class="bg-zinc-100 px-4 py-2">
                    <div class=" ">                        
                       <label for="" class="text-sm">CARD NUMBER</label>
                        <input 
                            type="text" 
                            name="email" 
                            placeholder="Enter Your card number" 
                            class="w-full py-2 px-4 border hover: border-gray-500  text-sm  placeholder:font-normal focus:outline-none"/>
                            <div class="relative">
                                <div
                                  class="inline-flex items-center justify-center absolute left-40 md:left-88 lg:left-44 xl:left-60 2xl:left-72 bottom-4 h-full w-6 lg:w-8  text-gray-400" >
                                  <img src="../../../assets/icons/visa.png" alt="">
                                </div>
                             </div>
                    </div>

                    <div class="pt-3 ">                        
                        <label for="" class="text-sm">ACCOUNT HOLDER</label>
                         <input 
                             type="text" 
                             name="name" 
                             placeholder="Enter Your name" 
                             class="w-full py-2 px-4 border hover: border-gray-500  text-sm  placeholder:font-normal focus:outline-none"/>                            
                     </div>

                     <div class=" pt-3">  
                        <div>                      
                        <label for="" class="text-sm">EX.DATE</label>
                        </div>
                        <div class="bg-white border border-gray-500 ">
                            <select name="Month" class=" w-1/2 lg:w-1/3  py-2 bg-white px-4 hover: border-gray-500 border-r-0    text-sm  placeholder:font-normal focus:outline-none">
                                <option value="MM">MM</option>
                                <option value="january">January</option>
                              <option value="february">February</option>
                              <option value="march">March</option>
                              <option value="april">April</option>
                              <option value="may">May</option>
                              <option value="june">June</option>
                              <option value="july">July</option>
                              <option value="august">August</option>
                              <option value="september">September</option>
                              <option value="october">October</option>
                              <option value="november">November</option>
                              <option value="december">December</option>
                            </select>
                            <span>
                                
                                    <select name="Year" class=" w-1/2 lg:w-1/3  py-2 bg-white  px-4 hover: border-gray-500  border-l-0  text-sm  placeholder:font-normal focus:outline-none">
                                        <option value="YYYY">YYYY</option>
                                        <option value="2016">2016</option>
                                      <option value="2017">2017</option>
                                      <option value="2018">2018</option>
                                      <option value="2019">2019</option>
                                      <option value="2020">2020</option>
                                      <option value="2021">2021</option>
                                      <option value="2022">2022</option>
                                      <option value="2023">2023</option>
                                      <option value="2024">2024</option>
                                    </select>
                                  
                            </span>
                        </div>
                       
                                                 
                     </div>
                     <div class="pt-3 ">                        
                        <label for="" class="text-sm">CVV NO</label>
                         <input 
                             type="text" 
                             name="password" 
                             placeholder="Enter Your card cvv.no" 
                             class="w-full py-2 px-4 border hover: border-gray-500  text-sm  placeholder:font-normal focus:outline-none"/>                            
                     </div>

                     <div class="text-sm  font-medium w-full pt-4">
                        <button 
                        [routerLink]="'/'"
                            type="button"   
                            class="text-center w-full py-3 bg-primary  text-white">
                            Continue
                        </button>
                    </div>
                    <div class="text-gray-500 font-medium text-xs pt-3 pb-6">
                        <p>By proceding you agree with our <span><a href="" class="text-primary">Terms of Service Privacy Policy</a></span> </p>
                    </div>

                </div>



                <div *ngIf="activeTab === 'rent'"
                 class="bg-zinc-100 px-4 py-2">
                    <div class=" ">                        
                       <label for="" class="text-sm">CARD NUMBER</label>
                        <input 
                            type="text" 
                            name="email" 
                            placeholder="Enter Your card number" 
                            class="w-full py-2 px-4 border hover: border-gray-500  text-sm  placeholder:font-normal focus:outline-none"/>
                            <div class="relative">
                                <div
                                  class="inline-flex items-center justify-center absolute left-40 md:left-88 lg:left-44 xl:left-60 2xl:left-72 bottom-4 h-full w-6 lg:w-8  text-gray-400" >
                                  <img src="../../../assets/icons/visa.png" alt="">
                                </div>
                             </div>                            
                    </div>

                    <div class="pt-3 ">                        
                        <label for="" class="text-sm">ACCOUNT HOLDER</label>
                         <input 
                             type="text" 
                             name="name" 
                             placeholder="Enter Your name" 
                             class="w-full py-2 px-4 border hover: border-gray-500  text-sm  placeholder:font-normal focus:outline-none"/>                            
                     </div>

                     <div class=" pt-3">  
                        <div>                      
                        <label for="" class="text-sm">EX.DATE</label>
                        </div>
                        <div class="bg-white border border-gray-500 ">
                            <select name="Month" class=" w-1/2 lg:w-1/3  py-2 bg-white px-4 hover: border-gray-500 border-r-0    text-sm  placeholder:font-normal focus:outline-none">
                                <option value="MM">MM</option>
                                <option value="january">January</option>
                              <option value="february">February</option>
                              <option value="march">March</option>
                              <option value="april">April</option>
                              <option value="may">May</option>
                              <option value="june">June</option>
                              <option value="july">July</option>
                              <option value="august">August</option>
                              <option value="september">September</option>
                              <option value="october">October</option>
                              <option value="november">November</option>
                              <option value="december">December</option>
                            </select>
                            <span>
                                
                                    <select name="Year" class=" w-1/2 lg:w-1/3  py-2 bg-white  px-4 hover: border-gray-500  border-l-0  text-sm  placeholder:font-normal focus:outline-none">
                                        <option value="YYYY">YYYY</option>
                                        <option value="2016">2016</option>
                                      <option value="2017">2017</option>
                                      <option value="2018">2018</option>
                                      <option value="2019">2019</option>
                                      <option value="2020">2020</option>
                                      <option value="2021">2021</option>
                                      <option value="2022">2022</option>
                                      <option value="2023">2023</option>
                                      <option value="2024">2024</option>
                                    </select>
                                  
                            </span>
                        </div>
                       
                                                 
                     </div>
                     <div class="pt-3 ">                        
                        <label for="" class="text-sm">CVV NO</label>
                         <input 
                             type="text" 
                             name="password" 
                             placeholder="Enter Your card cvv.no" 
                             class="w-full py-2 px-4 border hover: border-gray-500  text-sm  placeholder:font-normal focus:outline-none"/>                            
                     </div>

                     <div class="text-sm  font-medium w-full pt-4">
                        <button 
                           [routerLink]="'/'"
                            type="button"   
                            class="text-center w-full py-3 bg-primary  text-white">
                            Continue
                        </button>
                    </div>
                    <div class="text-gray-500 font-medium text-xs pt-3 pb-6">
                        <p>By proceding you agree with our <span><a href="" class="text-primary">Terms of Service Privacy Policy</a></span> </p>
                    </div>

                </div>
             </div>
            </div>
             
              
            
        </div>
    </div>    
</div>
