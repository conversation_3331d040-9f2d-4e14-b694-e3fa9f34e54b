import { Component, OnInit, Renderer2 } from '@angular/core';

@Component({
  selector: 'app-subscription-buy',
  templateUrl: './subscription-buy.component.html',
  styleUrls: ['./subscription-buy.component.scss']
})
export class SubscriptionBuyComponent implements OnInit {

  constructor(
    private renderer: Renderer2
  ) { }

  ngOnInit(): void { }

  activeTab: string = "resale"
  changeTab(event: any) {
    Array.from(document.querySelectorAll('.tab')).forEach(function (el) {
      el.classList.remove('active');
    });
    this.renderer.addClass(event.target, 'active')
    if (event.target.id !== this.activeTab) {
      this.activeTab = event.target.id
    }
    
  }
}

