import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SubscriptionBuyRoutingModule } from './subscription-buy-routing.module';
import { SubscriptionBuyComponent } from './subscription-buy.component';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [SubscriptionBuyComponent],
  imports: [
    CommonModule,
    SubscriptionBuyRoutingModule,
    TranslateModule
  ]
})
export class SubscriptionBuyModule { }
