import { Location } from '@angular/common';
// import { Component, OnInit, Renderer2 } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
// import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { CheckoutSubscriptionService } from 'src/app/Services/Checkout-subscription/checkout-subscription.service';
import { PlansService } from 'src/app/Services/Plans/plans.service';
import { AfterViewChecked, Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
// import { configSettings } from 'src/app/Config/config.settings';
import { UserAuthenticationService } from 'src/app/Services/user-authentication.service';
import { UserTypesService } from 'src/app/Services/user-types.service';
import { UserService } from 'src/app/Services/User/user.service';
import { SocialAuthService } from "@abacritt/angularx-social-login";
import { FacebookLoginProvider } from "@abacritt/angularx-social-login";
import { from } from 'rxjs';
import { CmsService } from 'src/app/Services/Cms/cms.service';
import { TranslateService } from '@ngx-translate/core';
import { AddressHelper } from 'src/app/globalFunctions/addressHelpers';
import { HttpClient } from '@angular/common/http';


@Component({
  selector: 'app-choose-plan',
  templateUrl: './choose-plan.component.html',
  styleUrls: ['./choose-plan.component.scss']
})
export class ChoosePlanComponent implements OnInit, AfterViewChecked  {
  TOSModel = false;
  PPModel = false;
  TOS: any;
  PP: any;

  res: any;

  constructor(
    private location: Location,
    private renderer: Renderer2,
    private plansService: PlansService,
    private toastr: ToastrService,
    private configSettings: configSettings,
    private checkoutSubscriptionService: CheckoutSubscriptionService,
    private router: Router,
    private route: ActivatedRoute,
    public cmsService: CmsService,
    private translateService: TranslateService

  ) { }

  ngAfterViewChecked(): void {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'auto'
    })
  }

  // Variables
  allPlans: any
  allPaymentMethods: any
  page = 1
  choosenPlan: any
  userID: any
  choosenPaymode: any = ''

  UserType: string
  userDetails: any;

  ngOnInit(): void {


    //this is passing undefined in the 
    this.route.params.subscribe(params => {
      if (params.type != undefined) {
        this.UserType = params.type
        this.page = 1
      } else {
        var userType = this.configSettings.getUserType();
        this.UserType = userType;
      }
      this.configSettings.setShowLoader(true)
      this.getAllPlans()
    });
    // this.userDetails = this.configSettings.getUserDetails()

    //@bob this has been added as per request up to u want to keep in the flow or remove it 
    // this.getCms('TC')

    this.userID = this.configSettings.getUserID()

  }
  getCms(type: string) {

    let getParams = {
      type: type
    }
    this.cmsService?.getCms(getParams).subscribe({
      next: (response: any) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            if (type == 'TC') {
              this.TOS = response?.body.data.contents.content_en
              console.log(this.TOS)
            } else {
              this.PP = response?.body.data.contents.content_en

            }
          }
        }
      },
      error: (err: any) => {
        this.toastr.error('', err.error.message);
      }
    })

  }



  goBack() {
    this.location.back()
  }
  OpenPPModel() {
    this.PPModel = !this.PPModel
  }

  OpenTOSModel() {
    this.TOSModel = !this.TOSModel
  }





  getAllPlans() {
    const getParams = {
      user_type: this.UserType
    }

    this.plansService.getPlans(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allPaymentMethods = response.body.data.Payment_methods
            this.allPlans = response.body.data.plans
            this.configSettings.setShowLoader(false)

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }



  choosenPlanFunc(plan: any) {
    this.choosenPaymode = ''
    this.page = 2
    // setTimeout(() => {

    // }, 1);
    this.choosenPlan = plan
  }


  choosePaymentType(e: any) {
    Array.from(document.querySelectorAll('.payment-method')).forEach(function (el) {
      el.classList.remove('active');
    });
    this.renderer.addClass(e.currentTarget, 'active')
    this.choosenPaymode = e.currentTarget.id
  }

  checkout() {

    if (this.choosenPaymode === '') {
      this.translateService.get('Please choose a payment method').subscribe((res: string | undefined) => {
        this.toastr.error('', res);

      })
      return
    }
    const confirmURL = window.location.origin + this.router.createUrlTree(['/confirmation-page']);

    const getParams = {}

    const postParams = {

      user_id: this.userID,
      plan_id: this.choosenPlan.id,
      pay_mode: this.choosenPaymode,
      redirect_url: confirmURL
    }

    // console.log(postParams)
    this.configSettings.setShowLoader(true)

    this.checkoutSubscriptionService.checkoutPlan(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            window.location.href = response.body.data.payment_url;

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }


}
