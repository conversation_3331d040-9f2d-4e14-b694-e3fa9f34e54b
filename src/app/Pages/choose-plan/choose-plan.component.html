<div class='container mx-auto px-4 md:px-20 lg:px-52 pt-4 pb-8 '>
  <div *ngIf="page === 1" class="bg-white rounded-md px-2 md:px-4  py-3 xl:px-4 2xl:px-12 min-h-[40rem]">

    <div  class="">
      <div class="  my-8">
        <!-- <div>
          <button (click)="goBack()" class="text-gray-400">&#8592;
            <span class="ml-4 text-gray-800 font-semibold">Our plan</span>
          </button>
        </div> -->
        <h4 class="text-5xl font-bold pb-4 text-center">
          {{"Buy a plan" | translate}}
        </h4>
        <p class="text-sm font-semibold text-zinc-600 text-center">
          {{"Try a plan for 30 day, or get started with" | translate}}
        </p>
      </div>
      <div class="grid grid-cols-12  gap-5 justify-center  pt-8 px-8 xl:px-4 2xl:px-8">
        <div *ngFor="let plan of allPlans"
          class="col-span-12 md:col-span-6 xl:col-span-3 border rounded-lg p-4 bg-cover bg-no-repeat bg-center flex flex-col bg "
          [ngStyle]="{'background-color': plan?.hex_code}">
          <!-- bg-cover bg-no-repeat bg-center use this on real image time   [ngStyle]="{'color': plan?.hex_code}"-->
          <div class="flex-grow">
            <h6 class="text-[#002349] font-bold text-2xl xl:text-lg leading-cls pb-4 w-1/2">
              {{plan.name}}
            </h6>
          </div>
          <div>
            <h6 class="text-[#002349] text-xl font-bold">
              {{plan.price}} {{"KD" | translate}}/<span class="font-semibold text-lg">{{plan.billing_type_caption}}</span>
            </h6>
            <p  class="text-zinc-600 font-medium pb-2 text-xs">
              {{"Billed" | translate}} {{plan.billing_type_caption}}
            </p>
          </div>
          <div class="">
            <ul class=" font-medium text-xs text-white pb-3">
              <div *ngIf="plan.user_type==3 || plan.user_type==1" class="flex justify-between items-center">
                <div>{{"Number of Properties" | translate}}</div>
                <div>{{plan.max_posts}}</div>
              </div>
              <div *ngIf="plan.user_type==4" class="flex justify-between items-center">
                <li>{{"Number of Projects" | translate}}</li>
                <li>{{plan.max_projects}}</li>
              </div>
            </ul>
            <ul class="flex justify-between font-medium text-xs text-white pb-3">
              <li *ngIf="plan.user_type==3 || plan.user_type==1">{{"Photos per Property" | translate}}</li>
              <li *ngIf="plan.user_type==4">{{"Photos per Project" | translate}}</li>
              <li>{{plan.max_photos}} </li>
            </ul>
            <ul class="flex justify-between font-medium text-xs text-white pb-3">
              <li>{{"Number of Stories" | translate}}</li>
              <li>{{plan.max_stories}}</li>
            </ul>
            <ul class="flex justify-between font-medium text-xs text-white pb-3">
              <li>{{"Validity" | translate}}</li>
              <li>{{plan.duration}}</li>
            </ul>
            <!-- <ul class="flex justify-between font-medium text-xs text-white pb-3">
              <li>{{"High position" | translate}}</li>
              <li>{{plan.high_position}} Days</li>
            </ul> -->
          </div>
          <div>
            <button (click)="choosenPlanFunc(plan)"
              class="w-full text-base font-semibold border rounded-md text-white  border-accent bg-accent  hover:bg-accentDark duration-150  py-1.5">
              {{"Buy Now" | translate}}</button>
          </div>
        </div>
      </div>
    </div>

  </div>

    <!-- Page 2 (PAYMENT PAGE) -->
    <div *ngIf="page === 2" class="bg-white rounded-md px-2 md:px-4  py-3 xl:px-4 2xl:px-12 min-h-[38rem]">
    <div  class="relative " >
      <div>
        <button (click)="page = 1" class="text-gray-400 flex mb-7"><span class="rtl:hidden block">&#8592;</span>
          <span class="rtl:block hidden">&#8594;</span>
          <span class="rtl:mr-4 ml-4 text-gray-800 font-semibold text-sm">{{"Plan Details/Pay" | translate}}</span>
        </button>
      </div>
      <div class="xl:my-40">
        <div class="flex flex-col xl:flex-row justify-center align-middle px-4 md:px-10 gap-7">
          <div class="  place-self-center ">
            <!-- <div class="h-64 w-80 rounded-lg p-5 bg-gradient-to-b from-[#d20000] to-black"> -->
            <div class="h-64 w-80 rounded-lg p-5 bg-cover bg-no-repeat bg-center bg"
              [ngStyle]="{'background-color': choosenPlan?.hex_code}">
              <!-- class="col-span-12 md:col-span-6 xl:col-span-3 border rounded-lg p-4 bg-cover bg-no-repeat bg-center flex flex-col bg "
          [ngStyle]="{'background-color': plan?.hex_code}" -->
              <div class="uppercase text-lighterBlack font-semibold mb-5">
                <p>{{choosenPlan.name}}</p>
              </div>
              <div class="text-lighterBlack tracking-tight">
                <p class="font-bold text-2xl">{{choosenPlan.price}} {{"KD" | translate}}/{{choosenPlan.billing_type_caption}}</p>
                <p class="font-semibold text-lighterBlack text-xs pb-3">{{"Billed" | translate}} {{choosenPlan.billing_type_caption}}</p>
              </div>
              <div class="text-[.7rem] leading-4 text-white flex flex-col gap-3">
                <div class=" items-center">
                  <div *ngIf="choosenPlan.user_type==3 || choosenPlan.user_type==1" class="flex justify-between pb-3">
                    <p>{{"Number of Properties" | translate}}</p>
                    <p>{{choosenPlan.max_posts}}</p>
                  </div>
                  <!-- <div *ngIf="choosenPlan.user_type==3" class="flex justify-between pb-3">
                    <p>{{"Number of Projects" | translate}}</p>
                    <p>{{choosenPlan.max_projects}}</p>
                  </div> -->
                  <div class="flex justify-between items-center pb-3">
                    <p *ngIf="choosenPlan.user_type==3 || choosenPlan.user_type==1">{{"Photos per posts" | translate}}</p>
                    <p *ngIf="choosenPlan.user_type==4">{{"Photos per Project"| translate}}</p>
                    <p>{{choosenPlan.max_photos}} {{"Photos" | translate}}</p>
                  </div>
                
                <div class="flex justify-between items-center pb-3">
                  <p>{{"Number of Stories" | translate}}</p>
                  <p>{{choosenPlan.max_stories}}</p>
                </div>
                <div class="flex justify-between items-center pb-3">
                  <p>{{"Validity" | translate}}</p>
                  <p>{{choosenPlan.duration}}</p>
                </div>
                <!-- <div class="flex justify-between items-center">
                  <p>{{"High position" | translate}}</p>
                  <p>{{choosenPlan.high_position}} Days</p>
                </div> -->
                </div>
              </div>
            </div>
           
          </div>
          <!-- right side (PAYMENT FIELDS) -->
          <div class=" place-self-center ">
            <div class=" w-[20rem] md:w-[25rem] p-5 rounded-md bg-lighterGray mb-3">
              <p class="mb-3 text-primary text-lg font-bold">{{"Select a payment method" | translate}}:</p>
              <div class="flex justify-center items-center gap-8 mb-5">
                <div  [id]="paymentType.id"  class="payment-method  border-2 relative" *ngFor="let paymentType of allPaymentMethods" (click)="choosePaymentType($event)">
                  <img [ngClass]="choosenPaymode == paymentType.id ? 'activeCheck' : ''"   src="../../../assets/icons/yellow-tick.svg" class="payment-method hidden">
                  <img [src]="paymentType.img"
                      class="w-12 h-8 cursor-pointer" >
              </div>
              </div>
              <!-- [routerLink]="['/post-property']" -->
              <button (click)="checkout()" class="block bg-primary text-white w-full py-3 mb-5">{{"Continue" | translate}}</button>
              <p class="text-[.7rem] font-semibold tracking-tight text-[#9a9a9a] ">{{"By proceeding you agree with our" | translate}}
                <span class="text-primary cursor-pointer hover:underline underline-offset-2" (click)="OpenTOSModel();getCms('TC')" >{{"Terms of Service" | translate}}</span>
                <span class="text-primary cursor-pointer hover:underline underline-offset-2" (click)="OpenPPModel();getCms('PP')"> & {{"Privacy Policy" | translate}}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>

   

 

  

<div *ngIf="PPModel">
  <div  class="bg-overlay fixed inset-0 z-[99]"></div>
  <div
      class="z-[100] mx-auto fixed overflow-y-auto max-h-[90vh] scroll-thin top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-10 py-5 bg-white w-11/12 md:w-4/5 lg:w-2/5 xl:w-2/5 rounded-xl">
      <div>
          <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b">
              <h5 class="text-2xl font-bold leading-normal ">
                  {{"Privacy policy" | translate}}
              </h5>
              <button  type="button" (click)="OpenPPModel()"
                  class="flex hover:cursor-pointer duration-200 ease-in-out hover:opacity-75 ">
                  <img src="../../../assets/icons/close-icon.png" alt="" class="w-6">
                  <span class="text-accent ">{{"Close" | translate}}</span>
              </button>
          </div>
          <div>
              <p class="text-black text-sm font-semibold font-oxygen py-3">{{"Must Read" | translate}}</p>
              <p [innerHTML]="PP"></p>


          </div>



          <div class="py-6">
              <button (click)="OpenPPModel()"
                  class="w-5/6 bg-primary hover:bg-blue-900 text-white p-2 mx-6  lg:mx-5 xl:mx-6 2xl:mx-8 rounded-sm">
                  {{"Yes I Agree" | translate}}</button>
          </div>


      </div>
  </div>
</div>

<div *ngIf="TOSModel">
  <div class="bg-overlay fixed inset-0 z-[99]"></div>
  <div
      class="z-[100] mx-auto  fixed overflow-y-auto max-h-[90vh] scroll-thin top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-10 py-5 bg-white w-11/12 md:w-4/5 lg:w-2/5 xl:w-2/5 rounded-xl">
      <div>
          <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b">
              <h5 class="text-2xl font-bold leading-normal ">
                  {{"Terms & Condition" | translate}}
              </h5>
              <button type="button" (click)="OpenTOSModel()"
                  class="flex hover:cursor-pointer duration-200 ease-in-out hover:opacity-75 ">
                  <img src="../../../assets/icons/close-icon.png" alt="" class="w-6">
                  <span class="text-accent ">{{"Close" | translate}}</span>
              </button>
          </div>
          <div>
              <p class="text-black text-sm font-semibold font-oxygen py-3">{{"Must Read" | translate}}</p>
              <p [innerHTML]="TOS"></p>
          </div>



          <div class="py-6">
              <button (click)="OpenTOSModel()"
                  class="w-5/6 bg-primary hover:bg-blue-900 text-white p-2 mx-6  lg:mx-5 xl:mx-6 2xl:mx-8 rounded-sm">
                  {{"Yes I Agree" | translate}}</button>
          </div>


      </div>
  </div>
</div>