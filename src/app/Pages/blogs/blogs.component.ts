import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { BlogsService } from 'src/app/Services/Blogs/blogs.service';
import { TranslateService } from '@ngx-translate/core';


BlogsService
@Component({
  selector: 'app-blogs',
  templateUrl: './blogs.component.html',
  styleUrls: ['./blogs.component.scss']
})
export class BlogsComponent implements OnInit {

  constructor(
    private blogsService: BlogsService,
    private toastr: ToastrService,
    private configSettings: configSettings,
    private http: HttpClient,
    private translateService: TranslateService,

  ) {
    this.configSettings.setShowLoader(true)
  }

  allBlogsArr: any

  // pagination
  p: number = 1;
  totalItems: any
  fromArticle: any
  toArticle: any
  itemsPerPage: any

  // search field
  searchInput: any = ''

  @ViewChild('searchBtn') searchBtnRef: ElementRef

  subscriptionEmail: string    //email for blog lates
  mailchimpEndpoint: string = 'https://bandarestate.us21.list-manage.com/subscribe/post-json?u=86d0e380ac78a9a42159f5384&id=7ac13498ab&MERGE0='


  ngOnInit(): void {
    this.configSettings.tiggerNotificationAPi()

    this.allBlogs(1)
  }


  allBlogs(pageTO: any) {
    this.configSettings.setShowLoader(true)
    const getParams = {
      page: pageTO,
      per_page: 6
    }

    const postParams = {
      country_id: "",
      search: this.searchInput
    }

    this.blogsService.blogsListing(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allBlogsArr = response.body.data.blogs
            this.totalItems = response.body.data.totalItemCount
            this.itemsPerPage = response.body.data.itemPerPage
            this.fromArticle = response.body.data.offset + 1
            this.toArticle = response.body.data.offset + this.allBlogsArr.length
            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  changePage(pageNumber: any) {
    this.allBlogs(pageNumber)
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' })
  }

  enterKeyWords(e: any) {
    if (e.key === "Enter") {
      this.searchBtnRef.nativeElement.click()
      e.preventDefault();
    }
  }


  validEmailRegEx = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

  subscribeForBlogs() {

    
    const mailChimpUrl = this.mailchimpEndpoint + this.subscriptionEmail;
console.log(this.subscriptionEmail);

    if(this.subscriptionEmail==='' || this.subscriptionEmail===undefined){
        this.translateService.get('Please enter email address').subscribe(res =>{
          this.toastr.error('', res);
        })
        return
      }

    if (this.subscriptionEmail.match(this.validEmailRegEx)) {
      this.http.jsonp<any>(mailChimpUrl, 'c').subscribe({
        next: (response) => {
          this.toastr.success('', response.msg)
          this.subscriptionEmail=''
        }, error: (err) => {
          this.toastr.error('', err.msg)
        }

      })
    } else {
      this.toastr.warning('', 'Enter a valid e-mail')
    }




  }
}
