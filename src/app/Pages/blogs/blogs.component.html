<div>
    <div class="bg-auto bg-no-repeat bg-center relative h-full text-black mb-16">
        <img src="../../../assets/images/blog-banner.png" class="w-full aspect-[2/1] md:aspect-[3/1] xl:aspect-[4/1]" alt="">
        <div class="container mx-auto px-4 sm:px-14 md:px-24 relative block h-0 bottom-36 md:bottom-[19rem] lg:bottom-96">
            <div class="md:py-20 lg:py-24 mx-auto md:px-0 xl:px-16">
                <p class="text-white text-4xl md:text-6xl  text-center font-semibold  pb-10 md:pb-16">{{"Blogs"| translate}}</p>
                <div class="grid grid-cols-12 gap-4 ">
                    <div class="col-span-8 lg:col-span-10">
                        <input [(ngModel)]="searchInput" type="text" placeholder="{{'Search Blog'| translate}}" (keydown)="enterKeyWords($event)"
                            class="w-full h-10  md:h-10 lg:h-14 rounded-lg px-5 py-4 text-lg text-black outline-none placeholder:text-sm md:placeholder:text-sm lg:placeholder:text-base">
                    </div>
                    <div class="col-span-4 lg:col-span-2">
                        <button (click)="allBlogs(p)" #searchBtn
                                class="rounded-md bg-accent h-10  md:h-10 lg:h-14 w-full text-white text-xl text-center flex justify-center items-center gap-1.5">
                            <img class="w-4 md:w-5  relative" src="../../../assets/icons/search.svg"><span
                                class="md:text-base  lg:text-xl">{{"Search"| translate}}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>


        

    </div>

    <div class="container mx-auto px-4 sm:px-14 md:px-24">
        <div class="container  mx-auto grid md:grid-cols-1 lg:grid-cols-2 gap-10  mb-8 lg:mb-20 xl:mb-20">
            <div *ngFor="let blog of allBlogsArr | paginate: { itemsPerPage: 5, currentPage: p,totalItems:totalItems }"
                [routerLink]="['/blog-details', blog.id ]">
                <app-blog [blog]="blog"></app-blog>
            </div>
        </div>



        <div class="container mx-auto  mb-24">
            <div class="flex justify-center gap-2 mb-2">
                <pagination-controls class="projects-pagination" (pageChange)="p = $event" (click)="changePage(p)">
                </pagination-controls>


            </div>
            <p class="text-xs text-center text-blue-500 space-x-0">{{fromArticle}} {{"to"| translate}} {{toArticle}}
                {{"of"| translate}} {{totalItems}} {{"Blogs"| translate}}</p>
        </div>

        <div class="container mx-auto mb-24 h-92 bg-secondary p-12">
            <p class="text-2xl text-white text-center mb-4">{{"Subscribe Blog For Latest Updates On Your Email"| translate}}</p>
            <p class="text-offwhite text-center mb-14">{{"Stay up to date and join out newsletter to receive the latest updates."| translate}}</p>
            <div class="flex gap-4 items-center md:w-full lg:w-3/5 mx-auto">
                <input [(ngModel)]="subscriptionEmail" name="MERGE0" id="MERGE0" type="email" placeholder="{{'Enter your email address'| translate}}"
                    class="h-10 xl:h-14 px-3 w-9/12 text-black outline-none text-xl placeholder:text-sm md:placeholder:text-sm lg:placeholder:text-base ">
                <button (click)="subscribeForBlogs()" type="button"
                    class="h-10 xl:h-14 w-3/12 bg-accent hover:bg-yellow-400 text-white duration-200  px-10  flex justify-center items-center space-x-1 md:text-base lg:text-xl">
                    {{"Subscribe"| translate}}
                </button>
            </div>
        </div>
    </div>

</div>