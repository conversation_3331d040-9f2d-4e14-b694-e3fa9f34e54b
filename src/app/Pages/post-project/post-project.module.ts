import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PostProjectRoutingModule } from './post-project-routing.module';
import { PostProjectFormModule } from '../../Components/post-project-form/post-project-form.module';
import { PostProjectComponent } from './post-project.component';
import { FeatureMyPostComponent } from '../../Components/feature-my-post/feature-my-post.component';
import { TranslateModule } from '@ngx-translate/core';
import { FeatureMyPostModule } from '../../Components/feature-my-post/feature-my-post.module';


@NgModule({
  declarations: [PostProjectComponent],
  imports: [
    CommonModule,
    PostProjectRoutingModule,
    PostProjectFormModule,
    FeatureMyPostModule
  ]
})
export class PostProjectModule { }
