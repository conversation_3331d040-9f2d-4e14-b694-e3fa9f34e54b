<section *ngIf="homeApiResponse !== ''" class="text-white ">
    <div *ngIf="banners?.length > 0" class="relative z-10">
        <div class=" ">
            <owl-carousel-o [options]="bannerOptions" #searchOwl class="bannerCarousal">
                <ng-container *ngFor="let banner of banners">
                    <ng-template carouselSlide [id]="banner.id">
                        <img class="z-10" [src]="banner.banner_web" [title]="banner.title"
                            (click)="bannerNavigation(banner.target,banner.title,banner.target_item)">
                        <!-- <div
                                class="font-normal text-3xl sm:text-4xl md:text-[3rem] lg:text-5xl w-2/5   xl:text-[5.5rem] tracking-wide absolute ltr:left-[10%] rtl:right-[15%] ltr:sm:left-[20%] rtl:sm:right-[25%] ltr:md:left-[18%] ltr:xl:left-[10%] ltr:2xl:left-[15%] rtl:md:right-[25%]  rtl:xl:right-[14%] rtl:2xl:right-[18%] top-1/2  md:top-[42%] -translate-y-1/2  lg:top-1/2 xl:top-[25%] z-[11]">
                                {{banner.title}}
                            </div> -->
                        <div
                            class="font-normal text-3xl sm:text-4xl md:text-[3rem] lg:text-5xl    xl:text-[5.5rem] tracking-wide absolute top-1/2 lg:top-[40%] left-1/2 -translate-x-1/2 -translate-y-1/2 z-[11]">
                            <p class="text-center">
                                {{banner.title}}
                            </p>
                        </div>
                    </ng-template>
                </ng-container>
            </owl-carousel-o>
        </div>
        <p (click)="searchOwl.prev()"
            class="hover:cursor-pointer z-30 absolute top-1/2 lg:top-[40%] hidden md:block left-12 hover:scale-105"><img
                class="h-7 w-7" src="../../../assets/icons/arrow-semi-left.svg"></p>
        <p (click)="searchOwl.next()"
            class="hover:cursor-pointer z-30 absolute top-1/2 lg:top-[40%] hidden md:block right-12 hover:scale-105">
            <img class="h-7 w-7" src="../../../assets/icons/arrow-semi-right.svg"></p>

        <div class="absolute inset-0 overlay banner-image hidden md:block"></div>

    </div>
    <!-- search menu -->
    <div class="relative z-10">
        <div
            class=" absolute lg:mt-0 mt-10 top-0 -translate-y-1/4 md:-translate-y-1/2  w-full lg:left-1/2 lg:-translate-x-1/2  mx-auto container px-4 sm:px-14 md:px-24 lg:px-12 xl:px-24">
            <div class="flex gap-2  lg:px-0">
                <div id="S" (click)="changeTab($event)"
                    class="tab tab1 active rounded-t-2xl w-full rtl:w-[10.5rem] lg:w-[9.5rem]  px-10 pb-3 pt-4  cursor-pointer text-lg text-center">
                    {{"Re-Sale" | translate}}</div>
                <div id="R" (click)="changeTab($event)"
                    class="tab tab2 rounded-t-2xl w-full rtl:w-[10.5rem] lg:w-[9.5rem] px-10 pb-3 pt-4  cursor-pointer text-lg text-center">
                    {{"Rent" | translate}}</div>
            </div>
            <div>
                <div class=" rounded-tr-none lg:rounded-tr-lg  rounded-lg ltr:rounded-tl-none rtl:rounded-tr-none shadow-xl bg-primary grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 justify-center
                        items-end gap-2 md:gap-3 gap-y-4  lg:gap-2 xl:gap-4 py-10 lg:py-16 px-4 xl:px-14">
                    <div class="w-full  relative  lg:px-0">
                        <p class="mb-2 tracking-[0.5px] ">{{"Country" | translate}}</p>
                        <span class=" downarrow z-[2]">
                            <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                                class="rtl:left-2.5 rtl:right-[unset]"></span>
                        <ng-select class="home-dropdown common-custom-styles " (change)="getStates()"
                            placeholder="{{'Select country' | translate}}" [(ngModel)]="countryID">
                            <ng-option *ngFor="let country of countries" [value]="country.id">
                                {{country.name}}
                            </ng-option>
                        </ng-select>
                    </div>
                    <div class="w-full  relative  lg:px-0">
                        <p class="mb-2 tracking-[0.5px] ">{{"State" | translate}}</p>
                        <span class="downarrow z-[2]">
                            <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                                class="rtl:left-2.5 rtl:right-[unset]"></span>
                        <ng-select class="home-dropdown common-custom-styles " (change)="getAreas()"
                            placeholder="{{'Select state' | translate}}" [(ngModel)]="stateID">
                            <ng-option *ngFor="let state of allStates" [value]="state.id">{{state.name}}
                            </ng-option>
                        </ng-select>
                    </div>
                    <div class="w-full  relative  lg:px-0">
                        <p class="mb-2 tracking-[0.5px]">{{"Area" | translate}}</p>
                        <span class=" downarrow  z-[2]">
                            <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                                class="rtl:left-2.5 rtl:right-[unset]">
                        </span>
                        <ng-select class="home-dropdown common-custom-styles"
                            placeholder="{{'Select area' | translate}}" [(ngModel)]="areaID">
                            <ng-option *ngFor="let area of allAreas"   [value]="area.id">{{area.name}}
                            </ng-option>
                        </ng-select>
                    </div>
                    
                    <div class=" w-full">
                        <p class="mb-2 tracking-[0.5px] ">{{"Category Type" | translate}}</p>
                        <span class="downarrow z-[2]">
                            <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                                class="rtl:left-2.5 top-0 rtl:right-[unset]"></span>
                        <ng-select placeholder="{{'Choose type' | translate }}" [(ngModel)]="categoryTypeID"
                            class="home-dropdown common-custom-styles">
                            <ng-option *ngFor="let item of propertyCategory" [value]="item.id">{{item.name}}
                            </ng-option>
                        </ng-select>
                    </div>
                    <div class="col-span-2 md:col-span-1 w-full">
                        <p class="mb-2 tracking-[0.5px] ">{{"Price Range" | translate}}</p>
                        <span class="downarrow  z-[2]">
                            <img src="../../../assets/icons/keyboard-arrow-down.svg" alt=""
                                class="rtl:left-2.5 top-0 rtl:right-[unset]"></span>
                        <ng-select placeholder="{{'Select range' | translate}}" (change)="getPriceRange($event)"
                            class="home-dropdown  z-[1]" [(ngModel)]="priceRangeID">
                            <ng-option *ngFor="let item of prices" [value]="item.id">{{item.name}}</ng-option>
                        </ng-select>

                    </div>

                    <div class=" col-span-2 md:col-span-1    flex-grow  mt-3 md:mt-0 lg:px-0 ">
                        <button (click)="search()"
                            class=" w-full h-14 bg-accent hover:bg-accentDark duration-150  px-9 py-4 rounded-md flex justify-center items-center gap-2 space-x-1 ">
                            <img class="h-6 w-6" src="../../../assets/icons/search.svg">
                            <span class="text-xl">{{"Search" | translate}}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- your last searches section -->

    <div *ngIf="lastSearches?.length === 0" class=" md:mb-10 mt-[29rem] md:mt-64 lg:mt-40 lg:mb-14 "></div>

    <div *ngIf="lastSearches?.length >0" id="" class="mb-10 mt-[31rem] md:mb-16 md:mt-72 lg:mt-52 lg:mb-14 container mx-auto">
        <p class="text-2xl md:text-3xl lg:text-4xl font-extrabold tracking-wider text-center uppercase lg:mb-14">
            {{"Your last searches" | translate}}
        </p>

        <!-- searches -->
        <div class="container grid lg:grid-cols-3 mx-auto mb-10 lg:mb-24 px-12 sm:px-14 md:px-24 gap-7">
            <div *ngFor="let lastSearch of lastSearches">
                <app-last-search [lastSearch]="lastSearch"></app-last-search>
            </div>
        </div>
    </div>

    <!-- carousal -->
    <!-- <div *ngIf="areaGuides.length > 0"
        class="justify-center items-center relative container mx-auto px-4 sm:px-14 md:px-24">
        <div  class=" mt-14 md:mt-28 flex md:hidden  justify-between items-center">
            <p class=" text-2xl md:text-3xl lg:text-4xl mb-0 xl:mb-6 lg:mb-6 font-bold tracking-wide uppercase">
                {{"Area Guides" | translate}}</p>
            <div class="flex rtl:flex-row-reverse items-center gap-4">
                <img src="../../../assets/icons/arrow-semi-left.svg"
                    class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="homeAds.prev()">
                <img src="../../../assets/icons/arrow-semi-right.svg"
                    class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="homeAds.next()">
            </div>
        </div>
        <div class="mt-10 md:mt-0">
            <owl-carousel-o [options]="customOptions" #homeAds class="searchCarousal "> -->
                
                <!-- <ng-template carouselSlide><img src="../../../assets/images/temp-carousal-imgs/2.jpg"
                        class="aspect-square object-cover" alt=""></ng-template> -->
                <!-- <ng-container *ngFor="let areaGuide of areaGuides">
                    <ng-template carouselSlide [id]="areaGuide.id">
                        <img [routerLink]="['/area-guides/' + areaGuide.id ]"
                            class="aspect-square object-cover  mr-auto cursor-pointer" [src]="areaGuide.background">
                    </ng-template>
                </ng-container>
            </owl-carousel-o>
        </div>

        <p (click)="homeAds.prev()" class="hidden md:block hover:cursor-pointer absolute top-1/2 left-12 hover:scale-105"><img
                class="h-7 w-7" src="../../../assets/icons/arrow-left.png"></p>
        <p (click)="homeAds.next()" class="hidden md:block hover:cursor-pointer absolute top-1/2 right-12 hover:scale-105"><img
                class="h-7 w-7" src="../../../assets/icons/arrow-right.png"></p>
    </div> -->
    

    <!-- ---------------------------------------------------------- -->

    <!-- featured property -->
    <div *ngIf="featuredProperties.length >0" class="container mx-auto px-4 sm:px-14 md:px-24">
        <div id="" class="mt-14 md:mt-28 flex justify-between items-center">
            <p class=" text-2xl md:text-3xl lg:text-4xl mb-0 xl:mb-6 lg:mb-6 font-bold tracking-wide uppercase">
                {{"featured property" |
                translate}}</p>
            <div class="flex rtl:flex-row-reverse items-center gap-5">
                <img src="../../../assets/icons/arrow-semi-left.svg"
                    class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="featuredProperty.prev()">
                <img src="../../../assets/icons/arrow-semi-right.svg"
                    class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="featuredProperty.next()">
            </div>
        </div>
        <div class="">
            <owl-carousel-o [options]="featuredPropertyOptions" #featuredProperty class="featuredProperty">
                <ng-container *ngFor="let featuredProperty of featuredProperties">
                    <ng-template carouselSlide>
                        <app-featured-property-card [featuredProperty]='featuredProperty'></app-featured-property-card>
                    </ng-template>
                </ng-container>
            </owl-carousel-o>
        </div>
    </div>

    <!-- featured architects -->
    <div class=" mb-8 lg:mb-24 container mx-auto px-4 sm:px-14 md:px-24">
        <div id="" class="mt-4 md:mt-14 flex justify-between items-center mb-12">
            <p class="text-2xl md:text-3xl lg:text-4xl font-bold tracking-wide uppercase">{{"featured projects" |
                translate}}
            </p>
            <div class="flex rtl:flex-row-reverse items-center gap-4">
                <img src="../../../assets/icons/arrow-semi-left.svg"
                    class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="featuredArchitectsCar.prev()">
                <img src="../../../assets/icons/arrow-semi-right.svg"
                    class="h-6 w-6 hover:cursor-pointer hover:scale-105" (click)="featuredArchitectsCar.next()">
            </div>
        </div>
        <div class="mx-auto">
            <owl-carousel-o [options]="featuredPropertyOptions" #featuredArchitectsCar class="featuredArchitects">
                <ng-container *ngFor="let featuredProject of featuredProjects">
                    <ng-template carouselSlide>
                        <app-featured-architects-card [featuredProject]='featuredProject'>
                        </app-featured-architects-card>
                    </ng-template>
                </ng-container>

            </owl-carousel-o>
        </div>
    </div>

    <!-- Years of experience -->
    <div class="mb-8 lg:mb-24 mt-14 container mx-auto px-4 sm:px-14 md:px-24">
        <div class="mb-10 2xl:mb-16">
            <h1 class=" text-2xl md:text-3xl lg:text-5xl text-center tracking-wide font-bold mb-4">
                {{"Years Of Experience" | translate}}</h1>
            <h2 class=" text-2xl md:text-3xl lg:text-5xl text-center tracking-wider">{{"We Are Ready To Help You" |
                translate}}
            </h2>
        </div>
        <div class="flex flex-col xl:flex-row items-center gap-14 mt-5 2xl:mt-9 mx-auto">
            <img [src]="yearsOfExperience" class="rounded-xl lg:col-span-3 w-full xl:w-[55%] aspect-[16/12]">
            <div class="self-center w-full xl:w-[45%]">
                <p class=" text-2xl md:text-2xl lg:text-5xl font-bold mb-10 text-start md:text-start">{{"Our services"
                    |
                    translate|
                    uppercase}}</p>
                <div class="flex items-center gap-8 mb-7">
                    <div
                        class="shrink-0 h-20 w-20 md:h-16 md:w-24 lg:w-16 lg:h-16 xl:h-[4.5rem] xl:w-[4.5rem] 2xl:h-24 2xl:w-24 border border-neutral rounded-full flex justify-center items-center">
                        <img src="../../../assets/icons/map-view.svg" class="h-6 w-6 2xl:h-14 2xl:w-14">
                    </div>
                    <div>
                        <p class="text-base lg:text-xl 2xl:text-2xl mb-2 font-normal">{{"Map View" | translate}}</p>
                        <p class="text-grayCaption text-sm">{{"Search for properties in preferred areas using a map" |
                            translate}}
                        </p>
                    </div>
                </div>
                <div class="flex items-center gap-8 mb-7">
                    <div
                        class="shrink-0 h-20 w-20 md:h-16 md:w-24 lg:w-16 lg:h-16 xl:h-[4.5rem] xl:w-[4.5rem] 2xl:h-24 2xl:w-24 border border-neutral rounded-full flex justify-center items-center">
                        <img src="../../../assets/icons/guides.svg" class="h-6 w-6 2xl:h-14 2xl:w-14">
                    </div>
                    <div>
                        <p class="text-base lg:text-xl 2xl:text-2xl mb-2 font-normal">{{"Guides" | translate}}</p>
                        <p class="text-grayCaption text-sm">
                            {{"We can make your buying process easier by providing you up-to-date, step-by-step and
                            country specific information" | translate}}
                        </p>
                    </div>
                </div>
                <div class="flex items-center gap-8 mb-7">
                    <div
                        class="shrink-0 h-20 w-20 md:h-16 md:w-24 lg:w-16 lg:h-16 xl:h-[4.5rem] xl:w-[4.5rem] 2xl:h-24 2xl:w-24 border border-neutral rounded-full flex justify-center items-center">
                        <img src="../../../assets/icons/search-services.svg" class="h-6 w-6 2xl:h-10 2xl:w-10">
                    </div>
                    <div>
                        <p class="text-base lg:text-xl 2xl:text-2xl mb-2 font-normal">{{"Saved Searches" | translate}}
                        </p>
                        <p class="text-grayCaption text-sm">{{"Checkout all the properties you searched for in the past"
                            | translate}}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- BLOG -->
    <div *ngIf="blogs.length > 0" class="mb-8 md:mb-16 container mx-auto px-4 sm:px-14 md:px-24">
        <h1 class="blog text-2xl md:text-3xl lg:text-[2.75rem] font-bold tracking-wide text-center mb-6 relative"><span
                class="z-[3] relative bg-primary px-5 ">{{"blog" | translate | uppercase}}</span></h1>
        <p class="text-base text-center mb-10 2xl:mb-16 text-gray-300">
            {{"Everything you need to know about the market, explained by our experts" | translate}}</p>
        <div class="hidden  lg:grid grid-cols-1 lg:grid-cols-3 space-y-4 lg:space-y-0 gap-7">
            <div *ngFor="let blogDetail of blogs">
                <app-blog-card [blogDetail]='blogDetail'></app-blog-card>
            </div>
        </div>
         
        <div class="mx-auto relative block lg:hidden">
            <owl-carousel-o [options]="blogsOptions" #blogscard  class="featuredArchitects">
                <ng-container *ngFor="let blogDetail of blogs">
                    <ng-template carouselSlide>
                        <app-blog-card [blogDetail]='blogDetail'>
                        </app-blog-card>
                    </ng-template>
                </ng-container>
            </owl-carousel-o>
            <!-- <p (click)="blogscard.prev()" class="block z-10 lg:hidden hover:cursor-pointer absolute top-1/3 md:top-[40%] left-2 bg-white w-9 h-9 rounded-full hover:scale-105"><img
                class="h-7 w-7 my-1 mx-1" src="../../../assets/icons/semi-left-black.png"></p>
        <p (click)="blogscard.next()" class="block z-10  lg:hidden hover:cursor-pointer absolute top-1/3 md:top-[40%] right-2 bg-white w-9 h-9 rounded-full  hover:scale-105"><img
                class="h-7 w-7 my-1 mx-1" src="../../../assets/icons/semi-right-black.png"></p> -->
        </div>
       
    </div>

    <!-- explore more section -->
    <div class="mb-8 lg:mb-24 container mx-auto px-4 sm:px-14 md:px-24">
        <button [routerLink]="['/blogs']"
            class="font-bold text-[15px] flex items-center gap-3 mx-auto border border-offwhite text-white px-6 py-4 hover:border-accent hover:bg-accent hover:text-white duration-300 ease-in-out uppercase">
            {{"Explore more" | translate}} <span><img src="../../../assets/icons/arrow-right.png"
                    class="h-4 w-4 rtl:rotate-180"></span>
        </button>
    </div>








</section>
<section *ngIf="homeApiResponse === ''" class="m-3 h-vh100 bg-primary"></section>