.tab {
    background-color: white;
    color: #002349;
}

.tab.active {
    background-color: #002349;
    color: white;
}
@media (min-width: 640px){
 .search{
    width: 304px;
 }
    
}

// line through a text
.blog::before {
    content: '';
    position: absolute;
    display: block;
    top: 50%;
    height: 1px;
    width: 80%;
    background: #a8aeb99d;
    left: 50%;
    z-index: 1;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
}

.banner-image {
    // background: linear-gradient(180deg, rgba(0, 35, 73, 0.2) 0%, rgba(0, 35, 73, 0.2) 100%), url("../../../assets/images/home-page-banner.jpg");
    // background-image: url("../../../assets/images/home-page-banner.jpg");
    background: linear-gradient(180deg, rgba(0, 35, 73, 0.2) 0%, rgba(0, 35, 73, 0.2) 100%);
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.9) 100%);
    // background-color: #002349;
    height: 50vh;
    background-size: cover;
}
.overlay{
    background: linear-gradient(180deg, rgba(0, 35, 73, 0.4) 0%, rgba(0, 35, 73, 0.4) 100%);
    // background: linear-gradient(180deg, rgba(247, 0, 0, 0.993) 0%, rgb(255, 0, 0) 100%);
}

.ng-select .ng-clear-wrapper {
    right: 16px !important;
    width: 10px !important;
 }