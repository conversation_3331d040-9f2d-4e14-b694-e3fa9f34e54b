import { Component, ElementRef, OnDestroy, OnInit, Renderer2, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { configSettings } from 'src/app/Config/config.settings';
import { HomeService } from 'src/app/Services/home.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit, OnDestroy {
  userDetails: any;
  userID: any;
  constructor(
    public router: Router,
    private renderer: Renderer2,
    private homeservice: HomeService,
    private toastr: ToastrService,
    private configSettings: configSettings
  ) {
    this.configSettings.setShowLoader(true)
    document.body.style.overflow = "hidden";

  }
  lang?: any
  isRTL: boolean = false
  homeResponse?: Subscription
  featuredProperties: any
  featuredProjects: any
  blogs: any
  areaGuides: any;
  lastSearches: any
  homeApiResponse: any = ''
  prices = [
    {
      id: '100 - 1000',
      name: '100 - 1000'
    },
    {
      id: '1001-2000',
      name: '1001-2000'
    },
    {
      id: '2001 - 3000',
      name: '2001 - 3000'
    },
    {
      id: '3001 - 4000',
      name: '3001 - 4000'
    },
    {
      id: '4001 - 5000',
      name: '4001 - 5000'
    },
    {
      id: '5001 - 6000',
      name: '5001 - 6000'
    },
    {
      id: '6001 - 99999999',
      name: '6001 and above'
    },
  ]

  propertyType: any
  banners: any

  featuredArchitectsArr: any = ["../../../assets/images/temp-carousal-imgs/1.jpg", "../../../assets/images/temp-carousal-imgs/2.jpg", "../../../assets/images/temp-carousal-imgs/3.jpg", "../../../assets/images/temp-carousal-imgs/4.jpg", "../../../assets/images/temp-carousal-imgs/5.jpg", "../../../assets/images/temp-carousal-imgs/6.jpg",]

  propertyTypeID = ''
  priceRangeID: any
  locationString: any
  categoryTypeID: any

  propertyCategory: any

  allStates: any
  stateID: any

  countries: any
  countryID: number = 114

  allAreas: any
  areaID: number | null

  ngOnInit(): void {
    this.configSettings.tiggerNotificationAPi()
    this.userID = this.configSettings.getUserID() ? this.configSettings.getUserID() : ''
    this.getHomeDetails()
    this.isRTL = true
    this.lang = this.configSettings.getLang()
    if (this.lang === 'ar') {
      this.isRTL = true
      // console.log("yes")
    }
    // console.log(this.lang, this.isRTL)

    // this.getBanners()

    window.scroll({
      top: 0,
      left: 0,
    });
  }

  propertFor: string = "S"
  changeTab(event: any) {
    Array.from(document.querySelectorAll('.tab')).forEach(function (el) {
      el.classList.remove('active');
    });
    this.renderer.addClass(event.target, 'active')
    if (event.target.id !== this.propertFor) {
      this.propertFor = event.target.id
    }

  }

  customOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    rtl: false,
    autoHeight: true,
    autoWidth: true,
    center: false,
    margin: 30,
    responsive: {
      0: {
        items: 1,
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 3,
      },
      1280: {
        items: 3,
      },
      1536: {
        items: 3
      }
    },
    // nav: true
  }

  featuredPropertyOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    autoWidth: false,
    margin: 30,
    center: false,
    rtl: false,
    responsive: {
      0: {
        items: 1
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 3
      },
      1280: {
        items: 3
      },
      1536: {
        items: 3
      }
    },
    nav: false
  }

  blogsOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: true,
    lazyLoad: false,
    navSpeed: 700,
    autoWidth: false,
    margin: 30,
    center: false,
    rtl: false,
    responsive: {
      0: {
        items: 1
      },
      640: {
        items: 2
      },
      768: {
        items: 2
      },
      896: {
        items: 2
      },
      1024: {
        items: 3
      },
      1280: {
        items: 3
      },
      1536: {
        items: 3
      }
    },
    nav: false
  }

  owlCaro = document.querySelector('.search-Caro')

  bannerOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: true,
    navSpeed: 700,
    autoWidth: false,
    center: false,
    rtl: this.isRTL,
    autoplay: true,
    autoplayTimeout: 5000,
    autoplayHoverPause: false,
    responsive: {
      0: {
        items: 1
      },
    },
    nav: false
  }

  currentPage = 1;
  itemsPerPage = 15;
  maxSize = 5;
  array = [1, 2, 3, 4, 5, 6, 7]
  yearsOfExperience: any
  getHomeDetails() {

    const getParams = {
      user_id: this.userID
    }

    this.homeResponse = this.homeservice.home(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.homeApiResponse = response.body.data
            this.banners = response.body.data.banners
            this.yearsOfExperience = response.body.data.year_of_experience_banner
            this.featuredProjects = response.body.data.featured_project
            this.featuredProperties = response.body.data.featured_properties
            this.propertyCategory = response.body.data.property_categories
            this.areaGuides = response.body.data.area_guides
            this.blogs = response.body.data.blogs
            // this.allStates = response.body.data.allStates
            this.lastSearches = response.body.data.last_searches
            // this.configSettings.setWhatsAppNo(this..settings.support_number)
            this.configSettings.setShowLoader(false)
            document.body.style.overflow = "auto";
            this.getCountryAPi()
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


  // getBanners() {
  //   const getParams = {}

  //   this.homeservice.getBanners(getParams).subscribe({
  //     next: (response) => {
  //       if (response.status === 200) {
  //         if (response.body.status == 200) {
  //           this.banners = response.body.data
  //         }
  //       }
  //     },
  //     error: (err) => {
  //       this.toastr.error(err)
  //     }
  //   })
  // }


  search() {



    this.configSettings.setSearchType(1)
    this.router.navigate(['/property-listing'], {
      queryParams: {
        propertyFor: this.propertFor, countryID: this.countryID, stateID: this.stateID, areaID: this.areaID,
        categoryType: this.categoryTypeID, min: this.minPrice, max: this.maxPrice
      }
    })


  }

  getPropertyType(e: any) {
    this.propertyTypeID = e
  }

  minPrice: any
  maxPrice: any
  getPriceRange(e: any) {
    this.priceRangeID = e
    this.minPrice = this.priceRangeID.split(' - ')[0]
    this.maxPrice = this.priceRangeID.split(' - ')[1]
  }





  ngOnDestroy(): void {
    this.homeResponse?.unsubscribe();
  }


  getCountryAPi() {


    this.homeservice.getallCountryApi({}).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.countries = response.body.data
            this.getStates()
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  getStates() {
    this.stateID = null

    const getParams = {
      country_id: this.countryID
    }
    this.homeservice.getallCountryApi(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allStates = response.body.data

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  getAreas() {
    this.allAreas = []
    this.areaID = null
    const getParams = {
      state_id: this.stateID
    }
    this.homeservice.getallCountryApi(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.allAreas = response.body.data

          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }

  // bannerNavigation(){
  //   console.log('will navigate banner');

  // }

  bannerNavigation(bannerTarget: any, bannerTitle: any, bannerTarget_itemId: any) {
    if (bannerTarget == 'PR') {
      this.router.navigate(['/project-details', bannerTarget_itemId]);
    } else if (bannerTarget == 'EL') {
      window.open(bannerTarget_itemId, '_self');
    } else if (bannerTarget == 'PT') {
      this.router.navigate(['/property-details', bannerTarget_itemId]);
    } else if (bannerTarget == 'AR') {
      this.router.navigate(['/architect-details', bannerTarget_itemId])
    } else {
      return;
    }
  }



}
