import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HomeRoutingModule } from './home-routing.module';
import { HomeComponent } from './home.component';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { CustomDropdownModule } from 'src/app/Components/custom-dropdown/custom-dropdown.module';
import { LastSearchModule } from 'src/app/Components/last-search/last-search.module';
import { FeaturedPropertyCardComponent } from 'src/app/Components/featured-property-card/featured-property-card.component';
import { FeaturedPropertyCardModule } from 'src/app/Components/featured-property-card/featured-property-card.module';
import { BlogCardModule } from 'src/app/Components/blog-card/blog-card.module';
import { FeaturedArchitectsCardComponent } from 'src/app/Components/featured-architects-card/featured-architects-card.component';
import { FeaturedArchitectsCardModule } from 'src/app/Components/featured-architects-card/featured-architects-card.module';
import { FormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [HomeComponent],
  imports: [
    CommonModule,
    HomeRoutingModule,
    CarouselModule,
    CustomDropdownModule,
    LastSearchModule,
    FormsModule,
    FeaturedPropertyCardModule,
    BlogCardModule,
    FeaturedArchitectsCardModule,
    NgSelectModule,
    TranslateModule
  ]
})
export class HomeModule { }
