<section>
    <div class="container mx-auto sm:px-12  md:px-24 xl:px-24 py-16">
        <div *ngIf="agentArr.length > 0" class="grid grid-cols-12 gap-5 gap-y-14 px-2 ">
            <div class="col-span-12 lg:col-span-4 2xl:col-span-3 row-span-1 lg:row-span-2 row-start-3 lg:row-start-1">
                <div class="bg-white  rounded-md overflow-hidden lg:mb-2 ">
                    <div class="text-center bg-indigo-300  py-3 mb-2">
                        <h5 class="text-primary font-bold text-2xl">{{"Other Agencies"| translate}}</h5>
                    </div>
                    
                        <div [routerLink]="['/agent-profile/' + agent.id ]"
                            class="flex items-center py-4 px-3 gap-3 cursor-pointer" *ngFor="let agent of agentArr">
                            <div class=" h-14 w-14 border-slate-300 border rounded-full overflow-hidden flex-shrink-0">
                                <img src="{{agent?.company_logo}}" alt="" class="object-cover aspect-square  h-16 w-16">
                            </div>
                            <div class="col-span-3 py-4 lg:pt-0  xl:py-0">
                                <h6 class="text-primary text-lg font-semibold">
                                    {{agent?.name}}
                                </h6>
                                <!-- <p class="text-[.8rem]  text-primary pr-2">{{agent?.address}}</p> -->
                                <p class="text-[.8rem]  text-black pr-2">{{agent.address}}</p>
                            </div>

                        </div>
                    

                </div>

            </div>
            <div class="col-span-12 lg:col-span-8 2xl:col-span-9 row-span-1 row-start-1 lg:row-start-1">
                <div class=" bg-white p-4 md:p-6 rounded-md">
                    <div class="grid grid-cols-2 ">
                        <div class=" col-span-2 lg:col-span-1 justify-center">
                            <img [src]="agentDetails?.company_logo" class="h-60 w-full object-cover" alt="">
                            <div
                                class="flex gap-5 py-4 bg-amber-400 justify-evenly md:justify-center rounded-lg mx-14 sm:mx-20 md:mx-32  lg:mx-6 xl:mx-12 2xl:mx-20 relative bottom-7">
                                <a *ngIf="agentDetails.email" href="mailto:{{agentDetails?.email}}" class=""><img
                                        src="../../../assets/icons/email-icon.png" alt=""
                                        class="w-6 h-6 cursor-pointer"></a>
                                <a *ngIf="agentDetails.phone" href="tel:{{agentDetails?.phone}}" class=""> <img
                                        src="../../../assets/icons/contact-icon.png" alt=""
                                        class="w-7 h-6 cursor-pointer"></a>
                                <a *ngIf="agentDetails.whatsapp"
                                    href="https://api.whatsapp.com/send?phone={{agentDetails?.whatsapp}}"
                                    target="_blank" class="">
                                    <img src="../../../assets/icons/whatsapp-icon.png" alt=""
                                        class="w-6 h-6 cursor-pointer">
                                </a>
                                <!-- [routerLink]="['/my-account/chats/' + agentID]" -->
                                <a *ngIf="agentID != userID" (click)="routeTochat()">
                                    <img src="../../../assets/icons/messages-icon.png" alt=""
                                        class="w-6 h-6 cursor-pointer">
                                </a>
                            </div>
                        </div>
                        <div class="col-span-2 lg:col-span-1 pt-2 pb-8 lg:p-8">
                            <div class="pb-2">
                                <h5 class="text-primary text-2xl font-semibold">
                                    {{agentDetails.name}}</h5>
                                <!-- <span class="text-sm text-gray-400">{{"Agent"| translate}}</span> -->
                            </div>
                            <div class="flex pb-8">
                                <span class="pr-2 pt-1">
                                    <img src="../../../assets/icons/placeholder-filled-point.png" alt="" class="w-8">
                                </span>
                                <p class="text-sm text-primary font-medium pr-2">{{agentDetails.address}}</p>
                            </div>
                            <div
                                class=" bg-[#dde5ee] lg:w-full flex text-xs lg:text-[10px] xl:text-xs justify-center text-center py-3 text-[#2979cf] mx-auto border border-[#c3d7ed]">
                                <button class="font-semibold " (click)="saleType();getAgentType()" [ngClass]=" !rent && sale? 'text-primary':''">{{numberOfSale}} {{"Re-Sale properties"
                                    |translate}}</button>
                                <span class="px-3 lg:px-1 2xl:px-3 text-gray-300 text-md">|</span>

                                <button class="font-semibold " (click)="rentType();getAgentType()" [ngClass]="rent && !sale? 'text-primary':''">{{numberOfRent}} {{"Rent properties" |
                                    translate}}</button>

                            </div>

                        </div>
                    </div>
                    <div *ngIf="agentDetails.about">
                        <h5 class="text-primary text-2xl md:text-xl font-semibold pb-4">{{"About The Agent" |
                            translate}} </h5>
                        <p [ngClass]="{'h-20 overflow-hidden': isReadMore}"
                            class="pb-2 text-sm text-mediumGray font-medium h-full">{{agentDetails.about}}</p>
                        <div class="w-fit my-2 mx-auto block lg:hidden">
                            <button *ngIf="isReadMore == true" type="button" (click)="showText()"
                                class="px-8 rounded-full text-xs text-amber-400 py-1.5 border-2 border-amber-300">
                                Read More
                                <span></span>
                            </button>
                            <button *ngIf="isReadMore == false" type="button" (click)="showText()"
                                class=" px-8 rounded-full text-xs text-amber-400 py-1.5 border-2 border-amber-300">
                                Read Less
                            </button>
                            <span class="w-fit relative bottom-5 mx-4 block h-0 rtl:right-[70%] ltr:left-[70%]">
                                <img class="w-3" [ngClass]="isReadMore == false ? '-scale-y-100' : ''"
                                    src="../../../assets/icons/gold-down arrow.svg" alt="">
                            </span>
                        </div>
                    </div>

                </div>
            </div>

            <!-- <div class="hidden lg:block lg:col-span-3"></div> -->
            <div class="col-span-12 lg:col-span-8 2xl:col-span-9  rounded-md row-span-1 row-start-2" *ngIf="numberOfRent>0 ||numberOfSale>0">
                <h4 class=" text-white text-3xl font-semibold mb-7 px-2">{{"Agency Property Listed"| translate}}</h4>
                <div class="grid grid-cols-12 md:gap-4">

                    <div class="mx-1.5 py-2  col-span-6 lg:col-span-4" *ngFor="let property of propertyTypeData" >
                        <app-property-card [property]="property" ></app-property-card>
                    </div>

                </div>



            </div>
        </div>
    </div>
</section>