import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { AgentService } from 'src/app/Services/Agent/agent.service';
import { PropertyListingService } from 'src/app/Services/Properties/property-listing.service';

@Component({
  selector: 'app-agent-profile',
  templateUrl: './agent-profile.component.html',
  styleUrls: ['./agent-profile.component.scss']
})
export class AgentProfileComponent implements OnInit {

  constructor(
    private toastr: ToastrService,
    private router: Router,
    private agentService: AgentService,
    private configSettings: configSettings,
    private propertyListingService: PropertyListingService,
    private activatedRoute: ActivatedRoute,
  ) { }


  user: any
  userId: any
  agentDataObj: any
  propertiesArr: any = []
  agentArr: any = []
  total_properties: number = 0;
  total_pages: number = 0;
  property_for: any;

  agentID: any
  agentDetails: any

  numberOfRent: number = 0
  numberOfSale: number = 0
  rentProperty: any;
  sellProperty: any;

  propertyType: any;
  propertyTypeData: any;
  rent = true;
  sale = true;

  userID = this.configSettings.getUserID()

  ngOnInit(): void {
    this.user = this.configSettings.getUserDetails();
    this.userId = this.configSettings.getUserDetails() ? this.configSettings.getUserDetails().user_id : ''
    // this.agentDataObj = this.configSettings.getAgentDetails()
    this.activatedRoute.params.subscribe(params => {
      this.configSettings.setShowLoader(true)
      this.agentID = +params.id
      this.getAgentListing();
    })
  }


  // property listing 
  propertyListing() {
    this.numberOfRent = 0
    this.numberOfSale = 0

    const getParams = {
      // lang: this.lang,
      page: 1,
      per_page: 6,
    };
    const postParams = {
      user_id: this.userId,
      agent_id: this.agentID,
    };
    this.propertyListingService
      .postProperties(getParams, postParams)
      .subscribe({
        next: (response) => {
          if (response.status === 200) {
            if (response.body.status == 200) {
              this.propertiesArr = response?.body?.data?.properties;
              this.total_pages = response?.body?.data?.total_pages;
              this.total_properties = response?.body?.data?.total_properties

              this.propertiesArr.forEach((property: any) => {
                if (property.property_for === 'R') {
                  this.numberOfRent++
                } else if (property.property_for === 'S') {
                  this.numberOfSale++;
                }
              });
              this.propertyType = this.propertiesArr
              this.getAgentType()



              this.configSettings.setShowLoader(false)
            } else {
              this.toastr.warning('', response.body.message);
            }
          } else {
            this.toastr.error('', 'Something went wrong');
          }
        },
        error: (err) => {
          this.toastr.error('', err.error.message);
        }
      })
  }
  getAgentType() {
    this.rentProperty = this.propertyType.filter((rent: any) => {
      return rent.property_for == 'R'
    });
    this.sellProperty = this.propertyType.filter((sell: any) => {
      return sell.property_for == 'S'
    });
    if (this.rent == true && this.sale == true) {
      this.propertyTypeData = this.propertyType
    }
    else if (this.rent == true && this.sale == false) {
      this.propertyTypeData = this.rentProperty
    }
    else if (this.rent == false && this.sale == true) {
      this.propertyTypeData = this.sellProperty;
    }
  }



  rentType() {
    this.rent = true;
    this.sale = false;

  }
  saleType() {
    this.rent = false;
    this.sale = true;
  }



  getAgentListing() {

    const getParams = {

    };
    this.agentService.agentListing(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {

            this.agentArr = response?.body?.data

            this.agentArr.forEach((agent: any) => {
              if (agent.id === this.agentID) {
                this.agentDetails = agent
              }
            });
            this.propertyListing()
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  ngOnDestroy() {
    this.configSettings.clearAgent()
  }
  userDetails: any = this.configSettings.getUserDetails()
  routeTochat() {
    if (this.userDetails) {
      this.router.navigate(['/my-account/chats/' + this.agentID])
    } else {
      this.router.navigate(['/login'])
    }
  }

  isReadMore = true

  showText() {
    this.isReadMore = !this.isReadMore
  }
}
