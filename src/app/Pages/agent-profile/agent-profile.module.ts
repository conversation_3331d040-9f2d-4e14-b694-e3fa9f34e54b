import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AgentProfileRoutingModule } from './agent-profile-routing.module';
import { AgentProfileComponent } from './agent-profile.component';
import { PropertyCardModule } from 'src/app/Components/property-card/property-card.module';
import { TranslateModule } from '@ngx-translate/core';


@NgModule({
  declarations: [AgentProfileComponent],
  imports: [
    CommonModule,
    PropertyCardModule,
    AgentProfileRoutingModule,
    TranslateModule
  ]
})
export class AgentProfileModule { }
