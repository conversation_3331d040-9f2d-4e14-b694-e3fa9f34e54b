<section *ngIf="projectDetails" class="text-white">
    <div class="min-h-vh50 lg:min-h-vh100 xl:min-h-vh100">
        <div class="bg-cover bg-no-repeat relative block h-0 bg-center ">
            <img class="blur-sm w-full h-vh10 xl:h-vh55 object-cover" [src]="projectDetails?.cover_photo" alt="">
        </div>
        <div class="container mx-auto px-4 sm:px-14 md:px-24 relative z-10">
            <div class="">
                <img class="w-full aspect-[5/2] object-cover" [src]="projectDetails?.cover_photo" alt="">
            </div>
            <div class="grid grid-cols-12 gap-4 px-2 ">
                <div class="col-span-12 md:col-span-12 xl:col-span-9">
                    <div class="">
                        <div
                            class="block relative h-0 bottom-28 sm:bottom-28 md:bottom-36 lg:bottom-40 xl:bottom-44 z-10 ">
                            <img [src]="projectDetails?.company_logo"
                                class="w-12 md:w-16 lg:w-24 h-12 md:h-16 lg:h-24 rounded-full" alt="">
                            <div class="flex flex-row space-x-4 mt-5">
                                <div class=" ">
                                    <button (click)="showGallery = true"
                                        class="items-center bg-white h-9 sm:h-9 lg:h-10 justify-items-center flex px-4  border-0 w-full text-primary md:text-sm lg:text-sm font-medium capitalize hover:bg-accent hover:text-white"><img
                                            class="lg:w-6 w-4 mr-4" src="assets/images/image-gallery.png" alt="">
                                        <a class="sm:pr-1 pr-2">{{"Gallery"| translate}}</a>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 gap-4 mt-6 border-b border-borderColor">
                            <div class="">
                                <h2 class="md:text-lg lg:text-3xl font-base text-white uppercase pb-4">{{title}}</h2>
                                <!-- <div class="grid grid-cols-3 gap-4 border-b border-borderColor pb-3.5"> -->
                                   
                                    <div _ngcontent-kqs-c119="" class="flex items-center gap-2 py-3"><img _ngcontent-kqs-c119="" src="../../../assets/icons/pin.png" class="w-3.5"><p _ngcontent-kqs-c119="" class="text-slate-300 text-sm lg:text-base">{{projectDetails?.country}}, {{projectDetails?.state}}, {{projectDetails?.area}}, {{projectDetails?.address}}</p></div>
                               
                            </div>
                        </div>
                        <div>
                            <h2 class="text-white font-bold text-base lg:text-3xl mb-4 mt-4">{{"Overview" | translate}}</h2>
                            <p class="text-slate-300 text-sm mb-4 lg:mb-16 " [innerHTML]="description">
                                {{description}}
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-span-3 hidden xl:block">
                    <div class="block relative bottom-16 z-10 ">
                       
                            <!-- [ngStyle]="{'background-image': 'url(' + projectDetails.cover_photo+ ')'}" -->
                            <div class="bg-white bg-center mt-2 p-5 mx-auto text-center cursor-pointer" >
                                <h3 class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl uppercase">
                                    {{"Architect" | translate}}</h3>
                                <img class="w-12 md:w-16 lg:w-24 aspect-square rounded-full mx-auto text-center block my-2 border border-black"
                                    [src]="projectDetails.company_logo" [routerLink]="['/architect-details/'+ projectDetails?.id]" alt="">
                                <span
                                    class="capitalize text-primary w-full text-sm mt-2 font-semibold text-center">
                                    {{projectDetails?.architect_name}}</span>
                                    <h4 _ngcontent-dwo-c74="" class="capitalize text-xs text-primary text-cente w-full mt-2 ng-star-inserted">{{ projectDetails?.address}}</h4>
                            </div>
                        </div>
                       
                        
                   
                </div>
                
            </div>
        </div>
    </div>
</section>

<div class="hidden xl:hidden lg:grid grid-cols-12 gap-4 px-2 ">

    <div class="block relative col-span-4  z-10 ml-12 mb-10">
       
            <!-- [ngStyle]="{'background-image': 'url(' + projectDetails.cover_photo+ ')'}" -->
            <div class="bg-white bg-center mt-2 p-5 mx-auto text-center cursor-pointer" >
                <h3 class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl uppercase">
                    {{"Architect" | translate}}</h3>
                <img class="w-12 md:w-16 lg:w-24 aspect-square rounded-full mx-auto text-center block my-2 border border-black"
                    [src]="projectDetails?.company_logo"  alt="">
                <span
                    class="capitalize text-primary w-full text-sm mt-2 font-semibold text-center">
                    {{projectDetails?.architect_name}}</span>
                    <h4 _ngcontent-dwo-c74="" class="capitalize text-xs text-primary text-cente w-full mt-2 ng-star-inserted">{{ projectDetails?.address}}</h4>
            </div>
        </div>
       
        
    </div>
<div class=" grid-cols-12 gap-4 px-2block hidden  mt-8">

    <div class="block relative col-span-12  z-10 mx-12 mb-10">
       
            <!-- [ngStyle]="{'background-image': 'url(' + projectDetails.cover_photo+ ')'}" -->
            <div class="bg-white bg-center mt-2 p-5 mx-auto text-center cursor-pointer" >
                <h3 class="text-primary text-center font-bold text-xs md:text-base lg:text-2xl uppercase">
                    {{"Architect" | translate}}</h3>
                <img class="w-12 md:w-16 lg:w-24 aspect-square rounded-full mx-auto text-center block my-2 border border-black"
                    [src]="projectDetails?.company_logo"  alt="">
                <span
                    class="capitalize text-primary w-full text-sm mt-2 font-semibold text-center">
                    {{projectDetails?.architect_name}}</span>
                    <h4 _ngcontent-dwo-c74="" class="capitalize text-xs text-primary text-cente w-full mt-2 ng-star-inserted">{{ projectDetails?.address}}</h4>
            </div>
        </div>
       
        
    </div>



<!-- <div *ngIf="showGallery" (click)="showGallery = false" class="bg-overlay fixed inset-0 z-[99]"></div>
<div *ngIf="showGallery && galleryMedia"
    class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-5 py-4 bg-white w-11/12 md:w-1/2 lg:w-2/3  xl:w-1/2  rounded-xl">
    <div>
        <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b">
            <h5 class="text-lg font-bold leading-normal ">{{"Gallery" | translate}}</h5>
            <button (click)="showGallery = false" type="button" class="flex gap-1 items-center hover:cursor-pointer ">
                <img src="../../../assets/icons/close-icon.png" alt="" class="w-4">
                <span
                    class="text-accent hover:text-accentDark duration-200 ease-in-out ">{{"Close" | translate}}</span>
            </button>
        </div>
        <div class="h-full">
            carousal
            <div class="justify-center items-center relative container ">

                <div class="h-full">
                    <owl-carousel-o [options]="galleryOptions" #gallery class="galleryCarousal ">
                        <ng-container *ngFor="let img of projectMedia">
                            <ng-template carouselSlide>
                                <img class="object-cover h-vh70" [src]="img?.name">
                            </ng-template>
                        </ng-container>
                    </owl-carousel-o>
                </div>

                <p (click)="gallery.prev()" class="hover:cursor-pointer absolute top-1/2 left-2 hover:scale-105 z-10">
                    <img class="h-7 w-7" src="assets/icons/arrow-left.png">
                </p>
                <p (click)="gallery.next()" class="hover:cursor-pointer absolute top-1/2 right-2 hover:scale-105 z-10">
                    <img class="h-7 w-7" src="assets/icons/arrow-right.png">
                </p>
            </div>
        </div>
    </div>
</div> -->

<div *ngIf="showGallery" (click)="showGallery = false; thumbsSwiper=0" class="bg-overlay fixed inset-0 z-[99] "></div>
<div *ngIf="showGallery"
    class="z-[100]  mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-white w-full lg:w-1/2 rounded-xl ">
    <div class="relative max-h-[80vh] overflow-hidden m-1 lg:m-3">
        <swiper #galleryCarousel class="" [config]="mainImage" [thumbs]="{ swiper: thumbsSwiper }">
            <ng-template swiperSlide class="" *ngFor="let img of projectMedia">
                <img class=" w-full cursor-pointer" [src]="img.name">
            </ng-template>
        </swiper>
        <img src="../../../assets/icons/arrow-semi-right.svg"
            class="h-8 aspect-square absolute z-[101] top-1/2 translate-y-1/2 right-3 cursor-pointer "
            (click)="slideNext()">
        <img src="../../../assets/icons/arrow-semi-left.svg"
            class="h-8 aspect-square absolute z-[101] top-1/2 translate-y-1/2 left-3 cursor-pointer "
            (click)="slidePrev()">
    </div>
    <swiper class="h-full textSlide" [config]="thumbs" (swiper)="thumbsSwiper = $event">
        <ng-template swiperSlide class="pb-1 lg:pb-3 " *ngFor="let img of projectMedia">
            <img class=" w-full aspect-square  object-cover cursor-pointer" [src]="img.name">
        </ng-template>
    </swiper>
</div>