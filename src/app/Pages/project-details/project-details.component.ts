import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { ProjectsService } from 'src/app/Services/Projects/projects.service';
import SwiperCore, { Autoplay, Pagination, Scrollbar, SwiperOptions, Navigation, Thumbs, FreeMode } from 'swiper';
import { SwiperComponent } from 'swiper/angular';
SwiperCore.use([Scrollbar, Autoplay, Pagination, Navigation, Thumbs, FreeMode]);


@Component({
  selector: 'app-project-details',
  templateUrl: './project-details.component.html',
  styleUrls: ['./project-details.component.scss']
})
export class ProjectDetailsComponent implements OnInit {

  constructor(
    private projectsService: ProjectsService,
    private toastr: ToastrService,
    private activatedRoute: ActivatedRoute,
    private configSettings: configSettings
  ) { }

  // VARIABLES
  projectID: string
  projectDetails: any
  projectMedia: any
  galleryMedia: any = []
  title: string
  description: any
  lang: string = this.configSettings.getLang()
  showGallery:boolean = false

  ngOnInit(): void {
    this.activatedRoute.params.subscribe(params => {
      this.projectID = params.id
    })

    this.getProjectDetails()
  }

// project detail api
  getProjectDetails() {
    const getParams = {
      project_id: this.projectID
    }

    this.projectsService.projectDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.projectDetails = response.body.data
            this.projectMedia = response.body.data.images
            this.title = response.body.data['title_' + this.lang]
            this.description = response.body.data['desc_' + this.lang]
            this.projectMedia.forEach((el: any) => {
              if (el.is_thumbnail == 0) {
                this.galleryMedia.push(el)
              }
            });
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  // gallery carousal
  galleryOptions: OwlOptions = {
    loop: true,
    mouseDrag: true,
    touchDrag: true,
    pullDrag: true,
    dots: false,
    lazyLoad: false,
    navSpeed: 700,
    rtl: false,
    autoHeight: true,
    autoWidth: true,
    center: false,
    margin: 25,
    responsive: {
      0: {
        items: 1,
      },
      640: {
        items: 1
      },
      768: {
        items: 1
      },
      896: {
        items: 1
      },
      1024: {
        items: 1,
      },
      1280: {
        items: 1,
      },
      1536: {
        items: 1
      }
    },
    nav: false
  }

  thumbsSwiper: any = ''
  
  mainImage: SwiperOptions = {
    slidesPerView: 1,
    slidesPerGroup: 1,
    spaceBetween: 2,
    loop: true,
    watchSlidesProgress: true,
    autoHeight:true
  };

  tempNumber: any = 4
  loopOrNoLoop: boolean = false
  thumbs: SwiperOptions = {
    slidesPerView: 10,
    slidesPerGroup: 1,
    spaceBetween: 5,
    loop: false,
    centerInsufficientSlides: true
  };

  @ViewChild('galleryCarousel', { static: false }) galleryCarousel?: SwiperComponent;

  slideNext() {
    this.galleryCarousel?.swiperRef.slideNext(500);
  }
  slidePrev() {
    this.galleryCarousel?.swiperRef.slidePrev(500);
  }
}
