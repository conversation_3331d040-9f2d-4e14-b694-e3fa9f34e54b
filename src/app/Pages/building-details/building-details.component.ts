import { ViewportScroller } from '@angular/common';
import { Component, ElementRef, HostListener, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { BuildingGuideService } from 'src/app/Services/Building-guides/building-guide.service';
import { configSettings } from 'src/app/Config/config.settings';


@Component({
  selector: 'app-building-details',
  templateUrl: './building-details.component.html',
  styleUrls: ['./building-details.component.scss']
})
export class BuildingDetailsComponent implements OnInit {

  constructor(

    private activatedRoute: ActivatedRoute,
    private buildingGuideService: BuildingGuideService,
    private toastr: ToastrService,
    private viewportScroller: ViewportScroller,
    public router: Router,
    private route: ActivatedRoute,
    private configSettings: configSettings

  ) { }


  buildingDetailID: any
  buildingGuideDetails: any
  buildings: any
  nearAmenity: any
  latlonArr: any

  showSearch = false
  lat: any
  lng: any

  googleLatLon: any

  ngOnInit(): void {

    this.configSettings.tiggerNotificationAPi()

    this.activatedRoute.params.subscribe((param) => {
      this.buildingDetailID = param['id'];
    });

    const getParams = {

      id: this.buildingDetailID

    }

    this.buildingGuideService.getBuildingGuidesDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.buildingGuideDetails = response.body.data
            this.buildings = this.buildingGuideDetails.building_detail
            this.nearAmenity = this.buildingGuideDetails.near_by
            this.latlonArr = this.buildingGuideDetails.latlon.split(',')
            this.googleLatLon = this.latlonArr.toString()
            this.lat = Number(this.latlonArr[0])
            this.lng = Number(this.latlonArr[1])


          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }

  currentActive = 1

  scrollTo(el: any, currentTab: any) {
    // this.viewportScroller.scrollToAnchor(el);

    el.scrollIntoView({ behavior: 'smooth', block: 'start' });
    this.currentActive = currentTab
  }

  @ViewChild('highlights') highlightsRef: ElementRef;
  @ViewChild('buildingDetails') buildingDetailsRef: ElementRef;
  @ViewChild('nearByAmenties') nearByAmentiesRef: ElementRef;
  @ViewChild('location') locationRef: ElementRef;
  lists: any
  public highlightsOffset: any
  public buildingDetailsOffset: any
  public nearByAmentiesOffset: any
  public locationOffset: any

  ngAfterViewInit() {
    this.highlightsOffset = this.highlightsRef.nativeElement.offsetTop;
    this.buildingDetailsOffset = this.buildingDetailsRef.nativeElement.offsetTop;
    this.nearByAmentiesOffset = this.nearByAmentiesRef.nativeElement.offsetTop;
    this.locationOffset = this.locationRef.nativeElement.offsetTop;
    // this.aboutOffset = this.aboutElement.nativeElement.offsetTop;
    // console.log(this.aboutOffset)
  }

  activeLink(li: any) {
    this.lists.forEach((item: { classList: { remove: (arg0: string) => any; }; }) => item.classList.remove('active'));
    li.classList.add('active');
  }


  @HostListener('window:scroll', ['$event'])
  checkOffsetTop() {
    if (window.scrollY >= this.highlightsOffset && window.scrollY < this.buildingDetailsOffset) {
      this.currentActive = 1;
    } else if (window.scrollY >= this.buildingDetailsOffset && window.scrollY < this.nearByAmentiesOffset) {
      this.currentActive = 2;
    } else if (window.scrollY >= this.nearByAmentiesOffset && window.scrollY < this.locationOffset) {
      this.currentActive = 3;
    }
    else if (window.scrollY >= this.locationOffset) {
      this.currentActive = 4;
    } else {
      this.currentActive = 1;
    }
    // console.log(window.scrollY, this.highlightsOffset)
  }
  routeTo(value: string) {
    this.router.navigate(['/property-listing'], { queryParams: { propertyFor: value }, relativeTo: this.route });
  }


}
