// .tab.active {
//     color: rgb(0 35 73 / var(--tw-text-opacity));
//     border-bottom: 3px solid rgb(0 35 73 / var(--tw-text-opacity));

// }

.oneline-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
.link-nav {
    color: #656565,
}

.link-bar {
    background-color: white;
}

.link-nav.active {
    color: #002349,
}

.link-bar.active {
    background-color: #002349;
}

.map {
    position: relative;
}

.map-center-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
}

.link-nav {
    color: #656565,
}

.link-bar {
    background-color: white;
}

.link-nav.active {
    color: #002349,
}

.link-bar.active {
    background-color: #002349;
}

@media only screen and (max-width: 1023px) and (min-width: 766px){
    .mobileslider{
        overflow-x: scroll;
        width: 100%;
        

        .mobilemenu{
            min-width: 250px;
        }

    }
}

@media only screen and (max-width: 767px){
    .mobileslider{
        overflow-x: scroll;
        width: 100%;
        

        .mobilemenu{
            min-width: 350px;
           
        }

    }
}

.gm-style-iw-t .gm-style-iw-tc{
    background: white;
    clip-path: polygon(0% 0%,50% 100%,100% 0%);
    content: '';
    height: 12px;
    position: absolute;
    bottom: -25px;
    width: 25px;
}