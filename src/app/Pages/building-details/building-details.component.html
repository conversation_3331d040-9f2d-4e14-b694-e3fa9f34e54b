<section>
    <div class="container mx-auto px-4 sm:px-14 md:px-24  my-16 ">
        <div class=" bg-white rounded-xl px-5 xl:px-14">
            <div class="flex py-4 xl:py-8 pt-12">
                <p class="text-blue-400 text-sm font-medium"> <span class="cursor-pointer"
                        [routerLink]="['/building-guides']">{{"Building Guides" | translate}}</span> <span
                        class="text-gray-600 text-xs px-2"> > </span></p>

                <p class="text-sm font-medium text-gray-700">{{buildingGuideDetails?.title}}</p>

            </div>
            <p class="text-primary text-2xl xl:text-5xl font-bold font-oxygen py-8">{{buildingGuideDetails?.title}}</p>

            <div>
                <img [src]="buildingGuideDetails?.banner" class="w-full h-[34rem] object-cover py-6" alt="">
            </div>
            <!-- <ul class="flex lg:justify-center pb-8 scroll-smooth overflow-x-scroll lg:overflow-hidden  px-8 md:px-0 ">
            <li
                class="tab tab1 active  pr-3 py-3  cursor-pointer text-center text-base  font-semibold  text-gray-400  border-b-primary ">
                <span class=" flex"><img src="../../../assets/icons/search-b.svg" alt=""
                        class="w-10 px-2 hidden lg:block"> Highlights</span>
            </li>
            <li
                class="tab tab2 px-3 py-3   cursor-pointer  text-center text-base  font-semibold  text-gray-400  border-b-primary">
                <span class="px-2 flex"><img src="../../../assets/icons/building_details.svg" alt=""
                        class="w-8 px-2 hidden lg:block"> Building Details</span>
            </li>
            <li
                class="tab tab3 px-3 py-3  cursor-pointer text-center text-base  font-semibold  text-gray-400  border-b-primary ">
                <span class="px-2 flex"><img src="../../../assets/icons/amenities.svg" alt=""
                        class="w-10 px-2 hidden lg:block">Near by Amenties </span>
            </li>
            <li
                class="tab tab4 px-3 py-3   cursor-pointer  text-center text-base  font-semibold  text-gray-400  border-b-primary">
                <span class="px-2 flex"><img src="../../../assets/icons/location.svg" alt=""
                        class="w-10 px-2 hidden lg:block">Location</span>
            </li>
        </ul> -->
            <!-- nav -->
            <div class="flex justify-center  2xl:gap-10  sticky my-6">
                <div (click)="scrollTo(highlights,1)" class="cursor-pointer  w-1/4">
                    <div class="flex items-center justify-center mb-3 gap-2 lg:gap-4  xl:px-5">
                        <img *ngIf="currentActive !== 1" src="../../../assets/icons/highlights.svg" class="h-5 w-5 xl:h-7 xl:w-7">
                        <img *ngIf="currentActive === 1" src="../../../assets/icons/search-b.svg" class="h-5 w-5 xl:h-7 xl:w-7">
                        <p [ngClass]="{'active': currentActive === 1}"
                            class="hidden lg:block link-nav  text-sm xl:text-lg font-bold text-mediumGray">
                            {{"Highlights" | translate}}</p>
                    </div>
                    <div [ngClass]="{'active': currentActive === 1}"
                        class="link-bar  h-[3.75px] bg-white rounded-t-lg ">
                    </div>
                </div>
                <div (click)="scrollTo(buildingDetails,2)" class="cursor-pointer  w-1/4">
                    <div class="flex items-center justify-center mb-3 gap-2 lg:gap-4  xl:px-5">
                        <img *ngIf="currentActive !== 2" src="../../../assets/icons/building_details.svg" class="h-5 w-5 xl:h-7 xl:w-7">
                        <img *ngIf="currentActive === 2" src="../../../assets/icons/building_details-b.svg" class="h-5 w-5 xl:h-7 xl:w-7">
                        <p [ngClass]="{'active': currentActive === 2}"
                            class="hidden lg:block link-nav text-sm xl:text-lg font-bold text-mediumGray">
                            {{"Building Details" | translate}}</p>
                    </div>
                    <div [ngClass]="{'active': currentActive === 2}"
                        class="link-bar  h-[3.75px] bg-white rounded-t-lg ">
                    </div>
                </div>
                <div (click)="scrollTo(nearByAmenties,3)" class="cursor-pointer  w-1/4">
                    <div class="flex items-center justify-center mb-3 gap-2 lg:gap-4  xl:px-5">
                        <img *ngIf="currentActive !== 3" src="../../../assets/icons/amenities.svg" class="h-5 w-5 xl:h-7 xl:w-7 ">
                        <img *ngIf="currentActive === 3" src="../../../assets/icons/amenities-b.svg" class="h-5 w-5 xl:h-7 xl:w-7 ">
                        <p [ngClass]="{'active': currentActive === 3}"
                            class="hidden lg:block link-nav text-sm xl:text-lg font-bold text-mediumGray">
                            {{"Near by Amenties" | translate}}</p>
                    </div>
                    <div [ngClass]="{'active': currentActive === 3}"
                        class="link-bar  h-[3.75px] bg-white rounded-t-lg ">
                    </div>
                </div>
                <div *ngIf="latlonArr?.length > 0" (click)="scrollTo(location,4)"
                    class="cursor-pointer  w-1/4">
                    <div class="flex items-center justify-center mb-3 gap-2 lg:gap-4  xl:px-5">
                        <img *ngIf="currentActive === 4" src="../../../assets/icons/location-b.svg" class="h-5 w-5 xl:h-7 xl:w-7">
                        <img *ngIf="currentActive !== 4" src="../../../assets/icons/location.svg" class="h-5 w-5 xl:h-7 xl:w-7">
                        <p [ngClass]="{'active': currentActive === 4}"
                            class="hidden lg:block link-nav text-sm xl:text-lg font-bold text-mediumGray">
                            {{"Location" | translate}}</p>
                    </div>
                    <div [ngClass]="{'active': currentActive === 4}"
                        class="link-bar  h-[3.75px] bg-white rounded-t-lg ">
                    </div>
                </div>

            </div>
            <section #highlights id="highlights" class="scroll-mt-40 mt-10 mb-6">
                <div *ngIf="buildingGuideDetails?.about !== ''">
                    <p class="font-bold text-2xl text-primary font-oxygen mb-4 ">{{"Highlights" | translate}}
                    </p>
                    <p class="text-gray-800 font-semibold text-lg font-oxygen pb-3">{{"About" | translate}}
                        {{buildingGuideDetails?.title}}</p>
                    <div class="text-base text-[#383838] font-oxygen space-x-2"
                        [innerHTML]="buildingGuideDetails?.about"></div>
                </div>
            </section>
            <section id="buildingDetails" #buildingDetails class="scroll-mt-40">
                <div *ngIf="buildings?.length > 0">
                    <p class="font-bold text-2xl text-primary font-oxygen ">
                        {{"Building Details" | translate}}
                    </p>
                    <p class="text-gray-800 font-semibold text-lg font-oxygen pt-4">{{"Facilities in" | translate}}
                        {{buildingGuideDetails?.title}}
                    </p>
                    <div class="grid grid-cols-12 pb-3 gap-5" *ngFor="let building of buildings">
                        <div *ngIf="building.image !== '' " class="col-span-12 lg:col-span-6 py-5">
                            <img [src]="building.image" alt="" class="w-full h-full aspect-[2/1] object-cover">
                        </div>
                        <div class="col-span-12 lg:col-span-6 px-1">
                            <p class="text-gray-800 font-semibold text-lg font-oxygen py-3">{{building.title}}</p>
                            <p class="text-base text-[#383838] font-oxygen space-x-2">{{building.content}}</p>
                        </div>
                    </div>
                </div>
            </section>

            <section id="nearByAmenties" #nearByAmenties class="scroll-mt-40">
                <div *ngIf="nearAmenity?.length>0">
                    <h5 class="font-bold text-2xl text-primary font-oxygen pt-8 ">
                        {{"Near by Amenties" | translate}}</h5>
                    <div class="grid grid-cols-12 pb-3 gap-5" *ngFor="let amenity of nearAmenity">
                        <div *ngIf="amenity.image !== '' " class="col-span-12 lg:col-span-6 py-5">
                            <img [src]="amenity.image" alt="" class="w-full h-full aspect-[2/1] object-cover">
                        </div>
                        <div class="col-span-12 lg:col-span-6 px-1">
                            <p class="text-gray-800 font-semibold text-lg font-oxygen py-3">{{amenity.title}}</p>
                            <p class="text-base text-[#383838] font-oxygen space-x-2">{{amenity.content}}</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- <div class="grid grid-cols-12 py-3 gap-5">
            <div class="col-span-12 lg:col-span-6 w-11/12 pb-8">
                <h5 class="text-gray-800 font-semibold text-lg font-oxygen py-5 mt-2">Most Popular Apartment Types In
                    Kipco Hamza Tower</h5>
                <div class="bg-teal-50 flex flex-col lg:flex-row  justify-center text-center py-3 lg:py-5">
                    <p class="px-10 text-3xl font-bold text-primary font-oxygen"><span
                            class="text-sm font-medium  px-3">1 Bed</span>30%</p>
                    <p class="px-10 text-3xl font-bold text-primary font-oxygen"><span
                            class="text-sm font-medium  px-3">2 Bed</span>75%</p>
                </div>
            </div>
            <div class="col-span-12 lg:col-span-6 w-11/12 lg:px-6 pb-8">
                <h5 class="text-gray-800 font-semibold text-lg font-oxygen py-5 mt-2">Rental Yield in Kipco Hamza Tower
                </h5>
                <div class="bg-teal-50 flex flex-col lg:flex-row  justify-center text-center  py-3 lg:py-5">
                    <p class="px-10 text-3xl font-bold text-primary font-oxygen"><span
                            class="text-sm font-medium  px-3">1 Bed</span>50%</p>
                    <p class="px-10 text-3xl font-bold text-primary font-oxygen"><span
                            class="text-sm font-medium  px-3">2 Bed</span>80%</p>
                </div>
            </div>

            <div class="col-span-12 lg:col-span-6">

                <p class="text-gray-800 font-semibold text-lg font-oxygen py-3 mt-2">Major Landmarks</p>
                <p class="text-base text-gray-400 font-oxygen space-x-2 pb-6">Sed ut perspiciatis unde omnis iste natus
                    error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo
                    inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam
                    voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos
                    qui ratione voluptatem sequi nesciunt. Neque porro quisquam est,</p>
                <div class="">
                    <p class="text-gray-800 font-semibold text-lg font-oxygen py-3">Supermarket includes</p>
                    <ul class="text-primary list-disc font-oxygen text-sm font-semibold mx-4">
                        <li>Lorem ipsum dolor sit amet</li>
                        <li class="py-2">consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                            dolore magna aliqua.</li>
                        <li class="py-2">Ut enim ad minim veniam, quis nostrud exercitation.</li>
                        <li class="py-2">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu
                            fugiat nulla pariatur. Excepteur sint occaecat.</li>
                    </ul>
                </div>
            </div>
            <div class="col-span-12 lg:col-span-6 py-6">
                <img src="../../../assets/images/bulding-details-image-4.png" alt=""
                    class="w-full  aspect-[5/2] object-cover">
            </div>

        </div>

        <div class="py-6">
            <p class="text-gray-800 font-semibold text-lg font-oxygen py-3">Things To Consider</p>
            <p class="text-base text-gray-400 font-oxygen space-x-2">Sed ut perspiciatis unde omnis iste natus error sit
                voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore
                veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia
                voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione
                voluptatem sequi nesciunt. Neque porro quisquam est,</p>
        </div> -->

            <section id="location" #location class="scroll-mt-40">
                <div *ngIf="latlonArr?.length > 0">
                    <p class="font-bold text-2xl text-primary font-oxygen ">{{"Location" | translate}}</p>
                    <!-- Map -->
                    <div class=" mb-12 w-full">
                        <p class="mb-2 text-sm text-lighterBlack">{{"Map Location" | translate}}</p>
                        <a class="map h-[34rem] block" target="_blank"
                            [href]="'https://www.google.com/maps/search/?api=1&query='+googleLatLon+'&zoom=17'">
                            <app-map class="pointer-events-none" [showSearch]="showSearch" [lat]="lat" [lng]="lng"
                                [zoom]="17"></app-map>
                            <div class="group w-fit mx-auto">
                                <div
                                    class="gm-style-iw-t w-fit mx-auto bottom-[25rem] max-w-[648px] max-h-20 min-w-0 relative hidden group-hover:block">
                                    <div class="flex gap-2 w-[17rem] h-24 cursor-pointer p-2  bg-white rounded-md">
                                        <div class="flex flex-col gap-1">
                                            <p class="text-base tracking-tight font-bold text-primary">
                                                {{buildingGuideDetails?.country}}</p>
                                            <p class="text-gray-800 text-sm tracking-tight font-semibold ">
                                                {{buildingGuideDetails?.state}} </p>
                                            <p
                                                class="text-xs text-[#383838]  tracking-tight font-medium mb-3 oneline-ellipsis">
                                                {{buildingGuideDetails?.title}}</p>
                                        </div>
                                    </div>
                                    <div class="gm-style-iw-tc left-[45%]"></div>
                                </div>
                                <div class="map-center-overlay">
                                    <img src="../../../assets/icons/location-pointer.svg" width="45" height="45">
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </section>
            <!-- 
        <p class="text-base text-gray-400 font-oxygen space-x-2 py-3 px-6">XDF International Airport is about 25 minutes
            away</p>
        <p class="text-base text-gray-400 font-oxygen space-x-2 py-3 px-6">6 minutes’ drive to get on to Al Fahaheel
            Area, Part 01 Building 800022 Coastal Line C,Dabbous street.</p>
        <p class="text-base text-gray-400 font-oxygen space-x-2 py-3 px-6">Business hub Al Kuwait City is 12 minutes
            away</p> -->

            <div class="pt-12 text-center">
                <p class="font-bold text-2xl text-primary font-oxygen ">{{"Are you looking to buy or rent apartments"
                    | translate}} ?</p>
                <div class="flex flex-col lg:flex-row  justify-center py-10 gap-4">
                    <button class="bg-primary text-gray-300 px-8 py-2 border border-primary "
                        (click)="routeTo('R')">{{"APARTMENTS FOR RENT" |
                        translate}}</button>
                    <button class="bg-primary text-gray-300  px-8  py-2  border border-primary "
                        (click)="routeTo('S')">{{"APARTMENTS FOR BUY"
                        | translate}}</button>
                </div>
            </div>


        </div>
    </div>
</section>