<div class='container mx-auto px-4 sm:px-14 md:px-24 leading-[0.5rem] '>
    <div class="grid grid-cols-12 border border-borderColor rounded-xl my-10 ">

        <!-- firstcolumn image -->
        <div id="banner" class="flex-row col-span-12 hidden xl:block xl:col-span-7 ">
            <div
                class="login-image banner rtl:rounded-r-xl ltr:rounded-l-xl    h-full   text-white font-sans font-bold">
                <!-- <div class="block absolute bottom-5 left-16  text-white w-2/3">
                    <p class="text-2xl lg:text-3xl font-semibold ">
                        Create an account to unlock these benefits
                    </p>
                    <ul class="list-disc text-xs md:text-sm mt-2 ml-4">
                        <li class="pb-3">Get latest update about Properties and Project.</li>
                        <li class="pb-3">Access millions of advertiser details in one click.</li>
                        <li class="pb-3">Get market information, reports and pice trends</li>
                        <li class="pb-3">Advertise your property for free!</li>
                    </ul>
                </div> -->
            </div>
            <div class="block relative bottom-54 md:bottom-52 rtl:right-12 left-12  text-white w-2/3">
                <p class="text-2xl lg:text-2xl font-semibold ">
                    {{"Create an account to unlock these benefits" | translate}}
                </p>
                <ul class="list-disc text-xs md:text-sm mt-2 rtl:mr-4 rtl:ml-[unset] ml-4">
                    <li class="pb-3">{{"Get latest update about Properties and Project" | translate}}.</li>
                    <li class="pb-3">{{"Access millions of advertiser details in one click" | translate}}.</li>
                    <li class="pb-3">{{"Get market information, reports and pice trends" | translate}}</li>
                </ul>
            </div>

        </div>
        <div class="  col-span-12 xl:col-span-5  text-black font-semibold bg-white rounded-md xl:rounded-r-xl ">
            <div class=" items-center justify-items-start ">
                <div class="flex shadow-md">
                    <div id="resale" (click)="changeTab($event)"
                        class="tab tab1 active  px-10 py-3  cursor-pointer text-center text-base w-1/2">{{"LOGIN" |
                        translate}}
                    </div>
                    <div id="rent" (click)="changeTab($event)"
                        class="tab tab2 px-10 py-3  cursor-pointer rounded-t-md rtl:rounded-r-none ltr:rounded-l-none text-center text-base w-1/2">
                        {{"REGISTER" | translate}}</div>
                </div>






                <div *ngIf="activeTab === 'resale'" class="rounded-md xl:rounded-r-md px-5 xl:px-16 ">
                    <div>
                        <h6 class="text-primary text-xl mb-2 pt-6">{{"Welcome back" | translate}}</h6>
                        <p class="text-zinc-400 text-xs font-medium font-oxygen">
                            {{"Showcase your property to 1 lac + buyers" | translate}}.</p>
                    </div>

                    <form #login="ngForm" (ngSubmit)="getLoginData(login.value)">
                        <div class="pt-5 ">

                            <input type="text" name="email" ngModel placeholder="{{'Enter your Email' | translate}}"
                                class="w-full h-10 px-4 rtl:pr-10 pl-10 pb-1 border autofill:bg-none hover: border-gray-500 placeholder:text-xs  text-xs   focus:outline-none  rounded font-normal" />
                            <div class="block relative h-0 bottom-7 rtl:right-2 left-2 w-4"><img
                                    src="../../../assets/icons/Iconly-Light-outline-Message.svg" alt=""></div>
                        </div>
                        <div class="pt-3 relative">

                            <input [type]="passwordVisibleOption" name="password" ngModel
                                placeholder="{{'Enter your Password' | translate}}"
                                class=" w-full h-10 px-4 rtl:pr-10 pl-10 pb-1 border autofill:bg-none hover: border-gray-500 placeholder:text-xs   text-xs  focus:outline-none  rounded font-normal" />
                            <div class="block relative h-0 bottom-7 rtl:right-2 left-2 w-4"><img
                                    src="../../../assets/icons/Iconly-Light-outline-Password.svg" alt=""></div>
                            <!-- Password icon -->
                            <div *ngIf="passwordVisibleOption === 'password'" (click)="passwordVisibleOption = 'text'"
                                class="block absolute h-0 bottom-7 rtl:left-2 ltr:right-2 w-5 cursor-pointer"><img
                                    src="../../../assets/icons/password-visible.png" class="w-4" alt="">
                            </div>
                            <div *ngIf="passwordVisibleOption === 'text'" (click)="passwordVisibleOption = 'password'"
                                class="block absolute h-0 bottom-7 rtl:left-2 ltr:right-2 w-5 cursor-pointer"><img
                                    src="../../../assets/icons/password-invisible.png" class="w-4" alt="">
                            </div>
                        </div>
                        <div class="relative   text-end">
                            <span class="text-xs mt-2 font-medium text-gray-600 cursor-pointer"
                                (click)="OpenforgotPassword()" >
                                {{"Forgot password?" | translate}}
                            </span>
                        </div>

                        <div class="text-lg font-medium w-full pt-8">
                            <button type="" class="text-center w-full h-10 bg-primary  text-white rounded">
                                {{"Login" | translate}}
                            </button>
                        </div>
                    </form>

                    <!-- <div class="text-gray-300 font-medium text-xs pt-3  flex">
                        <input type="checkbox" name="tos" ngModel class="h-4 w-4 relative rtl:ml-2 rtl:mr-0 mr-2"
                            required>
                        <p  class="cursor-pointer">{{"By proceding you agree with our" |
                            translate}} <span (click)="TOSModel = true">
                                <a class="text-primary underline underline-offset-4 ">
                                    {{"Terms of Service" | translate}}
                                </a></span>
                            <span class="text-primary  px-1">{{"&" | translate}}</span>
                            <span (click)="PPModel = true" class="cursor-pointer"><a
                                    class="text-primary underline underline-offset-4 ">
                                    {{"Privacy Policy" | translate}}
                                </a>
                            </span>
                        </p>

                    </div> -->


                    <div class="text-sm font-medium text-black pt-12 pb-20">
                        <p class="text-center">{{"Or Continue with social network" | translate}}</p>
                        <div class="grid grid-cols-3 gap-3 pt-6 ">
                            <button (click)="signInWithApple()" class="col-span-1 border py-1 h-9 rounded"><img
                                    src="../../../assets/icons/apple-logo.svg" alt="" class="mx-auto w-4"></button>
                            <button (click)="loginWithFacebook()" class="col-span-1  border  py-1 h-9  rounded"
                                style="background-color: #3B5998;"><img
                                    src="../../../assets/images/facebook-app-symbol.png" alt=""
                                    class="mx-auto w-3"></button>
                            <button #googleBtn GoogleSigninButtonDirective
                                class="col-span-1  border  h-9  rounded flex justify-center items-center cursor-pointer relative">
                                <img src="../../../assets/icons/google-logo.svg" alt=""
                                    class="absolute z-1 rtl:right-1/2 ltr:left-1/2 rtl:translate-x-1/2 ltr:-translate-x-1/2 w-4 cursor-pointer">
                                <asl-google-signin-button GoogleSigninButtonDirective type='icon' size='large'
                                    width="400" shape="rectangular" logo_alignment="center"
                                    class="opacity-0 cursor-pointer relative z-10"></asl-google-signin-button>
                                <asl-google-signin-button GoogleSigninButtonDirective type='icon' size='large'
                                    width="400" shape="rectangular" logo_alignment="center"
                                    class="opacity-0 cursor-pointer"></asl-google-signin-button>
                                <asl-google-signin-button GoogleSigninButtonDirective type='icon' size='large'
                                    width="400" shape="rectangular" logo_alignment="center"
                                    class="opacity-0 cursor-pointer relative z-10"></asl-google-signin-button>
                            </button>

                        </div>
                    </div>

                </div>


                <div *ngIf="activeTab === 'rent'" class=" rounded-r-md px-5 xl:px-16">
                    <div>
                        <h6 class="text-primary text-xl mb-2 pt-6">{{"Create an account" | translate}}</h6>
                        <p class="text-zinc-400 text-xs font-medium font-oxygen">
                            {{"Showcase your property to 1 lac +buyers" |translate}}.</p>
                    </div>
                    <form #register="ngForm" (ngSubmit)="getFormData(register.value)">
                        <div class="pt-3 relative">
                            <label for=""
                                class="text-primary text-xs font-bold relative top-2 rtl:right-1 left-1 bg-white px-2 ">
                                {{"I AM" | translate}}</label>
                            <select name="userType" id="" ngModel
                                class="w-full h-10 px-2 border rounded hover: border-gray-400 placeholder:text-xs text-xs bg-white   font-normal focus:outline-none">
                                <option value="" disabled selected hidden class=" text-xs">{{"Select User Type" |
                                    translate}}
                                </option>
                                <option *ngFor="let userType of userTypes" value={{userType?.id}}>
                                    {{userType.name}}</option>
                            </select>
                            <span class="absolute rtl:left-3 ltr:right-0 top-1/2 h-2 w-2 -translate-y-1/2">
                                <img class="h-full w-full mt-3 mt-l mt-l2"
                                    src="../../../assets/icons/arrow-semi-down.svg">
                            </span>


                        </div>

                        <!-- <div class="pt-4 ">
                            <div class="block relative  h-0 top-3 left-2"><img src="../../../assets/icons/Iconly-Light-outline-Profile.svg" alt=""></div>
                            <input type="text" name="firstName" placeholder="User Name" ngModel
                                class="w-full py-2 px-4 pl-10 border rounded  hover: border-gray-500    text-base  font-medium placeholder:font-normal focus:outline-none" />    
                        </div> -->

                        <div class="mt-4">
                            <div class="block relative h-0 top-2 rtl:right-2 left-2 w-5"><img
                                    src="../../../assets/icons/Iconly-Light-outline-Profile.svg" class="w-4" alt="">
                            </div>
                            <input type="text" name="firstName" placeholder="{{'First Name' | translate}}" ngModel
                                class="w-1/2 h-10 pb-1 px-4 rtl:pr-10 pl-10 border rtl:border-l-0 rtl:border-r rtl:rounded-l-none rtl:rounded-r border-r-0 rounded rounded-r-none placeholder:text-xs hover: border-gray-500     text-xs font-medium placeholder:font-normal focus:outline-none" />
                            <input type="text" name="lastName" placeholder="{{'Last Name' | translate}}" ngModel
                                class="w-1/2 h-10 pb-1 rtl:pr-0.5 pl-0.5 px-4 border rtl:border-r-0 rtl:border-l rtl:rounded-r-none rtl:rounded-l border-l-0 rounded rounded-l-none placeholder:text-xs hover: border-gray-500   text-xs  font-medium  placeholder:font-normal focus:outline-none" />
                            <div
                                class="block  relative h-0 bottom-7 rtl:right-36 rtl:sm:right-52 rtl:md:right-64 rtl:xl:right-32 left-36 sm:left-52 md:left-64 xl:left-32 rtl:2xl:right-48 2xl:left-48 w-1 text-gray-200">
                                <p>|</p>
                            </div>

                        </div>
                        <div class="my-4 ">

                            <input type="text" name="email" placeholder="{{'Enter Your email' | translate}}" ngModel
                                class="w-full h-10 pb-1 px-4 rtl:pr-10 pl-10 border autofill:bg-none rounded placeholder:text-xs hover: border-gray-500  text-xs  font-medium placeholder:font-normal focus:outline-none" />
                            <div class="block relative h-0 bottom-7 rtl:right-2 left-2 w-5"><img
                                    src="../../../assets/icons/Iconly-Light-outline-Message.svg" class="w-4" alt="">
                            </div>
                        </div>

                        <!-- <div class="mt-4 h-8 w-full inline-block border rounded hover: border-gray-500 ">
                            <span>
                                <select name="phoneCode" id="" ngModel
                                    class="w-1/3 h-6 2xl:w-1/4  px-4  bg-white   text-sm border-r-0  font-normal placeholder:font-normal focus:outline-none">
                                    <option value="+965">+965</option>
                                    <option value="+96">+96</option>
                                    <option value="+91">+91</option>
                                </select>
                            </span>
                            <input type="text" name="phone" placeholder="Mobile number" ngModel
                                class="w-2/3 h-7 2xl:w-3/4  px-4    text-base placeholder:text-sm  font-medium border-l-0 placeholder:font-normal focus:outline-none" />
                            <div class="block relative h-0 bottom-6 left-24 w-1 text-gray-200">
                                <p>|</p>
                            </div>


                        </div> -->

                        <div>
                            <!-- <label class="relative top-3 left-1 mb-2 bg-white px-1.5 text-[0.65rem] text-gray-400 ">MOBILE NUMBER</label> -->
                            <div class=" w-full flex items-center border rounded hover: border-gray-500">
                                <select name="phoneCode"
                                    class="w-24 md:w-24 px-2 py-2 h-10 text-gray-500  2xl:w-1/4  bg-white text-sm border-r-0 placeholder:font-xs focus:outline-none ng-pristine ng-valid ng-touched">

                                    <option (click)="getPhoneCode(country.phonecode)" [ngValue]="country.is_active"
                                        *ngFor="let country of countries"
                                        [ngClass]="{'hidden': country.is_active =='No'}"><span> {{country.flag }}</span>
                                        +{{country.phonecode}}</option>
                                </select>
                                <span class="rtl:left-2 ltr:right-0 top-1/2 h-2 w-2 -translate-y-1/2">
                                    <img class="h-full w-full mt-l mt-1"
                                        src="../../../assets/icons/arrow-semi-down.svg">
                                </span>
                                <!-- <div class="drop-down cursor-pointer relative w-[26%] sm:w-1/5 xl:w-[24%] 2xl:w-1/5" (click)="dropDown()">
                                    <div id="main"
                                        class="h-10 outline-none py-3 items-center  px-1  rounded-md  placeholder:text-lg bg-white relative text-xs   ">
                                        <div class="flex gap-1 items-center text-xs" [innerHTML]="defaultPhone"> -->
                                <!-- <img src="../../../assets/icons/kw_flag.png" class="w-6 h-4"><span>965</span> -->
                                <!-- </div>
                                        <span class="absolute rtl:left-2 ltr:right-0 top-1/2 h-2 w-2 -translate-y-1/2">
                                            <img class="h-full w-full" src="../../../assets/icons/arrow-semi-down.svg">
                                        </span>
                                    </div>
                                    <div *ngIf="open"
                                        class="absolute mt-1 bg-white min-w-[6rem] text-xs  w-full text-gray-500 z-10 shadow-md rounded-md overflow-hidden border border-gray-500">
                                        <ul>
                                            <li id="965"
                                                class="hover:bg-gray-200 hover:text-gray-900 p-2 flex text-xs gap-3 items-center "
                                                (click)="option($event)">
                                                <img src="../../../assets/icons/kw_flag.png" class="w-6 h-4"
                                                    alt=""><span>+965</span>
                                            </li>

                                            <li id="96"
                                                class="hover:bg-gray-200 hover:text-gray-900 p-2 flex text-xs items-center gap-3"
                                                (click)="option($event)">
                                                <img src="../../../assets/icons/in_flag.png" class="w-6 h-4"
                                                    alt=""><span>+91</span>
                                            </li>

                                            <li id="974"
                                                class="hover:bg-gray-200 hover:text-gray-900 p-2 flex text-xs items-center gap-3"
                                                (click)="option($event)">
                                                <img src="../../../assets/icons/qr_flag.png"
                                                    class="w-6 h-4"><span>+974</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div> -->
                                <p class="text-gray-200">|</p>

                                <input type="text" name="phone" pattern="^[0-9]{8,12}$" maxlength="10" minlength="8"
                                    ngModel placeholder="{{'Mobile Number' | translate}}"
                                    class="w-2/3 lg:w-1/2 2xl:w-3/4 px-4 rtl:pr-10 text-xs font-medium border-l-0 placeholder:font-xs focus:outline-none ng-pristine ng-valid ng-touched ">
                                <!-- <div  class="block relative h-0 bottom-[28px]  left-24 w-1 text-gray-200">
                                </div> -->
                            </div>
                        </div>
                        <div class="my-4 relative">

                            <input [type]="passwordVisibleOption" name="password"
                                placeholder="{{'Your Password' | translate}}" ngModel
                                class=" w-full h-10 pb-1 px-4 rtl:pr-10 pl-10 border autofill:bg-none placeholder:text-xs rounded hover: border-gray-500  shadow text-xs  font-medium placeholder:font-normal focus:outline-none" />
                            <div class="block relative h-0 bottom-7 rtl:right-2 left-2 w-5"><img
                                    src="../../../assets/icons/Iconly-Light-outline-Password.svg" class="w-4" alt="">
                            </div>
                            <!-- Password icon -->
                            <div *ngIf="passwordVisibleOption === 'password'" (click)="passwordVisibleOption = 'text'"
                                class="block absolute h-0 bottom-7 rtl:left-2 ltr:right-2 w-5 cursor-pointer"><img
                                    src="../../../assets/icons/password-visible.png" class="w-4" alt="">
                            </div>
                            <div *ngIf="passwordVisibleOption === 'text'" (click)="passwordVisibleOption = 'password'"
                                class="block absolute h-0 bottom-7 rtl:left-2 ltr:right-2 w-5 cursor-pointer"><img
                                    src="../../../assets/icons/password-invisible.png" class="w-4" alt="">
                            </div>

                        </div>
                        <div class="my-4 relative">

                            <input [type]="ConfirmPasswordVisibleOption" name="confirmPassword" ngModel
                                placeholder="{{'Your Confirm Password' | translate}}"
                                class=" w-full h-10 pb-1 px-4 rtl:pr-10 pl-10 border autofill:bg-none placeholder:text-xs rounded hover: border-gray-500  shadow text-xs font-medium placeholder:font-normal focus:outline-none" />
                            <div class="block relative h-0 bottom-7 rtl:right-2 left-2 w-5"><img
                                    src="../../../assets/icons/Iconly-Light-outline-Password.svg" class="w-4" alt="">
                            </div>

                            <!-- Password icon -->
                            <div *ngIf="ConfirmPasswordVisibleOption === 'password'"
                                (click)="ConfirmPasswordVisibleOption = 'text'"
                                class="block absolute h-0 bottom-7 rtl:left-2 ltr:right-2 w-5 cursor-pointer"><img
                                    src="../../../assets/icons/password-visible.png" class="w-4" alt="">
                            </div>
                            <div *ngIf="ConfirmPasswordVisibleOption === 'text'"
                                (click)="ConfirmPasswordVisibleOption = 'password'"
                                class="block absolute h-0 bottom-7 rtl:left-2 ltr:right-2 w-5 cursor-pointer"><img
                                    src="../../../assets/icons/password-invisible.png" class="w-4" alt="">
                            </div>

                        </div>
                        <div class="text-gray-300 font-medium text-xs pt-3  flex">
                            <input type="checkbox" name="tos" ngModel class="h-5 w-5 relative rtl:ml-2 rtl:mr-0 mr-2"
                                required>
                            <p class="cursor-pointer  text-black">{{"By proceeding you agree with our" |
                                translate}} <span (click)="OpenTOSModel()">
                                    <a class="text-primary underline underline-offset-4 ">
                                        {{"Terms of Service" | translate}}
                                    </a></span>
                                <span class="text-primary  px-1">{{"&" | translate}}</span>
                                <span (click)="OpenPPModel()" class="cursor-pointer"><a
                                        class="text-primary underline underline-offset-4 ">
                                        {{"Privacy Policy" | translate}}
                                    </a>
                                </span>
                            </p>

                        </div>


                        <div class="text-base font-medium w-full pt-5  ">
                            <button type="" class="text-center w-full h-10 bg-primary rounded  text-white">
                                {{"Register" | translate}}
                            </button>
                        </div>

                    </form>


                    <div class="text-sm font-medium text-black-300 pt-4 pb-6">
                        <p class="text-center text-black">{{"Or Continue with social network" | translate}}</p>
                        <div class="grid grid-cols-3 gap-3 pt-6 ">
                            <button (click)="signInWithApple()" class="col-span-1 border py-1 h-9 rounded"><img
                                    src="../../../assets/icons/apple-logo.svg" alt="" class="mx-auto w-4"></button>
                            <button (click)="loginWithFacebook()" class="col-span-1  border py-1 h-9  rounded"
                                style="background-color: #3B5998;"><img
                                    src="../../../assets/images/facebook-app-symbol.png" alt=""
                                    class="mx-auto w-3"></button>
                            <button #googleBtn GoogleSigninButtonDirective
                                class="col-span-1  border  h-9  rounded flex justify-center items-center cursor-pointer relative">
                                <img src="../../../assets/icons/google-logo.svg" alt=""
                                    class="absolute z-1 rtl:right-1/2 ltr:left-1/2 rtl:translate-x-1/2 ltr:-translate-x-1/2 w-4 cursor-pointer">
                                <asl-google-signin-button GoogleSigninButtonDirective type='icon' size='large'
                                    width="400" shape="rectangular" logo_alignment="center"
                                    class="opacity-0 cursor-pointer relative z-10"></asl-google-signin-button>
                                <asl-google-signin-button GoogleSigninButtonDirective type='icon' size='large'
                                    width="400" shape="rectangular" logo_alignment="center"
                                    class="opacity-0 cursor-pointer"></asl-google-signin-button>
                                <asl-google-signin-button GoogleSigninButtonDirective type='icon' size='large'
                                    width="400" shape="rectangular" logo_alignment="center"
                                    class="opacity-0 cursor-pointer relative z-10"></asl-google-signin-button>
                            </button>
                        </div>
                    </div>

                </div>


            </div>
        </div>
    </div>

</div>

<!--  don't remove Popup  -->

<!--  forget password model  -->
<!--                                           -->

<div *ngIf="forgotPasswordModel">
    <div (click)="OpenforgotPassword()" class="bg-overlay fixed inset-0 z-[99]"></div>
    <div
        class="z-[100] mx-auto fixed top-1/2 left-[5%] sm:left-1/2 sm:-translate-x-1/2 -translate-y-1/2 py-8 px-10 bg-white w-[90%] sm:w-92 rounded-xl">
        <div>
            <div class="h-14 mb-2">
                <img class="block mx-auto h-full w-full" src="../../../assets/icons/logo-blue.svg" alt="">
            </div>
            <div class="h-32 w-32 mx-auto">
                <img class="block mx-auto h-full w-full" src="../../../assets/images/boy-phone.png" alt="">
            </div>
            <p class="text-lg font-extrabold text-center py-3">{{"Forgot your password ?" | translate}}</p>
            <p class="text-[0.65rem] text-center text-gray-500 font-semibold px-4 pb-3  mb-2">
                {{"Enter your registered email id or mobile number to reset the password" | translate}}</p>

            <div class="flex justify-between mb-5">
                <div id="emailTab" (click)="resetPasswordTab($event)"
                    class="resetTab active text-sm w-full text-center py-2 inner-shadow cursor-pointer">{{ 'Email' | translate}}</div>
                <div id="phoneNoTab" (click)="resetPasswordTab($event)"
                    class="resetTab text-sm w-full text-center py-2 inner-shadow cursor-pointer">{{'Phone Number' | translate}}</div>
            </div>

            <!-- email tab -->
            <form *ngIf="defaultResetTab === 'emailTab'" #resetPasswordForm="ngForm">
                <div class="border flex justify-between items-center p-2 rounded-sm mb-4 relative">
                    <input name="email" ngModel class="outline-none font-medium text-sm w-full" type="email"
                        placeholder="{{'Enter Your Email' | translate}}" (keydown.enter)="resetPassword(resetPasswordForm.value)" type="text">
                </div>
                <div class="gap-4 flex">
                    <button (click)="OpenforgotPassword()"
                        class="w-2/5 p-2 text-sm rounded-sm text-gray-500 border">{{"Cancel" | translate}}</button>
                    <button class="w-3/5 bg-primary hover:bg-blue-900 text-white p-2 text-sm rounded-sm"
                        (click)="resetPassword(resetPasswordForm.value)" >{{"Send recovery link" | translate}}</button>
                </div>
            </form>

            <!-- phone number tab -->
            <form *ngIf="defaultResetTab === 'phoneNoTab'" #resetPasswordForm="ngForm">
                <div class="border flex justify-between items-center rounded-sm mb-4 relative">
                    <div class="flex gap-3">
                        <select name="phoneCode"
                        class="w-24 md:w-24 px-1 py-2 h-10 text-gray-500 font-medium  2xl:w-1/4  bg-white text-sm border-r-0 placeholder:font-xs focus:outline-none ng-pristine ng-valid ng-touched">

                        <option (click)="getPhoneCode(country.phonecode)" name="phoneCode" [ngValue]="country.is_active"
                            *ngFor="let country of countries"
                            [ngClass]="{'hidden': country.is_active =='No'}"><span> {{country.flag }}</span>
                            +{{country.phonecode}}</option>
                    </select>
                    <div
                    class="block mt-2 h-0 bottom-7 rtl:right-36 rtl:sm:right-52 rtl:md:right-64 rtl:xl:right-32 left-36 sm:left-52 md:left-64 xl:left-32 rtl:2xl:right-48 2xl:left-48 w-1 text-gray-200">
                    <p>|</p>
                </div>
                        <input name="phoneNo" ngModel class="outline-none font-medium text-sm w-full p-2" type="text"
                            placeholder="{{'Enter Your Number' | translate}}" type="text"  (keydown.enter)="resetPassword(resetPasswordForm.value)" >
                    </div>
                </div>
                <div class="gap-4 flex">
                    <button (click)="OpenforgotPassword()"
                        class="w-2/5 p-2 text-sm rounded-sm text-gray-500 border">{{"Cancel" | translate}}</button>
                    <button class="w-3/5 bg-primary hover:bg-blue-900 text-white p-2 text-sm rounded-sm"
                        (click)="resetPassword(resetPasswordForm.value)">{{"Send recovery link" | translate}}</button>
                </div>
            </form>
        </div>
    </div>
</div>


<!--  check mail model  -->
<!--                                           -->


<div *ngIf="checkMailModel">
    <div (click)=" OpencheckMail()" class="bg-overlay fixed inset-0 z-[99]"></div>
    <div class="z-[100] mx-auto fixed top-1/2 left-[5%] sm:left-1/2 sm:-translate-x-1/2 -translate-y-1/2 bg-white w-[90%] sm:w-92 rounded-xl ">
        <button (click)="OpencheckMail()" type="button"
        class="flex rtl:mr-auto ltr:ml-auto px-4 hover:cursor-pointer duration-200 ease-in-out hover:opacity-75  mt-4">
        <img src="../../../assets/icons/close-icon.png" alt="" class="w-5 lg:w-6">
        <span class="text-accent ">{{"Close" | translate}}</span>
    </button>
        <div class="py-8 px-10 border-b border-gray-300">
            <div class="h-14 mb-2">
                <img class="block mx-auto h-full w-full" src="../../../assets/icons/logo-blue.svg" alt="">
            </div>
            <div class="h-32 w-32 mx-auto">
                <img class="block mx-auto h-full w-full" src="../../../assets/images/boy-phone.png" alt="">
            </div>
            <p class="text-lg font-extrabold text-center py-3">{{"Check in your mail" | translate}} !</p>
            <p class="text-[0.65rem] text-center text-gray-500 font-semibold px-3 ">
                {{"We've just sent you a link to reset password.This link will be valid for 72 hours! If appear within a few minutes check your spam folder."
                | translate}}</p>
        </div>
        <div class="pt-4 pb-8 px-10">
            <p class="text-[0.65rem] text-center py-2 px-16 text-gray-500">
                {{"For any question or problems please email us at" | translate}}</p>
            <p class="text-xs font-semibold text-center text-primary">{{"<EMAIL>"}}</p>

        </div>
    </div>
</div>


<!--  Reset your password  and confirmation -->
<!--                                                        -->


<!-- <div class="bg-overlay fixed inset-0 z-[99]"></div>
<div class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 pt-6 pb-9 px-10 bg-white w-92 rounded-xl">
    <div >
        <div class="h-14 mb-2">
            <img class="block mx-auto h-full w-full" src="../../../assets/icons/logo-blue.svg" alt="">
        </div>
        <div class="h-32 w-32 mx-auto">
            <img class="block mx-auto h-full w-full" src="../../../assets/images/boy-phone.png" alt="">
        </div>
        <p class="text-lg font-extrabold text-center py-3">{{"Reset your password"}}</p>
        <p class="text-[0.65rem] text-center text-gray-500 font-semibold px-4 pb-3  mb-2">{{"set new password for your account so you can login and access all the features. "}}</p>
        <form  >
            <div class="border flex justify-between items-center p-2 rounded-sm mb-4 relative">
                <input name="otpField"  class="outline-none text-sm w-full"  placeholder="Enter Your Email" type="text">
                
            </div>
           
            <button class="w-full bg-primary hover:bg-blue-900 text-white p-2 text-sm rounded-sm">{{"Save"}}</button>
            
        </form>
    </div>
</div>


<div  
    class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-10 bg-white w-[26rem] rounded-xl">
    <div class="">
        <div class="h-16 w-16 mx-auto">
            <img class="block mx-auto h-full w-full" src="../../../assets/images/finish.webp" alt="">
        </div>
        <p class="text-lg font-bold text-center text-primary">{{"Password Changed !"}}</p>
        <p class="text-xs text-center pb-10">{{"Your password has been changed succeefully, Now you'll use new password to get your account"}}</p>
        <button class="w-11/12 ml-4 bg-primary hover:bg-blue-900 text-white p-2 rounded-sm" >{{"Back to Login"}}</button>
    </div>
</div> -->



<!--  terms and condition -->
<!--                                                        -->


<div *ngIf="TOSModel">
    <div (click)="OpenTOSModel()" class="bg-overlay fixed inset-0 z-[99]"></div>
    <div
        class="z-[100] mx-auto  fixed overflow-y-auto max-h-[90vh] scroll-thin top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-10 py-5 bg-white w-11/12 md:w-4/5 lg:w-2/5 xl:w-2/5 rounded-xl">
        <div>
            <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b">
                <h5 class="text-xl sm:text-2xl font-bold leading-normal ">
                    {{"Terms & Condition" | translate}}
                </h5>
                <button (click)="OpenTOSModel()" type="button"
                    class="flex items-center gap-1 hover:cursor-pointer duration-200 ease-in-out hover:opacity-75 ">
                    <img src="../../../assets/icons/close-icon.png" alt="" class="w-5 lg:w-6">
                    <span class="text-accent ">{{"Close" | translate}}</span>
                </button>
            </div>
            <div>
                <p class="text-black text-sm font-semibold font-oxygen py-3">{{"Must Read" | translate}}</p>
                <p [innerHTML]="TOS" class="h-88 overflow-y-auto "></p>
            </div>



            <div class="py-6">
                <button (click)="OpenTOSModel()"
                    class="w-5/6 bg-primary hover:bg-blue-900 text-white p-2 mx-6  lg:mx-5 xl:mx-6 2xl:mx-8 rounded-sm">
                    {{"Yes I Agree" | translate}}</button>
            </div>


        </div>
    </div>
</div>

<div *ngIf="PPModel">
    <div (click)="OpenPPModel()" class="bg-overlay fixed inset-0 z-[99]"></div>
    <div
        class="z-[100] mx-auto fixed overflow-y-auto max-h-[90vh] scroll-thin top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-10 py-5 bg-white w-11/12 md:w-4/5 lg:w-2/5 xl:w-2/5 rounded-xl">
        <div>
            <div class="modal-header flex flex-shrink-0 items-center justify-between pb-2  rounded-t-md border-b">
                <h5 class="text-xl sm:text-2xl font-bold leading-normal ">
                    {{"Privacy Policy" | translate}}
                </h5>
                <button (click)="OpenPPModel()" type="button"
                    class="flex items-center gap-1 hover:cursor-pointer duration-200 ease-in-out hover:opacity-75 ">
                    <img src="../../../assets/icons/close-icon.png" alt="" class="w-5 lg:w-6 ">
                    <span class="text-accent ">{{"Close" | translate}}</span>
                </button>
            </div>
            <div>
                <p class="text-black text-sm font-semibold font-oxygen py-3">{{"Must Read" | translate}}</p>
                <p [innerHTML]="PP" class="h-88 overflow-y-auto "></p>


            </div>



            <div class="py-6">
                <button (click)="OpenPPModel()"
                    class="w-5/6 bg-primary hover:bg-blue-900 text-white p-2 mx-6  lg:mx-5 xl:mx-6 2xl:mx-8 rounded-sm">
                    {{"Yes I Agree" | translate}}</button>
            </div>


        </div>
    </div>
</div>

<!-- reset password form -->
<!-- <div class="bg-overlay fixed inset-0 z-[99]"></div>
<div class="z-[100] mx-auto fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 p-10 bg-white w-92 rounded">
    <div>
        <div class="h-14">
            <img class="block mx-auto h-full w-full" src="../../../assets/icons/logo-blue.svg" alt="">
        </div>
        <div class="h-28 w-3/4 mx-auto mt-5  mb-5">
            <img class="block mx-auto h-full w-full object-cover object-top"
                src="../../../assets/images/passwordsave.svg" alt="">
        </div>
        <p class="text-base font-bold text-center">{{"Reset your password"}}</p>
        <p class="text-xs py-4 text-center mb-2">{{"Set new password for your account so you can login and access all the features."}}
        </p>

        <input type="text" class="border border-primary px-2 focus:outline-none h-8 w-full rounded-sm text-sm mb-5 placeholder:text-sm placeholder:text-[#ACACAC]" placeholder="Password">

        <button class="w-full bg-primary border border-primary hover:bg-blue-900 duration-150 text-white p-2 rounded-sm col-span-7 ">{{"Save"}}</button>
        
    </div>
</div> -->


<!-- <app-reset-password></app-reset-password> -->
<!-- <app-reset-password-link></app-reset-password-link> -->
<!-- <app-reset-password-save></app-reset-password-save> -->
<!-- <app-forgot-password-changed></app-forgot-password-changed> -->