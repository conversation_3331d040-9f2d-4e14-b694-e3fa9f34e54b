import { Component, ElementRef, OnInit, Renderer2, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { UserAuthenticationService } from 'src/app/Services/user-authentication.service';
import { UserTypesService } from 'src/app/Services/user-types.service';
import { UserService } from 'src/app/Services/User/user.service';
import { SocialAuthService } from "@abacritt/angularx-social-login";
import { FacebookLoginProvider } from "@abacritt/angularx-social-login";
import { from } from 'rxjs';
import { CmsService } from 'src/app/Services/Cms/cms.service';
import { TranslateService } from '@ngx-translate/core';
import { AddressHelper } from 'src/app/globalFunctions/addressHelpers';
import { HttpClient } from '@angular/common/http';
import { AddressService } from 'src/app/Services/Address/address.service';
import { JwtHelperService } from '@auth0/angular-jwt';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})

export class LoginComponent implements OnInit {
  constructor(
    private renderer: Renderer2,
    private userTypesService: UserTypesService,
    private toastr: ToastrService,
    private userAuthenticationService: UserAuthenticationService,
    private configSettings: configSettings,
    private router: Router,
    private userService: UserService,
    private authService: SocialAuthService,
    private cmsService: CmsService,
    private translateService: TranslateService,
    private addressService: AddressService,
    public addressHelper: AddressHelper,
    public http: HttpClient,

  ) { }

  // emailPattern: any = /[a-z0-9]+@[a-z]+\.[a-z]{2,3}/;
  emailPattern: any = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,4}$/;

  numberRegex = /^[0-9][6,12]+$/

  userTypes: any
  forgotPasswordModel = false
  checkMailModel = false
  TOSModel = false
  PPModel = false

  TOS: any
  PP: any
  lang = this.configSettings.getLang();


  countries: any

  jwtHelper = new JwtHelperService();

  @ViewChild('googleBtn') googleBtnRef: ElementRef


  passwordVisibleOption: string = 'password'
  ConfirmPasswordVisibleOption: string = 'password'

  ngOnInit(): void {
    // this.toastr.error('ertyu')
    this.configSettings.setShowLoader(false)
    this.getUserTypes()
    this.getCountries()
    this.authService.authState.subscribe((res) => {
      const postParams = {
        social_register_type: "F",
        first_name: res.firstName,
        last_name: res.lastName,
        email: res.email,
      }
      this.socialLogin(postParams)

    })

    this.getCms('TC')
    this.getCms('PP')
    // this.toastr.success('Hello world!', 'Toastr fun!');
  }

  getCountries() {
    const getParams = {}

    this.addressService.getCountry(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.countries = response.body.data
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }


  activeTab: string = "resale"
  changeTab(event: any) {
    Array.from(document.querySelectorAll('.tab')).forEach(function (el) {
      el.classList.remove('active');
    });
    this.renderer.addClass(event.target, 'active')
    if (event.target.id !== this.activeTab) {
      this.activeTab = event.target.id
    }
  }

  getUserTypes() {
    this.userTypesService.userTypes().subscribe(res => {
      this.userTypes = res.data
    })
  }

  getPhoneCode(code: any) {
    this.phoneCode = code
  }

  getFormData(registerForm: any) {

    if (registerForm.userType === "") {
      // this.toastr.error('', 'Please Select User Type ');
      this.translateService.get('Please select user type').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    if (registerForm.firstName === "") {
      // this.toastr.error('', 'Please enter your first name ');
      this.translateService.get('Please enter your first name').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    if (registerForm.lastName === "") {
      // this.toastr.error('', 'Please enter your last name');
      this.translateService.get('Please enter your last name').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    // check if its a valid email
    if (registerForm.email === '') {
      // this.toastr.error('', 'Please enter your email address');
      this.translateService.get('Please enter your email address').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    // check if its a valid email
    if (!registerForm.email.match(this.emailPattern)) {
      // this.toastr.error('', 'Enter a valid E-mail');
      this.translateService.get('Enter a valid e-mail').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    if (this.phoneCode === "") {
      // this.toastr.error('', 'Choose a phone code');
      this.translateService.get('Choose a phone code').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    if (registerForm.phone === "") {
      // this.toastr.error('', 'Enter Phone Number');
      this.translateService.get('Enter phone number').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    if (!registerForm.phone.match(/^-?(0|[1-9]\d*)?$/)) {
      // this.toastr.error('', 'Special characters are not allowed in phone number');
      this.translateService.get('Special characters are not allowed in phone number').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }


    if (registerForm.phone.length > 10) {
      this.translateService.get('Phone number cannot be longer than 10 number').subscribe(res => {
        this.toastr.error('', res);
      })
    }



    // password atleast 8 charactes
    if (registerForm.password.length < 8 || registerForm.password.length > 16) {
      // this.toastr.error('', 'Password should be between 8-16 characters');
      this.translateService.get('Password should be between 8-16 characters').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    if (registerForm.password !== registerForm.confirmPassword) {
      // this.toastr.error('', "Passwords don't match");
      this.translateService.get("Password does not match").subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    if (!registerForm.tos) {
      // this.toastr.error('', "Please agree to our terms of service");
      this.translateService.get('Please agree to our terms of service').subscribe(res => {
        this.toastr.error('', res);
      })

      // this.configService.toggleLoading(false);
      return
    }



    let params = {

      user_type_id: registerForm.userType,
      first_name: registerForm.firstName,
      last_name: registerForm.lastName,
      email: registerForm.email,
      phone: registerForm.phone,
      phone_code: this.phoneCode,
      password: registerForm.password,
      company_name: "",
      device_type: 'W',
      device_model: "",
      app_version: "",
      os_version: "",
      device_token: "",
      country_id: "",
      area_id: "",
      street: "",
      flat: "",
      building: "",
      zip_code: "",
      company_logo: "",
      portfolio: "",
      project_photo: "",

    }

    this.configSettings.setShowLoader(true) // <--- add the loader before the api function call

    this.userAuthenticationService.register(params).subscribe({
      next: (response) => {
        if (response.status === 200) {

          this.configSettings.setLocalStorage('userDetails', response.data)
          this.configSettings.setUserTypes()
          // setTimeout(() => {                      // <--- settimeout is removed this is so wrong please stop using it
            this.configSettings.setShowLoader(false) // make the loader stop on the response (make sure to add it in failed 
          // }, 1000)                                // response too)

          this.configSettings.setIsUserLoggedIn(true)
          this.configSettings.setIsOTPVerified(false)


if (response.data?.user_types) {
  
  response.data.user_types.forEach((ele: any) => {
    if (ele.type == 'AG') {
      this.configSettings.setUserType(ele.type)
    } else if (ele.type == 'AR') {
      this.configSettings.setUserType(ele.type)
    } else {
      this.configSettings.setUserType('UR')
    }
  });
}

          // }
        } else {
          this.configSettings.setShowLoader(false)
          this.toastr.error('', response.message);
        }
      },
      error: (err) => {
        this.configSettings.setShowLoader(false)
        this.toastr.error('', err.message);
      }
    })



    // .subscribe(res => {
    //   if (res.success) {
    //     // this.configSettings.setIsUserLoggedIn(true)
    //     // this.toastr.success('', res.message);
    //     this.configSettings.setLocalStorage('userDetails', res.data)
    //     this.configSettings.setUserTypes()
    //     this.configSettings.setIsUserLoggedIn(true)
    //     // this.router.navigate(['/home']).then(() => { window.location.reload() })

    //     //this is to manage settings toggle just like apps
    //     this.configSettings.setIsOTPVerified(false)
    //     res.data.user_types.forEach((ele: any) => {
    //       if (ele.type == 'AG') {
    //         console.log(ele.type);
    //         this.configSettings.setUserType(ele.type)
    //       } else if (ele.type == 'AR') {
    //         this.configSettings.setUserType(ele.type)
    //       } else {
    //         this.configSettings.setUserType('UR')
    //       }
    //     });


    //   }
    // })

  }

  getLoginData(loginForm: any) {

    if (loginForm.email === "") {
      // this.toastr.error('', 'Enter email');
      // this.configService.toggleLoading(false);
      this.translateService.get('Please enter email address').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }
    if (!loginForm.email.match(this.emailPattern)) {
      this.translateService.get('Enter valid email address').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    if (loginForm.password === "") {
      // this.toastr.error('', 'Enter password');
      this.translateService.get('Please enter password').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }

    let params = {
      email: loginForm.email,
      password: loginForm.password,
    }

    this.userAuthenticationService.login(params).subscribe(res => {
      if (res.success) {

        // this.router.navigate(['/home']).then(() => { window.location.reload() });
      }
    })

    this.userAuthenticationService.login(params).subscribe({
      next: (response) => {
        if (response.status === 200) {

          // if (response.body.status == 200) {

          this.configSettings.setIsUserLoggedIn(true)
          this.userService.setUser(true)
          this.configSettings.setLocalStorage('userDetails', response?.data)
          this.configSettings.setUserTypes()
          if (response.data.is_phone_verified == 0) {
            this.configSettings.setIsOTPVerified(false)
          }
          else {
            this.router.navigate(['/home'])
          }
          //this is to manage settings toggle just like apps
          this.configSettings.setUserType('UR')


        } else {
          this.toastr.error(response?.message)
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });

  }

  resetPassword(data: any) {
    if (data.email === "") {
      // this.toastr.error('', 'Please enter Email address');
      this.translateService.get('Please enter email address').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }
    if (data.phoneNo === "") {
      // this.toastr.error('', 'Please enter Email address');
      this.translateService.get('Please enter phone number').subscribe(res => {
        this.toastr.error('', res);
      })
      // this.configService.toggleLoading(false);
      return
    }
    if (!data.email.match(this.emailPattern)) {
      this.translateService.get('Enter valid email address').subscribe(res => {
        this.toastr.error('', res);
      })
      return
    }

    const getParams = {

    }

    const postParams = {

      "email": data.email
      // "phone_code":data.phoneCode,
      // "phone":data.phone
    }
    console.log(postParams);


    this.configSettings.setShowLoader(true)

    this.userAuthenticationService.forgotPassword(getParams, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            // console.log(response.body.data)
            this.forgotPasswordModel = false
            this.checkMailModel = true
            this.configSettings.setShowLoader(false)
            // this.toastr.success('', response.body.message);
          }
        }
        if (response.body.status === 404) {
          this.translateService.get('Email address does not exist').subscribe(res => {
            this.configSettings.setShowLoader(false)
            this.toastr.error('', res);
          })
          return
        }

      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }

  open: boolean = false
  dropDown() {
    this.open = !this.open
  }

  defaultPhone: any = "965"
  // '<img _ngcontent-ggp-c68=\"\" src=\"../../../assets/icons/kw_flag.png\" alt=\"\" class=\"w-6 h-4\"><span _ngcontent-ggp-c68=\"\" id=\"+965\">+965</span>'
  phoneCode: any = '+965'

  option(e: any) {
    this.phoneCode = e.currentTarget.id
    this.defaultPhone = e.currentTarget.innerHTML
  }

  loginWithFacebook() {
    from(this.authService.signIn(FacebookLoginProvider.PROVIDER_ID)).subscribe(res => {

      const getParams = {}
      const postParams = {
        social_register_type: "F",
        first_name: res.firstName,
        last_name: res.lastName,
        email: res.email,
      }

      this.socialLogin(postParams)

    })
  }

  socialLogin(postParams: any) {

    this.userAuthenticationService.socialLoginAndRegister({}, postParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.configSettings.setLocalStorage('userDetails', response.body.data)
            this.configSettings.setIsUserLoggedIn(true)
            this.configSettings.setUserTypes()
            this.router.navigate(['/home'])

            // this.configSettings.setIsOTPVerified(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })
  }



  getCms(type: string) {

    let getParams = {
      type: type
    }

    this.cmsService.getCms(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            if (type == 'TC') {
              this.TOS = response.body.data.contents['content_' + this.lang]
            } else {
              this.PP = response.body.data.contents['content_' + this.lang]
            }
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      }
    })

  }


  OpenPPModel() {
    if (this.PPModel = !this.PPModel) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

  }

  OpenTOSModel() {
    if (this.TOSModel = !this.TOSModel) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

  }

  OpenforgotPassword() {
    if (this.forgotPasswordModel = !this.forgotPasswordModel) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    this.defaultResetTab = 'emailTab'
  }

  OpencheckMail() {
    if (this.checkMailModel = !this.checkMailModel) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

  }



  defaultResetTab: string = 'emailTab'
  resetPasswordTab(event: any) {
    Array.from(document.querySelectorAll('.resetTab')).forEach(function (el) {
      el.classList.remove('active');
    });
    this.renderer.addClass(event.target, 'active')
    if (event.target.id !== this.defaultResetTab) {
      this.defaultResetTab = event.target.id
    }
  }


  signInWithApple() {
    const CLIENT_ID = "com.app.Bandar.backend"
    const REDIRECT_API_URL = "https://dev-cp.bandarestate.com/api/v1/apple-social-auth"
    window.open(
      `https://appleid.apple.com/auth/authorize?client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(
        REDIRECT_API_URL
      )}&response_type=code id_token&scope=name email&response_mode=form_post`,
      '_blank'
    );

    window.addEventListener('message', event => {
      const decodedToken = this.jwtHelper.decodeToken(event.data.id_token);
      let requestData = {}
      if (event.data.user) {
        const userName = JSON.parse(event.data.user);
        requestData = {
          "email": decodedToken.email,
          "name": `${userName.name.firstName} ${userName.name.lastName}`,
          "socialId": decodedToken.sub,
        };
      } else {
        requestData = {
          "email": decodedToken.email,
          "socialId": decodedToken.sub,
        };
      }
      console.log(`User Data : ${requestData}`);
      // do your next stuff here
    });
  };
}