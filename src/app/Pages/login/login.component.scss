.tab{
    background-color: white;
    color: #002349;
}

.tab.active{
    background-color: #002349;
    color: white;
}




.scroll-thin::-webkit-scrollbar
{
	display: none;
	scrollbar-width: none;
	-ms-overflow-style: none;
	background-color: #F5F5F5;
}


::-webkit-scrollbar {
    width: 3px;
  }
  
  /* Track */
  ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 3px #f1f1f1; 
    border-radius: 10px;
  }
   
  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #d9dce1; 
    border-radius: 10px;
  }
  
  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #f6e0ac; 
  }



// .login-image{
// 	// background:white;
//     background: linear-gradient(to bottom, #ffffff 0%, #ffffff 50%, #000000 100%);
// }

.login-image{
    background-image: linear-gradient(180deg, rgba(238, 238, 238, 0) 0%, rgba(255, 255, 255, 0) 55%, rgb(0, 35, 73) 100%), url('../../../assets/images/home-page-banner.jpg') ;
    background-size: cover;
}

.mt-l{
    margin-left: -6px;
    // filter: grayscale(90%);
    filter:  invert(0) sepia(1) saturate(0.1) hue-rotate(0deg) brightness(4)
}
.mt-l2{
    margin-left: -20px;

}

.resetTab{
    background-color: white;
    color: #002349;
}

.resetTab.active{
    background-color: #002349;
    color: white;
}


.inner-shadow {
    box-shadow: inset 0 0 2.25px black;
  }

  input[type=password]::-ms-reveal,
input[type=password]::-ms-clear
{
    display: none;
}