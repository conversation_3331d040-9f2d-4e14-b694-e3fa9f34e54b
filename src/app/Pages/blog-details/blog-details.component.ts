import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { configSettings } from 'src/app/Config/config.settings';
import { BlogCommentsService } from 'src/app/Services/Blogs/Blog-comments/blog-comments.service';
import { BlogsService } from 'src/app/Services/Blogs/blogs.service';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-blog-details',
  templateUrl: './blog-details.component.html',
  styleUrls: ['./blog-details.component.scss'],
})
export class BlogDetailsComponent implements OnInit {
  isUserLoggedIn: boolean;
  isLoggedIn: boolean;
  constructor(
    private activatedRoute: ActivatedRoute,
    private blogsService: BlogsService,
    private toastr: ToastrService,
    private blogCommentsService: BlogCommentsService,
    private configSettings: configSettings,
    private translateService: TranslateService,
  ) { }

  blogDetailID: string;
  blogDetail: any;
  blogComments: any;
  @ViewChild('textfield') textfieldRef: ElementRef;

  ngOnInit(): void {
    this.loginDetail()

    this.configSettings.setShowLoader(true)

    this.activatedRoute.params.subscribe((param) => {
      this.blogDetailID = param['id'];
    });

    this.blogDetails(this.blogDetailID);

    this.blogCommentsService.getComments().subscribe((res) => {
      this.blogComments = res;
      // console.log(this.blogComments);
    });
   
    }

  blogDetails(id: any) {
    const getParams = {
      blog_id: id,
    };

    this.blogsService.blogsDetails(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.blogDetail = response.body.data;
            // this.blogComments = response.body.data.comments
            this.blogCommentsService.setComments(response.body.data.comments);
            this.configSettings.setShowLoader(false)
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }


  loginDetail(){
    this.configSettings.getIsUserLoggedIn().subscribe(res => {
      this.isUserLoggedIn = res
      this.isLoggedIn = res    
    })
  }

  inputField: any;
  postComment(data: any) {
    if ( this.isLoggedIn == false) {
      this.translateService.get('Please login first').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
    if (data.postCommentField === '') {
      // this.toastr.error('', 'Enter a comment to post');
      this.translateService.get('Enter a comment to post').subscribe(res =>{
        this.toastr.error('', res);
      })
      return;
    }
   

    const getParams = {};

    const posttParams = {
      blog_id: this.blogDetailID,
      parent_comment_id: '',
      user_id: this.configSettings.getUserDetails().user_id,
      comment: data.postCommentField,
    };

    this.blogCommentsService.blogComments(getParams, posttParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status === 200) {
            this.toastr.success('', response.body.message);
            this.blogCommentsService.setComments(response.body.data.comments);
            this.inputField = '';
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  isReadMore = true

  showText() {
    this.isReadMore = !this.isReadMore
  }

  isCommentMore = false

  showComment() {
    this.isCommentMore = !this.isCommentMore
  }

}
