<div *ngIf="blogDetail" class="container mx-auto px-4 sm:px-14 md:px-48 mt-5">
    <p><span [routerLink]="['/blogs']" class="text-[#2979CF] text-xs cursor-pointer">{{"Blogs" | translate}}</span>
        <span class="text-white text-xs opacity-40 px-3">></span> <span
            class="text-white text-xs opacity-40">{{blogDetail?.title}}</span>
    </p>
    <h2 class="text-white text-xl lg:text-2xl font-semibold mt-6 mb-4">{{blogDetail?.title}}</h2>
    <p class="flex text-[#56708E] text-xs"><img src="../../../assets/icons/calendericon.png" class="mr-2"
            alt="">{{blogDetail?.created_at}}</p>

    <div class="pb-8">
        <div class="mt-8">
            <img [src]="blogDetail?.banner" class="lg:h-[37rem] object-cover w-full " alt="">
        </div>
        <div [ngClass]="{'h-20 overflow-hidden': isReadMore == true}" [innerHTML]="blogDetail?.description"
            class="text-[#B1BBC6] mt-5 h-full"></div>
        <div class="w-fit my-4 mx-auto block ">
            <button *ngIf="isReadMore == true" type="button" (click)="showText()"
                class="px-8 rounded-full text-xs text-amber-400 py-1.5 border-2 border-amber-300">
                {{"Read More" | translate}}
                <span></span>
            </button>
            <button *ngIf="isReadMore == false" type="button" (click)="showText()"
                class=" px-8 rounded-full text-xs text-amber-400 py-1.5 border-2 border-amber-300">
                {{"Read Less" | translate}}
            </button>
            <span class="w-fit relative bottom-[1.35rem] mx-4 block h-0 rtl:right-[70%] ltr:left-[70%]">
                <img class="w-3" [ngClass]="isReadMore == false ? '-scale-y-100' : ''"
                    src="../../../assets/icons/gold-down arrow.svg" alt="">
            </span>
        </div>

        <div class="mt-10">
            <h1 class="text-2xl lg:text-4xl text-white pb-3">{{"Comments" | translate}}</h1>
            <h4 class="text-[#FCFCFC] font-semibold text-base lg:text-2xl pt-1 pb-6">{{"Leave Your Comment" |
                translate}}</h4>
            <form #postCommentForm="ngForm" (ngSubmit)="postComment(postCommentForm.value)">
                <textarea name="postCommentField" id="" ngModel [value]="inputField"
                    class="placeholder:text-[#ACACAC] text-base resize-none block w-full lg:w-3/5 bg-[#91919170] p-3 text-white focus:outline-none h-36"
                    placeholder="{{'Write your comment here....' | translate}}"></textarea>
                <button class="bg-accent py-2 px-10 text-white text-sm mt-5">{{"Post Comment" | translate}}</button>
            </form>

        </div>


        <div class="mt-10">



            <div *ngFor="let mainComment of blogComments  let i = index" [ngClass]="{'hidden': i>=3}"
                class=" w-full gap-4 border-b border-white py-10">
                <!-- <div class="w-[4%]">
                    <img [src]="mainComment.profile_img" class="w-full lg:w-12 lg:h-12 rounded-full inline-block"
                        alt="">
                </div>
                <div class="w-[96%]">
                    <p class="text-white text-base font-semibold inline-block"><span
                            class="text-[#56708E] ml-4 text-xs">{{mainComment.created_at}}</span></p>
                    <p class="text-white text-xs mt-3">{{mainComment.comments}}</p>
                    <p class="text-accent text-sm mt-2 cursor-pointer">Reply</p>

                    <div class="flex gap-4 mt-8">
                        <div class="w-[4%]">
                            <img src="../../../assets/images/architectslogo.jpg"
                                class="w-full lg:w-12 lg:h-12 rounded-full inline-block" alt="">
                        </div>
                        <div class="w-[96%]">
                            <p class="text-white text-base font-semibold inline-block">John Doe <span
                                    class="text-[#56708E] ml-4 text-xs">Sep 10, 2021</span></p>
                            <p class="text-white text-xs mt-3">Lorem ipsum dolor sit amet, consectetur adipiscing
                                elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad
                                minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                                commodo consequat. Duis aute irure dolor in reprehenderit.</p>
                            <p class="text-accent text-sm mt-2 cursor-pointer">Reply</p>
                        </div>
                    </div>
                </div> -->
                <app-comments [mainComment]="mainComment" [blogDetailID]="blogDetailID"></app-comments>

            </div>
            <div *ngIf="isCommentMore">
                <div *ngFor="let mainComment of blogComments  let i = index" [ngClass]="{'hidden': i<=3}"
                    class=" w-full gap-4 border-b border-white py-10">
                    <div>

                        <app-comments [mainComment]="mainComment" [blogDetailID]="blogDetailID"></app-comments>
                    </div>


                </div>





            </div>
            <div class=" flex justify-center my-5" *ngIf="isCommentMore == false && blogComments.length>3" type="button" (click)="showComment()">
                <button class="px-8  rounded-full text-xs text-amber-400 py-1.5 border-2 border-amber-300">
                    {{"View All Comments" | translate}}
                    <span></span>
                </button>
            </div>
            <div class=" flex justify-center my-5" *ngIf="isCommentMore == true" type="button" (click)="showComment()">
                <button class="px-8  rounded-full text-xs text-amber-400 py-1.5 border-2 border-amber-300">
                    {{"View Less Comments" | translate}}
                    <span></span>
                </button>
            </div>
        </div>
    </div>