import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { FooterComponent } from './Layout/footer/footer.component';
import { HeaderComponent } from './Layout/header/header.component';
import { ToastrModule } from 'ngx-toastr';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { LoginComponent } from './Pages/login/login.component';
import { PropertyListingComponent } from './Pages/property-listing/property-listing.component';
import { PropertyCardComponent } from './Components/property-card/property-card.component';
import { HttpClientModule, HttpClient, HttpClientJsonpModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { OtpVerificationComponent } from './Components/otp-verification/otp-verification.component';
import { ProfileDropdownComponent } from './Components/profile-dropdown/profile-dropdown.component';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { WriteAReviewComponent } from './Components/write-a-review/write-a-review.component';
import { GuidesDropdownComponent } from './Components/guides-dropdown/guides-dropdown.component';
import { ResetPasswordComponent } from './Components/reset-password/reset-password.component';
import { ResetPasswordLinkComponent } from './Components/reset-password-link/reset-password-link.component';
import { ResetPasswordSaveComponent } from './Components/reset-password-save/reset-password-save.component';
import { ForgotPasswordChangedComponent } from './Components/forgot-password-changed/forgot-password-changed.component';
import { LoaderComponent } from './Components/loader/loader.component';
import { ConfirmationPageComponent } from './Pages/confirmation-page/confirmation-page.component';



@NgModule({
  declarations: [
    AppComponent,
    FooterComponent,
    HeaderComponent,
    // PropertyListingComponent,
    // PropertyCardComponent,
    OtpVerificationComponent,
    ProfileDropdownComponent,
    // WriteAReviewComponent,
    GuidesDropdownComponent,
    ResetPasswordComponent,
    ResetPasswordLinkComponent,
    ResetPasswordSaveComponent,
    ForgotPasswordChangedComponent,
    LoaderComponent,
    ConfirmationPageComponent,

  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    FormsModule,
    ToastrModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }

// required for AOT compilation
export function HttpLoaderFactory(http: HttpClient): TranslateHttpLoader {
  return new TranslateHttpLoader(http);
}