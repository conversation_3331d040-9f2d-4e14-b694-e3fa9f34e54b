import { Injectable } from "@angular/core";
import { ToastrService } from "ngx-toastr";


@Injectable({
    providedIn: 'root',
  })

  export class ValidationHelpers {
    constructor(
      private toastr: ToastrService
    ) {}
  
    public amenities: any = [];
    public features: any = [];
  

    // validate phone
    phoneErr: any = false;
    validatePhone(event: any) {
        
      const pattern = /[0-9\+\-\ ]/;
      let inputChar = String.fromCharCode(event.charCode);
      if (event.keyCode != 7 && !pattern.test(inputChar)) {
        this.phoneErr = true;
        event.preventDefault();
      } else {
        this.phoneErr = false;
      }
    }
  
  }  