import { Injectable } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { PropertyListingService } from "../Services/Properties/property-listing.service";


@Injectable({
    providedIn: 'root',
  })

  export class AmenitiesHelper {
    constructor(
      private propertyListingService : PropertyListingService,
      private toastr: ToastrService
    ) {}
  
    public amenities: any = [];
    public features: any = [];
  
    getPropertyAmenities(value: string) {

      const getParams = {
        type: value
      };
      this.propertyListingService.getPropertyAmenities(getParams).subscribe({
        next: (response) => {
          if (response.status === 200) {
            if (response.body.status == 200) {
                value == 'A' ? this.amenities = response?.body?.data : this.features = response?.body?.data  
            }
          }
        },
        error: (err) => {
          this.toastr.error('', err.error.message);
        },
      });
    }
  
  }  