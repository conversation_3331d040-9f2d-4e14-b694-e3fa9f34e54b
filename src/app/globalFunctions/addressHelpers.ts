import { Injectable } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { Observable } from "rxjs";
import { AddressService } from "../Services/Address/address.service";

@Injectable({
  providedIn: 'root',
})
export class AddressHelper {
  constructor(
    private addressService: AddressService,
    private toastr: ToastrService
  ) {}

  public countries: any = [];
  public states: any = [];
  public areas: any = [];

  getCountry() {
    const getParams = {};
    this.addressService.getCountry(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.countries = response?.body?.data;            
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  getState(value: any) {
    const getParams = {
        country_id: value
    };
    this.addressService.getState(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.states = response?.body?.data; 
                       
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }

  getAreas(value: any) {
    const getParams = {
        state_id: value
    };
    this.addressService.getAreas(getParams).subscribe({
      next: (response) => {
        if (response.status === 200) {
          if (response.body.status == 200) {
            this.areas = response?.body?.data;            
          }
        }
      },
      error: (err) => {
        this.toastr.error('', err.error.message);
      },
    });
  }
}