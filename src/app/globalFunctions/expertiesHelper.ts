import { Injectable } from "@angular/core";
import { ToastrService } from "ngx-toastr";
import { ExpertiseService } from "../Services/Expertise/expertise.service";


@Injectable({
    providedIn: 'root',
  })


  export class ExpertiseHelper {
    constructor(
      private expertiseService : ExpertiseService,
      private toastr: ToastrService
    ) {}

    public expertise: any = [];

    getExpertise() {

        const getParams = {
         
        };
        this.expertiseService.getExpertise(getParams).subscribe({
          next: (response) => {
            if (response.status === 200) {
              if (response.body.status == 200) {
               this.expertise = response?.body?.data 
              }
            }
          },
          error: (err) => {
            this.toastr.error('', err.error.message);
          },
        });
      }

  }