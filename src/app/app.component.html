<div [dir]="direction" class="min-h-screen flex flex-col items-stretch w-full font-light tracking-[0.5px] leading-5"
  [ngClass]="lang === 'en'?'font-oxygen':'font-cairo'">
  <div class="sticky top-0 z-50">
    <app-header></app-header>
  </div>
  <main class="flex-grow h-full bg-primary relative">
    <router-outlet></router-outlet>
    <app-otp-verification *ngIf="!isOTPVerfied" (closePopup)="isOTPVerfied=true"></app-otp-verification>
    <app-reset-password *ngIf="resetPassword"></app-reset-password>
    <app-loader class=" fixed inset-0 pointer-events-none z-[101]" *ngIf="showLoader == true"></app-loader>
  </main>
  <app-footer></app-footer>
</div>