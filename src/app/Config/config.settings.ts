import { HttpClient } from "@angular/common/http";
import { EventEmitter, Injectable, Output } from "@angular/core";
import { BehaviorSubject, Subject } from "rxjs";

@Injectable({
  providedIn: 'root',
})
export class configSettings {
  isLoggedIn(arg0: boolean) {
    throw new Error('Method not implemented.');
  }
  isloggedIn(arg0: boolean) {
    throw new Error('Method not implemented.');
  }
  constructor(private http: HttpClient) {

    if (localStorage.getItem('lang')) {
      this._lang = localStorage.getItem('lang');
    } else {
      this._lang = 'en';
    }



    if (localStorage.getItem('_currency')) {
      this._currency = localStorage.getItem('_currency');
    } else {
      this._currency = 'KW';
    }

    if (localStorage.getItem('_currency_name')) {
      this._currency_name = localStorage.getItem('_currency_name');
    } else {
      this._currency_name = 'KD';
    }
    // this._loading_status = false;
  }

  // baserouteAPI = 'http://admin.elevator-cms.test/api/v1/'
  baserouteAPI = 'https://dev-cp.bandarestate.com/api/v1/'
  // isOTPVerified = true
  isOTPVerified = new BehaviorSubject(true);
  // isUserLoggedIn?: any = false

  setLang(lang?: any) {
    localStorage.setItem('lang', lang);
  }

  _lang: any;
  _currency: any;
  _currency_name: any;
  getLang() {
    let lang = localStorage.getItem('lang');
    if (lang == null || lang == undefined) {
      localStorage.setItem('lang', 'en');
      return 'en';
    } else {
      return lang as string;
    }
  }

  setLocalStorage(key: any, values: any) {
    localStorage.setItem(key, JSON.stringify(values));
  }

  getLocalStorage(key: any) {
    let data = localStorage.getItem(key)
    if (data) {
      return JSON.parse(data);
    }
  }

  getUserDetails() {
    let data = localStorage.getItem('userDetails')
    if (data) {
      return JSON.parse(data)
    }
  }

  getUserID() {
    let data = localStorage.getItem('userDetails')
    if (data) {
      return JSON.parse(data).user_id
    }
  }

  setUserDetails(value: any) {
    localStorage.setItem('userDetails', JSON.stringify(value));
  }

  setAgentDetails(value: any) {
    sessionStorage.setItem('agentDetails', JSON.stringify(value));
  }

  getAgentDetails() {
    let data = sessionStorage.getItem('agentDetails')
    if (data) {
      return JSON.parse(data)
    }
  }

  clearAgent() {
    sessionStorage.removeItem('agentDetails')
  }

  getIsOTPVerified() {
    return this.isOTPVerified.asObservable()
  }

  setIsOTPVerified(data: any) {
    this.isOTPVerified.next(data)
  }


  isUserLoggedIn = new BehaviorSubject(false)
  checkLocalStorageLoggedIn: any

  setIsUserLoggedIn(value: any) {
    // this.isUserLoggedIn = value
    localStorage.setItem('isUserLoggedIn', JSON.stringify(value));
    this.isUserLoggedIn.next(value)
  }

  getIsUserLoggedIn() {

    this.checkLocalStorageLoggedIn = localStorage.getItem('isUserLoggedIn');
    if (this.checkLocalStorageLoggedIn === null || this.checkLocalStorageLoggedIn === undefined) {
      this.setIsUserLoggedIn(false)
      this.isUserLoggedIn.next(JSON.parse(this.checkLocalStorageLoggedIn))
      return this.isUserLoggedIn.asObservable()
    } else {
      this.isUserLoggedIn.next(JSON.parse(this.checkLocalStorageLoggedIn))
      return this.isUserLoggedIn.asObservable()
    }
  }

  resetPassword = new BehaviorSubject(false);

  getResetPassword() {
    return this.resetPassword.asObservable()
  }

  setResetPassword(data: any) {
    this.resetPassword.next(data)
  }


  getSearchData() {
    let data = localStorage.getItem('searchData')
    if (data) {
      return JSON.parse(data)
    }
  }

  setSearchData(value: any) {
    localStorage.setItem('searchData', JSON.stringify(value));
  }

  clearSearch() {
    localStorage.removeItem('searchData')
  }



  showLoader = new BehaviorSubject<any>(false);

  setShowLoader(data: boolean) {
    this.showLoader.next(data)
  }

  getShowLoader() {
    return this.showLoader.asObservable()
  }
  setWhatsAppNo(whatsAppNo?:any){
    sessionStorage.setItem('whatsAppNo',JSON.stringify(whatsAppNo));
}
getWhatsAppNo(){
    return JSON.parse(sessionStorage.getItem('whatsAppNo')!);
}

  // loading = false;
  // @Output() load: EventEmitter<boolean> = new EventEmitter<boolean>();

  // toggleLoading(key: boolean) {
  //   this.loading = key;
  //   this.load.emit(this.loading)
  // }


  searchType = new BehaviorSubject(0);  // 1 for is_searched true and will save the search. 0 wont save the search it

  setSearchType(data: any) {
    this.searchType.next(data)
  }

  getSearchType() {
    return this.searchType.asObservable()
  }


  menuType = new BehaviorSubject('');

  setMenuType(data: any) {
    this.menuType.next(data)
  }

  getMenuType() {
    return this.menuType.asObservable()
  }

  userTypesUpdated = new BehaviorSubject('')

  setUserTypes() {
    this.userTypesUpdated.next('')
  }

  getUserTypes() {
    return this.userTypesUpdated.asObservable()
  }

  mapDetails = new BehaviorSubject('');

  setMapDetails(data: any) {
    this.mapDetails.next(data)
  }

  getMapDetails() {
    return this.mapDetails.asObservable()
  }

  public userTypeAgent: boolean = false;
  public userTypeUser: boolean = false;
  public userTypeArchitect: boolean = false;
  setUserType(userType: any) {
    sessionStorage.setItem('userType', JSON.stringify(userType))
  }
  getUserType() {
    return JSON.parse(sessionStorage.getItem('userType')!)
  }

  setNotificationCount(count: number) {
    localStorage.setItem('notificationCount', JSON.stringify(count));
  }

  getNotificationCount() {
    let count = localStorage.getItem('notificationCount')
    if (count) {
      return JSON.parse(count)
    }
  }

  dingNotification = new BehaviorSubject(1)

  tiggerNotificationAPi() {
    this.dingNotification.next(1)
  }

  callNotificationAPi() {
    return this.dingNotification.asObservable()
  }

  // for architect listing when clicking "architect >" to take u to the last architect listing page the user was on

  lastCurrentPage = new BehaviorSubject(1)

  setLastCurrentPage(pageNumber: number) {
    this.lastCurrentPage.next(pageNumber)
  }

  getLastCurrentPage() {
    return this.lastCurrentPage.asObservable()
  }









}


