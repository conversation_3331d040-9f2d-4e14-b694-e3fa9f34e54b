<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Bander</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- <link rel="icon" type="image/x-icon" href="./assets/icons/b-logo.png"> -->
  <link rel="apple-touch-icon-precomposed" sizes="57x57" href="./assets/favicons/apple-touch-icon-57x57.png" />
  <link rel="apple-touch-icon-precomposed" sizes="114x114" href="./assets/favicons/apple-touch-icon-114x114.png" />
  <link rel="apple-touch-icon-precomposed" sizes="72x72" href="./assets/favicons/apple-touch-icon-72x72.png" />
  <link rel="apple-touch-icon-precomposed" sizes="144x144" href="./assets/favicons/apple-touch-icon-144x144.png" />
  <link rel="apple-touch-icon-precomposed" sizes="60x60" href="./assets/favicons/apple-touch-icon-60x60.png" />
  <link rel="apple-touch-icon-precomposed" sizes="120x120" href="./assets/favicons/apple-touch-icon-120x120.png" />
  <link rel="apple-touch-icon-precomposed" sizes="76x76" href="./assets/favicons/apple-touch-icon-76x76.png" />
  <link rel="apple-touch-icon-precomposed" sizes="152x152" href="./assets/favicons/apple-touch-icon-152x152.png" />
  <link rel="icon" type="image/png" href="./assets/favicons/favicon-196x196.png" sizes="196x196" />
  <link rel="icon" type="image/png" href="./assets/favicons/favicon-96x96.png" sizes="96x96" />
  <link rel="icon" type="image/png" href="./assets/favicons/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="./assets/favicons/favicon-16x16.png" sizes="16x16" />
  <link rel="icon" type="image/png" href="./assets/favicons/favicon-128.png" sizes="128x128" />
  <meta name="application-name" content="./assets/favicons/&nbsp;" />
  <meta name="msapplication-TileColor" content="./assets/favicons/#FFFFFF" />
  <meta name="msapplication-TileImage" content="./assets/favicons/mstile-144x144.png" />
  <meta name="msapplication-square70x70logo" content="./assets/favicons/mstile-70x70.png" />
  <meta name="msapplication-square150x150logo" content="./assets/favicons/mstile-150x150.png" />
  <meta name="msapplication-wide310x150logo" content="./assets/favicons/mstile-310x150.png" />
  <meta name="msapplication-square310x310logo" content="./assets/favicons/mstile-310x310.png" />

  <script
    id="mcjs">!function (c, h, i, m, p) { m = c.createElement(h), p = c.getElementsByTagName(h)[0], m.async = 1, m.src = i, p.parentNode.insertBefore(m, p) }(document, "script", "https://chimpstatic.com/mcjs-connected/js/users/86d0e380ac78a9a42159f5384/7a1c7bfd0def6bc335495c343.js");</script>

</head>

<body>
  <app-root></app-root>
</body>

</html>