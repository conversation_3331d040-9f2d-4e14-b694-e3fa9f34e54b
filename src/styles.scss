/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

@import 'ngx-owl-carousel-o/lib/styles/scss/owl.carousel';
@import 'ngx-owl-carousel-o/lib/styles/scss/owl.theme.default';
@import "~@ng-select/ng-select/themes/default.theme.css";
// breaks html page
// html{
//    scroll-behavior: smooth;
// }

@import 'swiper/scss';
@import 'swiper/scss/navigation';
@import 'swiper/scss/pagination';
@import 'swiper/scss/thumbs';
@import 'swiper/scss/free-mode';

@media only screen and (min-device-width: 0px) and (max-device-width: 1024px) {
   .owl-carousel .owl-item img {
      width: auto;
      margin: auto;
   }


   .featuredProperty .owl-carousel .owl-item img {
      margin: 0;
      width: 100%;
   }
}

.searchCarousal .owl-carousel .owl-item img {
   width: 100%;
   aspect-ratio: 1;
}

.featuredProperty .owl-carousel .owl-stage {
   padding: 2.5rem 0;
}

.featuredProperty .owl-carousel .owl-item .location-icon {
   height: 1.5rem;
   width: 1.5rem;
}

.ng-select .ng-arrow-wrapper {
   display: none;
}

.ng-select .ng-clear-wrapper {
   right: 18px;
}





.downarrow {
   position: relative;
   height: 0;
   top: 27px;
   z-index: 2 !important;
   // right: px;
   display: block;
}

.downarrow img {
   // display: block;
   // text-align: right;
   // margin: 0 90%;
   position: absolute;
   right: 10px;
   width: 10px;
}

.search-arrow img {
   margin: 0 95%;
}

.ng-select .ng-dropdown-panel {
   z-index: 10;
}

.owl-carousel .owl-item img {
   margin: 0;
}

@media only screen and (max-width: 767px) {
   .search-arrow img {
      margin: 0 90%;
   }
}

.blue-gradient {
   // #071778
   // #004a9c
   // background: linear-gradient(58deg, rgba(7,23,120,1) 0%, rgba(0,74,156,1) 100%);
   background: linear-gradient(58deg, rgba(7, 23, 80, 1) 0%, rgba(7, 23, 100, 1) 54%, rgba(0, 74, 156, 1) 100%);
}

.bannerCarousal .owl-carousel .owl-item img {
   width: 100%;
   height: 50vh;
   object-fit: cover;
}

@media (min-width: 768px) {
   .bannerCarousal .owl-carousel .owl-item img {
      width: 100%;
      height: 70vh;

   }
}

.ngx-pagination a {
   padding: 0 !important;
}

.ngx-pagination .current {
   padding: 0 !important;
}

.projects-pagination .ngx-pagination li {
   // padding: 3rem 1rem;
   margin-inline-end: .25rem;
   background-color: #ececec;
   // border: 1px solid #ececec !important;

   color: black;
}

.ngx-pagination{
   display: flex;
   justify-content: center;
}

.ngx-pagination li {
   margin: 0;
}

.projects-pagination .ngx-pagination li a {
   // padding: 1rem;
   background-color: #ececec;

   color: black;
   font-size: .7rem;
   padding: .6rem .9rem !important;
   // height: 30px;
   // width: 30px;
   // display: flex;
   // justify-content: center;
   // align-items: center;
}

.projects-pagination .ngx-pagination li.current {
   padding: .6rem .9rem !important;
   // padding: 0.1875rem 0.625rem;
   // height: 33px;
   // width: 33px;
   // display: flex;
   // justify-content: center;
   // align-items: center;
   background-color: #b0d4fa !important;
   border: 1px solid #054a98 !important;
   font-size: .7rem;

}

.projects-pagination .ngx-pagination li.pagination-previous,
.projects-pagination .ngx-pagination li.pagination-next {
   padding: 1rem 1rem;
   background-color: rgba(255, 255, 255, 0);
   display: none;
}
.featuredArchitects .owl-carousel .owl-item img {
   width: 100%;
   
}

.featuredArchitects .owl-carousel .owl-item img.logo {
   width: 5rem;
   height: 5rem;
}

.areaGuidePropertyCarousal .owl-carousel .owl-item {
   // width: 150px !important;
   height: 300px;
}

.areaGuidePropertyCarousal .owl-carousel .owl-item img {
   width: auto;
}

.areaGuidePropertyCarousal .owl-carousel .owl-item img.logo {
   width: 3.5rem;
   height: 3.5rem;
}


.youMayalsoLikeCarousal .owl-carousel .owl-item img {
   width: 100%;
}


.youMayalsoLikeCarousal .owl-carousel .owl-item img.logo {
   width: 3.5rem;
   height: 3.5rem;
}

.youMayalsoLikeCarousal .owl-carousel .owl-item img.pin {
   width: 0.5rem;
}

// .ng-select.enter-location {
// }



// property details carousal
.galleryCarousal .owl-carousel .owl-item {
   // width: fit-content !important;
   height: auto;
   // width: 100%;
}

.galleryCarousal .owl-carousel .owl-item .img {
   object-fit: cover;
}





// 
.ng-select.property-listing-dropdown .ng-select-container {
   z-index: 1 !important;
   background-color: #082a51;
   border-radius: 0;
   border-color: #ABBED4;
   color: white;
   outline: none;
   height: 40px;
}

.ng-select.property-listing-dropdown .ng-dropdown-panel {
   // z-index: 1 !important;

}



.ng-select.register-dropdown .ng-select-container {
   background-color: white;
   border-radius: 0;
   border-color: #f5f5f5;
   color: black;
   outline: none;
   height: 40px;
   width: 6rem;
}

.ng-select.register-dropdown .ng-dropdown-panel {
   width: 6rem;

}

.ng-select.ng-select-single.register-dropdown .ng-select-container .ng-value-container,
.ng-select.ng-select-single.register-dropdown .ng-select-container .ng-value-container .ng-value {
   overflow: visible;
}

.post-property-dropdownNew {
   background-color: #f5f5f5;
   border-radius: 0;
   border-color: #f5f5f5;
   color: black;
   outline: none;
   height: 44px;
   padding-left: 10px;
   padding-right: 50px;
   z-index: 1 !important;
   overflow-y: auto !important;
   width: 100%;

}

select {
   -webkit-appearance: none;
   -moz-appearance: none;
   text-indent: 1px;
   text-overflow: '';
}



.ng-select.post-property-dropdown .ng-select-container {
   background-color: #f5f5f5;
   border-radius: 0;
   border-color: #f5f5f5;
   color: black;
   outline: none;
   max-height: 44px;
   height: 44px;
   z-index: 1 !important;
   overflow-y: auto;
   // cursor: not-allowed;
}
.ng-select .ng-select-container{
   flex-wrap: wrap !important;
}

.ng-select.post-property-dropdown .ng-dropdown-panel {
   // z-index: 5 !important;

}

.ng-select.ng-select-single.post-property-dropdown .ng-select-container .ng-value-container .ng-input,
.ng-select.ng-select-single.post-property-dropdown .ng-select-container .ng-value-container .ng-input input {
   // cursor: not-allowed !important;
}

.ng-select.ng-select-focused:not(.ng-select-opened)>.ng-select-container {
   border-color: #cccccc;
   box-shadow: inset 0 0px 0px rgba(0, 0, 0, 0), 0 0 0 0px rgba(0, 128, 255, 0);
}

// home dropdown styles
.ng-select.home-dropdown .ng-select-container {
   background-color: white;
   border-radius: 10px;
   border-color: #ABBED4;
   color: black;
   outline: none;
   height: 3.5rem;
   z-index: 1 !important;
}

.ng-select.home-dropdown .ng-input,
.ng-select.home-dropdown input,
.ng-select.home-dropdown .ng-value {
   height: 100%;

}

.ng-select.home-dropdown .ng-dropdown-panel {
   // z-index: 1 !important;

}

.ng-select.home-dropdown .ng-select-container .ng-value-container .ng-input {
   top: 0;
}



.featured-post-dropdown .ng-placeholder {
   font-size: 0.7rem;
}

.owl-carousel,
.bx-wrapper {
   direction: ltr;
}

// .owl-carousel .owl-item { direction: rtl; }

[dir="rtl"] .owl-carousel .owl-item {

   direction: rtl;
}

[dir="ltr"] .owl-carousel .owl-item {

   direction: ltr;
}



.ng-select.architect-expertise .ng-select-container {
   z-index: 1 !important;
   border-radius: 0;
   color: white;
   border-radius: 2px;
   outline: none;
   height: 42px;
   overflow-y: auto !important;
}

.ng-select.architect-select .ng-select-container {
   z-index: 1 !important;
   border-radius: 0;
   // color: white;
   border-radius: 2px;
   outline: none;
   min-height: 42px;
}


.ng-select.architect-expertise .ng-dropdown-panel {
   // z-index: 1 !important;

}

.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
   top: 25%;
}



.ng-select.architect-register-enterprise .ng-select-container {
   z-index: 1 !important;
   border-radius: 2px;
   color: white;
   outline: none;
   min-height: 37px;
   border-color: #0369a1
}

.ng-select.architect-register-enterprise .ng-select-container .ng-placeholder {
   font-size: 15px;
   color: black;

}

.ng-select.architect-register-enterprise .ng-dropdown-panel {
   // z-index: 1 !important;

}

// .gm-style-iw .gm-ui-hover-effect button {

//    display: none !important;
// }

.gm-style-iw.gm-style-iw-c .gm-ui-hover-effect {
   display: none !important;
}

.googlebtn {
   // width: 100%;
   // height: 100%;
   opacity: 0;
   // border: 1px red solid;
   z-index: 2;
   // position: relative;
   cursor: pointer;
}

// .googlebtn iframe{
//    width: 100% !important;
//    height: 100% !important;
// }

// .googlebtn iframe #container{
//    width: 100% !important;
//    height: 100% !important;
// }

.textSlide {
   margin-right: 0.75rem;
   margin-left: 0.75rem;
}

.textSlide .swiper-slide{
   width: 10% !important;
   opacity: .5;
}

.textSlide .swiper-slide.swiper-slide-thumb-active {
   opacity: 1 !important;
}

// feature post border
.pac-container {
   display: none !important;
}

// .owl-theme .owl-dots{
//    width: 70px;
//    display: flex;
//    flex-wrap: nowrap;
//    margin-left: auto;
//    margin-right: auto;
//    overflow-x: scroll;
// }
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
   background: white !important;
   opacity: 1;
   
 }
 .owl-theme .owl-dots .owl-dot span {
   width: 10px !important;
   height: 10px !important;
   border: 2px solid white;
   background: transparent !important;
   opacity: 0.5;
 }
 .owl-theme .owl-nav.disabled+.owl-dots {
   margin-top: 25px !important;
 }