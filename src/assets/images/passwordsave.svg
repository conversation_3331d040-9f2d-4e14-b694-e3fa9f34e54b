<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="315" height="148" viewBox="0 0 315 148">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_21286" data-name="Rectangle 21286" width="315" height="148" rx="5" transform="translate(894.17 317.768)" fill="#2979cf" stroke="#002349" stroke-linecap="round" stroke-linejoin="round" stroke-width="3"/>
    </clipPath>
  </defs>
  <g id="Mask_Group_69" data-name="Mask Group 69" transform="translate(-894.17 -317.768)" clip-path="url(#clip-path)">
    <circle id="Ellipse_87" data-name="Ellipse 87" cx="102" cy="102" r="102" transform="translate(939.17 390.768)" fill="#002349" opacity="0.06"/>
    <g id="Group_101944" data-name="Group 101944" transform="translate(232.308 160.288)">
      <g id="Background_Complete" data-name="Background Complete" transform="translate(693.543 246.439)">
        <g id="Group_101955" data-name="Group 101955" transform="translate(0 0)">
          <g id="Group_101944-2" data-name="Group 101944">
            <path id="Path_31608" data-name="Path 31608" d="M734.25,360.99c8.181-6.694,9.24-17.952,8.081-27.9-.194-1.661-.153-3.432-1.074-4.826s-2.876-2.239-4.329-1.413c-1.2.685-1.634,2.169-2.237,3.416a9.855,9.855,0,0,1-3.651,4.056,3.815,3.815,0,0,1-3.438.595c-1.52-.673-1.854-2.642-1.979-4.3q-.349-4.631-.7-9.263a15.272,15.272,0,0,0-.909-4.877,5.175,5.175,0,0,0-3.555-3.2,3.177,3.177,0,0,0-3.736,2.411,15.192,15.192,0,0,0,.121,2.066,1.75,1.75,0,0,1-.9,1.725,2.051,2.051,0,0,1-1.905-.608,22.065,22.065,0,0,1-4.493-5.638,30.54,30.54,0,0,0-4.239-5.85,9.127,9.127,0,0,0-6.426-2.9,5.455,5.455,0,0,0-5.271,4.117c-.418,2.385,1.156,4.64,2.813,6.406a37.322,37.322,0,0,0,8.4,6.694,3.068,3.068,0,0,1,1.418,1.277c.454,1.074-.516,2.258-1.59,2.712a19.8,19.8,0,0,1-3.88.758,3.8,3.8,0,0,0-3.063,2.207c-.548,1.75,1.031,3.406,2.538,4.452a32.282,32.282,0,0,0,9.938,4.627,10.922,10.922,0,0,1,3.666,1.4,3.05,3.05,0,0,1,1.282,3.429,3.2,3.2,0,0,1-3.331,1.588,22.578,22.578,0,0,1-3.8-1.085c-2.376-.669-5.341-.266-6.656,1.822a5.2,5.2,0,0,0-.186,4.68,12.08,12.08,0,0,0,2.878,3.883,32.349,32.349,0,0,0,14.557,8.42c5.5,1.389,10.5,1.476,15.654-.88" transform="translate(-693.543 -304.481)" fill="#ebebeb"/>
          </g>
          <g id="Group_101946" data-name="Group 101946" transform="translate(2.307 1.648)">
            <g id="Group_101945" data-name="Group 101945">
              <path id="Path_31609" data-name="Path 31609" d="M736.562,369.517A72.188,72.188,0,0,0,732.7,356.1a147.953,147.953,0,0,0-7.275-15.481l-2.118-3.961c-.7-1.3-1.39-2.586-2.146-3.8a74.323,74.323,0,0,0-4.748-6.752c-1.617-2.075-3.227-3.986-4.746-5.763s-2.974-3.4-4.361-4.828a59.644,59.644,0,0,0-7.043-6.248c-.888-.671-1.592-1.17-2.075-1.5l-.55-.375c-.125-.083-.192-.12-.192-.12s.057.051.177.141l.536.394c.472.346,1.168.857,2.044,1.539a62.256,62.256,0,0,1,6.975,6.293c1.376,1.431,2.816,3.062,4.33,4.84s3.114,3.695,4.721,5.77a74.609,74.609,0,0,1,4.722,6.737c.751,1.213,1.436,2.491,2.135,3.794l2.117,3.96a150.835,150.835,0,0,1,7.282,15.447,73.233,73.233,0,0,1,3.9,13.364" transform="translate(-697.444 -307.269)" fill="#e0e0e0"/>
            </g>
          </g>
          <g id="Group_101948" data-name="Group 101948" transform="translate(25.944 8.794)">
            <g id="Group_101947" data-name="Group 101947">
              <path id="Path_31610" data-name="Path 31610" d="M737.435,337.777a3.976,3.976,0,0,0,.1-.721c.047-.465.095-1.14.143-1.974.1-1.669.155-3.977.214-6.523s.156-4.849.29-6.511c.064-.831.124-1.5.172-1.968a4.015,4.015,0,0,0,.046-.725,3.988,3.988,0,0,0-.143.713c-.079.461-.166,1.133-.253,1.964-.18,1.663-.306,3.972-.364,6.521-.054,2.523-.1,4.807-.14,6.521-.025.8-.047,1.463-.063,1.976A4.063,4.063,0,0,0,737.435,337.777Z" transform="translate(-737.422 -319.355)" fill="#e0e0e0"/>
            </g>
          </g>
          <g id="Group_101950" data-name="Group 101950" transform="translate(4.06 24.882)">
            <g id="Group_101949" data-name="Group 101949">
              <path id="Path_31611" data-name="Path 31611" d="M700.41,346.7a.793.793,0,0,0,.225.017l.643,0c.558,0,1.365,0,2.362.025,1.993.041,4.745.2,7.771.508s5.754.717,7.718,1.068c.983.168,1.774.327,2.322.434l.631.121a.776.776,0,0,0,.224.025.727.727,0,0,0-.213-.075l-.621-.166c-.542-.137-1.329-.322-2.311-.514-1.959-.4-4.69-.827-7.725-1.142s-5.8-.44-7.8-.436c-1-.007-1.808.023-2.366.054l-.642.045A.75.75,0,0,0,700.41,346.7Z" transform="translate(-700.41 -346.564)" fill="#e0e0e0"/>
            </g>
          </g>
          <g id="Group_101952" data-name="Group 101952" transform="translate(37.288 22.012)">
            <g id="Group_101951" data-name="Group 101951">
              <path id="Path_31612" data-name="Path 31612" d="M756.61,369.8a1.436,1.436,0,0,0,.121-.274l.3-.8c.26-.7.61-1.716,1.026-2.979.836-2.526,1.884-6.05,2.991-9.954s2.126-7.433,2.918-9.97l.95-3c.1-.334.19-.6.256-.817a1.319,1.319,0,0,0,.073-.291,1.361,1.361,0,0,0-.121.274c-.077.208-.177.474-.3.8-.26.7-.609,1.716-1.026,2.979-.837,2.526-1.884,6.05-2.991,9.955s-2.126,7.431-2.919,9.969l-.95,3c-.105.334-.19.6-.256.818A1.307,1.307,0,0,0,756.61,369.8Z" transform="translate(-756.608 -341.711)" fill="#e0e0e0"/>
            </g>
          </g>
          <g id="Group_101954" data-name="Group 101954" transform="translate(7.878 43.343)">
            <g id="Group_101953" data-name="Group 101953">
              <path id="Path_31613" data-name="Path 31613" d="M706.867,377.79a1.546,1.546,0,0,0,.3.087l.857.2,3.152.72c2.66.611,6.325,1.5,10.372,2.492s7.722,1.855,10.394,2.422c1.337.284,2.419.5,3.171.642l.866.155a1.447,1.447,0,0,0,.306.037,1.383,1.383,0,0,0-.3-.087l-.856-.2-3.151-.72c-2.661-.611-6.326-1.5-10.374-2.492s-7.721-1.854-10.393-2.421c-1.336-.284-2.419-.5-3.171-.642l-.866-.155A1.364,1.364,0,0,0,706.867,377.79Z" transform="translate(-706.867 -377.787)" fill="#e0e0e0"/>
            </g>
          </g>
        </g>
        <g id="Group_101963" data-name="Group 101963" transform="translate(195.419 2.098)">
          <path id="Path_31614" data-name="Path 31614" d="M1071.395,308.573s-2.77-2.745-8.179,4.11a101.07,101.07,0,0,0-7.76,10.963s-1.346,1.394-2.236.13-1.8-4.528-3.776-4.366c-2.046.168-3.114,2.2-3.378,5.45s-1.1,10.344-1.18,10.985-.614,1.473-1.358.709-4.6-7.275-7.584-5.259-2.228,11.186-2.165,12.19-.2,1.374-.7,1.52-1.591-2.166-2.764-3.973-2.851-3.7-4.473-2.39-3.338,7.734.75,19.541l2.252,5.347,5.77.821c12.455,1,18.247-2.266,19.107-4.164s-1.389-3.049-3.433-3.733-4.554-1.162-4.538-1.682.307-.873,1.3-1.062,10.054-1.756,11.26-5.146-6.064-5.5-6.99-6.027-.244-1.261.346-1.492,7.261-2.824,10.34-3.892,4.78-2.611,4.431-4.634c-.337-1.951-3.726-2.019-5.172-2.564s-.433-2.2-.433-2.2A101.132,101.132,0,0,0,1069.5,317.5c5.283-6.953,1.839-8.981,1.839-8.981" transform="translate(-1024.056 -308.03)" fill="#ebebeb"/>
          <g id="Group_101956" data-name="Group 101956" transform="translate(0.564 6.674)">
            <path id="Path_31615" data-name="Path 31615" d="M1066.929,319.318a.769.769,0,0,1-.094.15l-.3.422-1.183,1.6c-1.032,1.39-2.542,3.388-4.42,5.849-3.754,4.922-9.005,11.67-14.838,19.1l-14.91,19.043-4.5,5.788-1.23,1.569-.326.4a.718.718,0,0,1-.119.131.779.779,0,0,1,.095-.15l.3-.422,1.183-1.6c1.032-1.39,2.542-3.388,4.42-5.848,3.754-4.922,9.006-11.671,14.841-19.1l14.908-19.04,4.5-5.788,1.23-1.569.326-.4A.786.786,0,0,1,1066.929,319.318Z" transform="translate(-1025.009 -319.318)" fill="#e0e0e0"/>
          </g>
          <g id="Group_101957" data-name="Group 101957" transform="translate(26.453 15.998)">
            <path id="Path_31616" data-name="Path 31616" d="M1068.825,335.087a7.321,7.321,0,0,1,.219,1.549c.088.961.167,2.292.219,3.761.038,1.414.073,2.692.1,3.762a9.674,9.674,0,0,1,0,1.561,6.637,6.637,0,0,1-.216-1.55c-.079-.962-.14-2.293-.193-3.762-.048-1.412-.093-2.69-.13-3.759A8.569,8.569,0,0,1,1068.825,335.087Z" transform="translate(-1068.797 -335.087)" fill="#e0e0e0"/>
          </g>
          <g id="Group_101958" data-name="Group 101958" transform="translate(27.523 25.792)">
            <path id="Path_31617" data-name="Path 31617" d="M1070.606,352.695c-.007-.085,2.726-.381,6.1-.663a61.4,61.4,0,0,1,6.131-.357,122.215,122.215,0,0,1-12.235,1.021Z" transform="translate(-1070.606 -351.652)" fill="#e0e0e0"/>
          </g>
          <g id="Group_101959" data-name="Group 101959" transform="translate(17.712 38.23)">
            <path id="Path_31618" data-name="Path 31618" d="M1054.012,372.7a8.971,8.971,0,0,1,1.575.252c.966.183,2.3.438,3.776.673s2.823.416,3.8.542a9.116,9.116,0,0,1,1.575.253,7.482,7.482,0,0,1-1.6-.037c-.983-.072-2.338-.215-3.824-.455s-2.818-.527-3.774-.765A7.509,7.509,0,0,1,1054.012,372.7Z" transform="translate(-1054.012 -372.688)" fill="#e0e0e0"/>
          </g>
          <g id="Group_101960" data-name="Group 101960" transform="translate(14.606 28.643)">
            <path id="Path_31619" data-name="Path 31619" d="M1048.765,356.475a7.057,7.057,0,0,1,.573,1.359c.317.852.734,2.036,1.178,3.35s.828,2.51,1.092,3.38a7.036,7.036,0,0,1,.367,1.428,7,7,0,0,1-.573-1.359c-.317-.852-.734-2.036-1.178-3.351s-.828-2.509-1.092-3.38A7.026,7.026,0,0,1,1048.765,356.475Z" transform="translate(-1048.759 -356.475)" fill="#e0e0e0"/>
          </g>
          <g id="Group_101961" data-name="Group 101961" transform="translate(4.667 37.61)">
            <path id="Path_31620" data-name="Path 31620" d="M1031.954,371.641a11.583,11.583,0,0,1,.722,1.745c.409,1.091.954,2.605,1.538,4.284s1.1,3.2,1.453,4.314a11.433,11.433,0,0,1,.517,1.816,11.456,11.456,0,0,1-.723-1.745c-.409-1.091-.954-2.605-1.538-4.284s-1.1-3.2-1.453-4.313A11.493,11.493,0,0,1,1031.954,371.641Z" transform="translate(-1031.949 -371.641)" fill="#e0e0e0"/>
          </g>
          <g id="Group_101962" data-name="Group 101962" transform="translate(8.882 49.441)">
            <path id="Path_31621" data-name="Path 31621" d="M1039.079,391.66a14.479,14.479,0,0,1,2,.265c1.232.191,2.933.456,4.818.7s3.6.44,4.836.575a14.519,14.519,0,0,1,2,.267,11.991,11.991,0,0,1-2.023-.05c-1.246-.079-2.967-.235-4.857-.487s-3.591-.549-4.816-.8A12.028,12.028,0,0,1,1039.079,391.66Z" transform="translate(-1039.079 -391.65)" fill="#e0e0e0"/>
          </g>
        </g>
      </g>
      <g id="Character" transform="translate(723.123 162.48)">
        <g id="Group_102017" data-name="Group 102017" transform="translate(0)">
          <g id="Group_101981" data-name="Group 101981" transform="translate(0 63.957)">
            <path id="Path_31622" data-name="Path 31622" d="M792.623,316.081,774.769,298.15l8.976-16.487c8.977-11.49,27.976-10.75,27.976-10.75a108.909,108.909,0,0,1,28.023,1.692c15.092,2.937,13.876,12.925,13.876,12.925l2.022,37,2.168,30.124-55.666,1.122-9.522-37.692" transform="translate(-756.324 -270.652)" fill="#002349"/>
            <path id="Path_31623" data-name="Path 31623" d="M809.858,365.4,760,366.007a16.207,16.207,0,0,1-14.848-23.139l21.318-39.536,19.418,13.127-13.245,27.491,40.362.72Z" transform="translate(-743.573 -284.009)" fill="#031c37"/>
            <path id="Path_31624" data-name="Path 31624" d="M905.117,285.659l40.01,61.774-23.342,1.527s-13.939-21.968-13.624-21.513C908.731,328.269,905.117,285.659,905.117,285.659Z" transform="translate(-809.603 -276.786)" fill="#031c37"/>
            <path id="Path_31625" data-name="Path 31625" d="M802.288,351.976" transform="translate(-767.572 -303.892)" fill="#e8505b"/>
            <g id="Group_101964" data-name="Group 101964" transform="translate(34.728 7.608)">
              <path id="Path_31626" data-name="Path 31626" d="M808.895,311.91a1.258,1.258,0,0,1,.056-.318c.057-.238.127-.534.212-.9.184-.777.453-1.9.735-3.3s.579-3.071.783-4.942a26.073,26.073,0,0,0,.021-6.092,16.044,16.044,0,0,0-2.065-5.687,22.939,22.939,0,0,0-2.97-4,28.21,28.21,0,0,0-2.422-2.343,12.033,12.033,0,0,1-.935-.816,1.3,1.3,0,0,1,.272.173c.169.125.43.3.739.551a23.475,23.475,0,0,1,2.505,2.286,22.062,22.062,0,0,1,3.056,4,16,16,0,0,1,2.126,5.787,25.474,25.474,0,0,1-.044,6.167,49.8,49.8,0,0,1-.852,4.957c-.31,1.4-.612,2.515-.833,3.284-.11.384-.2.681-.268.881A1.29,1.29,0,0,1,808.895,311.91Z" transform="translate(-802.309 -283.52)" fill="#263238"/>
            </g>
            <g id="Group_101965" data-name="Group 101965" transform="translate(29 38.129)">
              <path id="Path_31627" data-name="Path 31627" d="M803.331,335.139a5.668,5.668,0,0,1-.344.894c-.239.567-.6,1.382-1.048,2.385-.9,2.007-2.189,4.761-3.668,7.775s-2.872,5.718-3.906,7.66c-.517.971-.942,1.753-1.245,2.289a5.646,5.646,0,0,1-.5.818,5.655,5.655,0,0,1,.391-.873c.3-.6.687-1.385,1.158-2.334l3.824-7.7,3.747-7.731,1.137-2.344A5.629,5.629,0,0,1,803.331,335.139Z" transform="translate(-792.621 -335.139)" fill="#263238"/>
            </g>
            <g id="Group_101966" data-name="Group 101966" transform="translate(17.686 59.678)">
              <path id="Path_31628" data-name="Path 31628" d="M822.084,393.295a.325.325,0,0,1,0-.1c.006-.082.015-.181.024-.3.029-.287.068-.679.118-1.178.116-1.05.281-2.549.488-4.436.441-3.821,1.063-9.206,1.8-15.549l.14.157-14.728.013-25.767-.045-7.808-.049-2.123-.029-.552-.015a.095.095,0,1,1,0-.032l.552-.015,2.123-.028,7.808-.049,25.767-.045,14.728.013h.159l-.02.157c-.778,6.338-1.438,11.718-1.907,15.536l-.567,4.426c-.069.5-.122.888-.162,1.174l-.047.3A.375.375,0,0,1,822.084,393.295Z" transform="translate(-773.485 -371.586)" fill="#263238"/>
            </g>
            <g id="Group_101967" data-name="Group 101967" transform="translate(21.226 53.437)">
              <path id="Path_31629" data-name="Path 31629" d="M787.667,367.954a14.187,14.187,0,0,1-1-1.246,19.433,19.433,0,0,0-5.8-4.9,14.219,14.219,0,0,1-1.395-.772,5.832,5.832,0,0,1,1.5.579,15.983,15.983,0,0,1,5.877,4.963A5.828,5.828,0,0,1,787.667,367.954Z" transform="translate(-779.472 -361.03)" fill="#263238"/>
            </g>
            <g id="Group_101968" data-name="Group 101968" transform="translate(97.032 13.566)">
              <path id="Path_31630" data-name="Path 31630" d="M910.8,344.3a3.323,3.323,0,0,1-.079-.511q-.062-.545-.167-1.472c-.137-1.278-.32-3.13-.517-5.421-.4-4.581-.871-10.916-1.287-17.919s-.7-13.351-.87-17.946c-.085-2.3-.139-4.157-.168-5.442q-.016-.932-.025-1.481a.259.259,0,1,1,.062,0q.03.548.082,1.478c.064,1.313.155,3.163.266,5.437.222,4.592.546,10.934.96,17.937s.847,13.336,1.2,17.919c.176,2.27.318,4.116.42,5.428q.068.929.109,1.476A3.2,3.2,0,0,1,910.8,344.3Z" transform="translate(-907.684 -293.596)" fill="#263238"/>
            </g>
            <g id="Group_101969" data-name="Group 101969" transform="translate(49.231 14.633)">
              <path id="Path_31631" data-name="Path 31631" d="M833.246,311.428a4.981,4.981,0,0,1,.063-.808c.069-.563.159-1.314.271-2.236l1.026-7.721.229.141-3.826,2.678-3.892,2.707-.28.2.051-.336c.469-3.069.875-5.719,1.171-7.652.15-.911.273-1.651.365-2.207a4.852,4.852,0,0,1,.164-.788,5.045,5.045,0,0,1-.067.8c-.071.559-.165,1.3-.281,2.219-.268,1.938-.636,4.593-1.061,7.669l-.229-.141,3.883-2.719,3.834-2.667.279-.194-.05.335c-.455,3.089-.848,5.758-1.135,7.706-.146.918-.265,1.664-.355,2.225A4.984,4.984,0,0,1,833.246,311.428Z" transform="translate(-826.838 -295.4)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101970" data-name="Group 101970" transform="translate(62.586 24.682)">
              <path id="Path_31632" data-name="Path 31632" d="M864.729,320.773a5.025,5.025,0,0,1-.581-.565l-1.547-1.637-5.282-5.725.254-.086c-.12,1.449-.25,3.021-.385,4.654-.142,1.657-.28,3.254-.406,4.723l-.03.341-.225-.255-5.137-5.792-1.464-1.691a4.819,4.819,0,0,1-.5-.63,5.055,5.055,0,0,1,.573.564l1.527,1.634,5.218,5.719-.255.085c.122-1.47.254-3.066.391-4.724.14-1.632.275-3.2.4-4.653l.029-.338.226.252,5.2,5.8,1.484,1.7A5.009,5.009,0,0,1,864.729,320.773Z" transform="translate(-849.426 -312.397)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101971" data-name="Group 101971" transform="translate(82.842 32.267)">
              <path id="Path_31633" data-name="Path 31633" d="M889.933,325.225a5.045,5.045,0,0,1-.4.7l-1.2,1.908-4.217,6.549-.147-.225,4.6-.794,4.674-.792.337-.057-.19.283-4.319,6.425-1.269,1.842a4.926,4.926,0,0,1-.484.642,4.98,4.98,0,0,1,.4-.7l1.2-1.888,4.227-6.486.147.225-4.672.806-4.6.78-.335.057.187-.282,4.309-6.488,1.269-1.861A5.012,5.012,0,0,1,889.933,325.225Z" transform="translate(-883.684 -325.225)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101972" data-name="Group 101972" transform="translate(67.949 43.723)">
              <path id="Path_31634" data-name="Path 31634" d="M864.745,344.6a5.077,5.077,0,0,1-.4.7l-1.2,1.908-4.217,6.549-.148-.225,4.6-.793,4.674-.792.336-.057-.19.282-4.318,6.425-1.27,1.842a4.973,4.973,0,0,1-.484.642,4.975,4.975,0,0,1,.4-.7l1.2-1.888,4.227-6.486.147.225-4.672.806-4.6.78-.335.057.187-.282,4.31-6.488,1.268-1.861A4.993,4.993,0,0,1,864.745,344.6Z" transform="translate(-858.496 -344.601)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101973" data-name="Group 101973" transform="translate(100.126 32.104)">
              <path id="Path_31635" data-name="Path 31635" d="M919.166,324.95a4.917,4.917,0,0,1-.4.7l-1.2,1.908-4.217,6.549-.147-.225,4.6-.793,4.674-.792.337-.057-.19.282-4.318,6.425-1.27,1.842a4.93,4.93,0,0,1-.484.642,4.964,4.964,0,0,1,.4-.7l1.2-1.888,4.227-6.486.147.225-4.672.806-4.6.78-.335.057.187-.282,4.31-6.488,1.268-1.861A5.042,5.042,0,0,1,919.166,324.95Z" transform="translate(-912.917 -324.95)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101974" data-name="Group 101974" transform="translate(24.771 22.64)">
              <path id="Path_31636" data-name="Path 31636" d="M791.718,308.943a5.065,5.065,0,0,1-.4.7l-1.2,1.908L785.9,318.1l-.148-.225,4.6-.793,4.674-.792.336-.057-.19.282-4.319,6.425-1.27,1.841a4.959,4.959,0,0,1-.484.643,4.972,4.972,0,0,1,.4-.7l1.2-1.888,4.227-6.486.147.225-4.672.806-4.6.78-.335.056.187-.281,4.31-6.488,1.268-1.861A4.942,4.942,0,0,1,791.718,308.943Z" transform="translate(-785.469 -308.943)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101975" data-name="Group 101975" transform="translate(7.645 65.648)">
              <path id="Path_31637" data-name="Path 31637" d="M756.5,382.885a5.015,5.015,0,0,1,.544.6l1.442,1.731,4.913,6.044-.26.07.676-4.621c.245-1.645.482-3.229.7-4.689l.05-.338.209.268,4.765,6.1,1.356,1.779a5.013,5.013,0,0,1,.461.66,4.89,4.89,0,0,1-.537-.6l-1.422-1.726-4.851-6.033.26-.069c-.213,1.459-.445,3.044-.685,4.69-.242,1.62-.475,3.18-.69,4.619l-.05.336-.21-.266-4.828-6.112-1.375-1.784A4.992,4.992,0,0,1,756.5,382.885Z" transform="translate(-756.503 -381.683)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101976" data-name="Group 101976" transform="translate(46.3 38.214)">
              <path id="Path_31638" data-name="Path 31638" d="M836.613,335.294a5.03,5.03,0,0,1-.8.1l-2.244.194-7.768.579.091-.254,3.406,3.2,3.448,3.254.248.235-.339.018-7.73.424-2.235.1a4.881,4.881,0,0,1-.8,0,4.915,4.915,0,0,1,.8-.1l2.23-.181,7.723-.534-.091.253-3.458-3.243-3.4-3.205-.247-.233.338-.02,7.775-.469,2.25-.109A5.057,5.057,0,0,1,836.613,335.294Z" transform="translate(-821.881 -335.283)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101977" data-name="Group 101977" transform="translate(47.93 66.666)">
              <path id="Path_31639" data-name="Path 31639" d="M824.638,384.764a5.008,5.008,0,0,1,.556.589l1.475,1.7L831.7,393l-.258.076.585-4.633.608-4.7.044-.339.215.264,4.883,6.007,1.391,1.752a4.99,4.99,0,0,1,.473.651,5.018,5.018,0,0,1-.549-.588l-1.456-1.7-4.968-5.937.258-.075c-.185,1.463-.385,3.053-.594,4.7-.21,1.624-.413,3.189-.6,4.631l-.043.336-.215-.261-4.947-6.017-1.41-1.757A4.991,4.991,0,0,1,824.638,384.764Z" transform="translate(-824.637 -383.404)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101978" data-name="Group 101978" transform="translate(9.783 46.159)">
              <path id="Path_31640" data-name="Path 31640" d="M774.851,348.732a5.006,5.006,0,0,1-.8.1l-2.244.194-7.767.579.091-.254,3.406,3.2,3.448,3.254.248.234-.339.019-7.73.424-2.235.1a4.967,4.967,0,0,1-.8,0,4.984,4.984,0,0,1,.8-.1l2.23-.18,7.723-.534-.092.253-3.458-3.243-3.4-3.205-.247-.233.338-.021,7.774-.469,2.25-.109A4.962,4.962,0,0,1,774.851,348.732Z" transform="translate(-760.119 -348.721)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101979" data-name="Group 101979" transform="translate(29.9 67.343)">
              <path id="Path_31641" data-name="Path 31641" d="M808.875,384.561a5.022,5.022,0,0,1-.8.1l-2.244.194-7.768.579.091-.253,3.406,3.195L805,391.633l.248.234-.339.019-7.73.423-2.235.1a4.964,4.964,0,0,1-.8,0,4.946,4.946,0,0,1,.8-.1l2.23-.181,7.723-.533-.091.253-3.458-3.243-3.4-3.205-.247-.233.338-.021,7.775-.469,2.25-.109A4.972,4.972,0,0,1,808.875,384.561Z" transform="translate(-794.143 -384.55)" fill="#e0e0e0"/>
            </g>
            <g id="Group_101980" data-name="Group 101980" transform="translate(75.315 13.445)">
              <path id="Path_31642" data-name="Path 31642" d="M885.685,293.4a4.94,4.94,0,0,1-.8.1l-2.244.195-7.767.579.09-.253,3.406,3.195,3.448,3.254.248.235-.34.018-7.729.424-2.235.1a5.018,5.018,0,0,1-.8,0,4.886,4.886,0,0,1,.8-.1l2.23-.181,7.723-.533-.091.252-3.458-3.243-3.4-3.205-.247-.233.338-.02,7.775-.469,2.25-.109A5.017,5.017,0,0,1,885.685,293.4Z" transform="translate(-870.953 -293.392)" fill="#e0e0e0"/>
            </g>
          </g>
          <g id="Group_101985" data-name="Group 101985" transform="translate(66.613 127.581)">
            <path id="Path_31643" data-name="Path 31643" d="M857.579,381.99s9.949-2.866,13.241-3.705c1.283-.328,6.778,2.571,10.573,4.981,0,0,5.227,3.1,4.8,4.054-.465,1.051-2.393.835-5.466-.61,0,0,1.072,3.114-1.033,3.047,0,0,1.045,3.365-.928,3.714s-2.437-3.946-4.178-4.178-5.339-.464-5.339-.464-3,4.961-5.317,5.31a43.57,43.57,0,0,1-7.7-.279Z" transform="translate(-856.236 -378.259)" fill="#ffbe9d"/>
            <g id="Group_101982" data-name="Group 101982" transform="translate(18.321 4.412)">
              <path id="Path_31644" data-name="Path 31644" d="M887.223,385.726a33.135,33.135,0,0,1,5.393,3.4,33.141,33.141,0,0,1-5.393-3.4Z" transform="translate(-887.222 -385.721)" fill="#eb996e"/>
            </g>
            <g id="Group_101983" data-name="Group 101983" transform="translate(17.733 6.323)">
              <path id="Path_31645" data-name="Path 31645" d="M886.229,388.96a41.275,41.275,0,0,1,6.468,2.985,41.214,41.214,0,0,1-6.468-2.985Z" transform="translate(-886.228 -388.954)" fill="#eb996e"/>
            </g>
            <g id="Group_101984" data-name="Group 101984" transform="translate(18.798 10.932)">
              <path id="Path_31646" data-name="Path 31646" d="M888.03,396.785a16.419,16.419,0,0,1,4.449.61,16.478,16.478,0,0,1-4.449-.61Z" transform="translate(-888.03 -396.749)" fill="#eb996e"/>
            </g>
          </g>
          <g id="Group_102016" data-name="Group 102016" transform="translate(46.818)">
            <g id="Group_102010" data-name="Group 102010">
              <path id="Path_31647" data-name="Path 31647" d="M830.954,191.177a2.044,2.044,0,0,1-2.879,2.418c-1.348-.683-1.519-2.543-1.254-4.031s.771-3.076.147-4.452a10.115,10.115,0,0,0-2.318-2.549,6.522,6.522,0,0,1-1.812-5.533,5.483,5.483,0,0,1,3.77-4.313c1.98-.553,4.421.055,5.895-1.379,1.069-1.039,1.135-2.742,1.961-3.982,1.284-1.925,4-2.231,6.29-1.917s4.627,1.049,6.872.49c1.875-.467,3.415-1.776,5.172-2.58a10.128,10.128,0,0,1,13.187,13.9,6.469,6.469,0,0,1-3.142,3.109" transform="translate(-822.756 -162.48)" fill="#263238"/>
              <g id="Group_102001" data-name="Group 102001" transform="translate(7.518 11.948)">
                <g id="Group_102000" data-name="Group 102000">
                  <g id="Group_101999" data-name="Group 101999">
                    <g id="Group_101986" data-name="Group 101986">
                      <path id="Path_31648" data-name="Path 31648" d="M847.56,248.274h0a11.4,11.4,0,0,0,11.385-11.556l-.15-10.87s9.131-.915,9.736-9.961.168-29.92.168-29.92h0a31.51,31.51,0,0,0-31.831,2.251l-1.4.96.68,47.859A11.4,11.4,0,0,0,847.56,248.274Z" transform="translate(-835.471 -182.687)" fill="#ffbe9d"/>
                    </g>
                    <g id="Group_101990" data-name="Group 101990" transform="translate(28.027 15.362)">
                      <g id="Group_101987" data-name="Group 101987" transform="translate(1.603 1.603)">
                        <path id="Path_31649" data-name="Path 31649" d="M886.784,214.081a1.354,1.354,0,0,1,.015-2.693.612.612,0,0,1,.22,0,1.354,1.354,0,0,1-.015,2.693A.611.611,0,0,1,886.784,214.081Z" transform="translate(-885.585 -211.379)" fill="#263238"/>
                      </g>
                      <g id="Group_101989" data-name="Group 101989">
                        <g id="Group_101988" data-name="Group 101988">
                          <path id="Path_31650" data-name="Path 31650" d="M887.451,210.085c-.15.149-1-.584-2.249-.657-1.246-.092-2.187.521-2.315.354-.064-.075.093-.35.5-.629a3.079,3.079,0,0,1,3.643.243C887.405,209.726,887.524,210.019,887.451,210.085Z" transform="translate(-882.874 -208.668)" fill="#263238"/>
                        </g>
                      </g>
                    </g>
                    <g id="Group_101994" data-name="Group 101994" transform="translate(14.424 15.722)">
                      <g id="Group_101991" data-name="Group 101991" transform="translate(1.69 1.126)">
                        <path id="Path_31651" data-name="Path 31651" d="M864.792,211.417a1.317,1.317,0,1,1-1.832.332A1.316,1.316,0,0,1,864.792,211.417Z" transform="translate(-862.725 -211.182)" fill="#263238"/>
                      </g>
                      <g id="Group_101993" data-name="Group 101993">
                        <g id="Group_101992" data-name="Group 101992">
                          <path id="Path_31652" data-name="Path 31652" d="M864.445,210.694c-.151.149-1-.584-2.249-.657-1.246-.093-2.186.52-2.316.353-.063-.075.093-.35.5-.629a3.078,3.078,0,0,1,3.643.242C864.4,210.335,864.517,210.628,864.445,210.694Z" transform="translate(-859.867 -209.277)" fill="#263238"/>
                        </g>
                      </g>
                    </g>
                    <g id="Group_101996" data-name="Group 101996" transform="translate(23.273 16.817)">
                      <g id="Group_101995" data-name="Group 101995">
                        <path id="Path_31653" data-name="Path 31653" d="M875,222.01a6.047,6.047,0,0,1,2.039-.317c.328-.018.588-.077.621-.251a1.776,1.776,0,0,0-.183-.947l-.848-2.587c-1.165-3.684-1.959-6.717-1.771-6.776s1.283,2.878,2.448,6.562l.795,2.6a2.084,2.084,0,0,1,.1,1.365.856.856,0,0,1-.59.49,2.094,2.094,0,0,1-.561.042A6.047,6.047,0,0,1,875,222.01Z" transform="translate(-874.833 -211.13)" fill="#263238"/>
                      </g>
                    </g>
                    <g id="Group_101997" data-name="Group 101997" transform="translate(19.209 28.853)">
                      <path id="Path_31654" data-name="Path 31654" d="M868.066,231.487a23.384,23.384,0,0,0,6.018,2.11,3.451,3.451,0,0,1-4.141,1.15C867.288,233.608,868.066,231.487,868.066,231.487Z" transform="translate(-867.96 -231.487)" fill="#fff"/>
                    </g>
                    <g id="Group_101998" data-name="Group 101998" transform="translate(11.727 39.034)">
                      <path id="Path_31655" data-name="Path 31655" d="M866.924,252.7a21.08,21.08,0,0,1-11.619-4s2.435,6.668,11.512,6.344Z" transform="translate(-855.305 -248.706)" fill="#eb996e"/>
                    </g>
                  </g>
                </g>
              </g>
              <path id="Path_31656" data-name="Path 31656" d="M867.175,186.608s-5.464,2.777-12.711-.666c-4.756-2.259-10.269-3.3-11.461-2.331s-1.353,5.829-5.992,7.337c0,0,.5,9.254-3.2,9s-1.67-14.645-1.67-14.645l8.946-4.587,9.82-2.054,9.313,1.129,7.225,1.828Z" transform="translate(-826.366 -169.092)" fill="#263238"/>
              <g id="Group_102005" data-name="Group 102005" transform="translate(1.861 27.473)">
                <g id="Group_102002" data-name="Group 102002">
                  <path id="Path_31657" data-name="Path 31657" d="M832.164,209.349c-.15-.075-6.12-2.179-6.259,4.021s6.114,5.061,6.128,4.884S832.164,209.349,832.164,209.349Z" transform="translate(-825.903 -208.946)" fill="#ffbe9d"/>
                </g>
                <g id="Group_102004" data-name="Group 102004" transform="translate(1.573 1.985)">
                  <g id="Group_102003" data-name="Group 102003">
                    <path id="Path_31658" data-name="Path 31658" d="M831.28,217.235c-.027-.021-.109.073-.294.153a1.083,1.083,0,0,1-.808,0A2.672,2.672,0,0,1,829,214.961a3.56,3.56,0,0,1,.337-1.54,1.248,1.248,0,0,1,.831-.811.551.551,0,0,1,.631.31c.079.176.037.3.07.31.017.016.135-.1.091-.358a.69.69,0,0,0-.235-.394.817.817,0,0,0-.594-.171,1.52,1.52,0,0,0-1.15.936,3.68,3.68,0,0,0-.413,1.72,2.806,2.806,0,0,0,1.515,2.713,1.157,1.157,0,0,0,.992-.142C831.266,217.384,831.3,217.247,831.28,217.235Z" transform="translate(-828.563 -212.303)" fill="#eb996e"/>
                  </g>
                </g>
              </g>
              <g id="Group_102006" data-name="Group 102006" transform="translate(4.965 8.306)">
                <path id="Path_31659" data-name="Path 31659" d="M831.154,176.689a3.422,3.422,0,0,1,3.828,1.428,5.844,5.844,0,0,0-3.828-1.428Z" transform="translate(-831.154 -176.529)" fill="#263238"/>
              </g>
              <g id="Group_102007" data-name="Group 102007" transform="translate(8.467 4.753)">
                <path id="Path_31660" data-name="Path 31660" d="M837.3,170.52c.108.007-.062,1.307.155,2.858a12.275,12.275,0,0,0,.866,2.7c-.03.021-.256-.221-.52-.685a5.834,5.834,0,0,1-.65-1.971A6.447,6.447,0,0,1,837.3,170.52Z" transform="translate(-837.076 -170.52)" fill="#263238"/>
              </g>
              <g id="Group_102008" data-name="Group 102008" transform="translate(34.168 24.883)">
                <path id="Path_31661" data-name="Path 31661" d="M880.547,205.43c-.033-.073.233-.286.736-.492a5.661,5.661,0,0,1,4.149.041c.5.215.76.434.726.506-.076.164-1.271-.3-2.8-.305C881.824,205.153,880.62,205.6,880.547,205.43Z" transform="translate(-880.544 -204.565)" fill="#263238"/>
              </g>
              <g id="Group_102009" data-name="Group 102009" transform="translate(20.343 23.578)">
                <path id="Path_31662" data-name="Path 31662" d="M857.165,203.224c-.033-.073.233-.286.735-.492a5.663,5.663,0,0,1,4.149.041c.5.215.76.434.725.506-.076.164-1.27-.3-2.8-.305C858.441,202.947,857.238,203.389,857.165,203.224Z" transform="translate(-857.162 -202.359)" fill="#263238"/>
              </g>
            </g>
            <g id="Group_102015" data-name="Group 102015" transform="translate(7.302 26.272)">
              <g id="Group_102011" data-name="Group 102011" transform="translate(25.712 0.306)">
                <path id="Path_31663" data-name="Path 31663" d="M878.682,207.576a2.688,2.688,0,0,1,.024.367c.008.262.018.594.03,1,.009.439.02.958.033,1.552a5.891,5.891,0,0,0,.295,2.011,5.385,5.385,0,0,0,1.307,2.066,5.266,5.266,0,0,0,2.444,1.342,5,5,0,0,0,1.512.132,4.853,4.853,0,0,0,1.547-.326,5.209,5.209,0,0,0,2.55-2.063,5.308,5.308,0,0,0,.831-3.027c0-1.058.006-2.079.008-3.054l.143.142-7.673-.07-2.228-.038-.6-.018a1.175,1.175,0,0,1-.219-.017,6.3,6.3,0,0,1,.76-.034l2.2-.039,7.768-.069.143,0v.145c0,.975.006,2,.008,3.054a5.588,5.588,0,0,1-.874,3.193,5.489,5.489,0,0,1-2.7,2.179,5.138,5.138,0,0,1-1.637.338,5.266,5.266,0,0,1-1.595-.15,5.5,5.5,0,0,1-2.557-1.436,5.56,5.56,0,0,1-1.334-2.179,5.934,5.934,0,0,1-.26-2.094q.019-.914.032-1.592c.012-.4.022-.728.03-.987A1.7,1.7,0,0,1,878.682,207.576Z" transform="translate(-878.592 -207.432)" fill="#263238"/>
              </g>
              <g id="Group_102012" data-name="Group 102012" transform="translate(11.216)">
                <path id="Path_31664" data-name="Path 31664" d="M854.166,207.059a2.781,2.781,0,0,1,.023.367l.03,1q.013.66.033,1.552a5.871,5.871,0,0,0,.294,2.011,5.39,5.39,0,0,0,1.307,2.066A5.265,5.265,0,0,0,858.3,215.4a5.019,5.019,0,0,0,1.512.132,4.868,4.868,0,0,0,1.547-.326,5.217,5.217,0,0,0,2.55-2.063,5.313,5.313,0,0,0,.831-3.027c0-1.058.005-2.079.008-3.055l.142.143-7.673-.07-2.228-.038-.6-.018a1.231,1.231,0,0,1-.219-.017,6.279,6.279,0,0,1,.76-.034l2.2-.039,7.767-.07h.142v.144c0,.976.005,2,.008,3.055a5.589,5.589,0,0,1-.874,3.193,5.493,5.493,0,0,1-2.7,2.179,5.139,5.139,0,0,1-1.637.337,5.229,5.229,0,0,1-1.595-.15,5.486,5.486,0,0,1-2.557-1.436,5.55,5.55,0,0,1-1.335-2.179,5.938,5.938,0,0,1-.26-2.094c.013-.61.024-1.142.033-1.592.012-.4.022-.728.03-.987A1.613,1.613,0,0,1,854.166,207.059Z" transform="translate(-854.076 -206.915)" fill="#263238"/>
              </g>
              <g id="Group_102013" data-name="Group 102013" transform="translate(21.849 0.727)">
                <path id="Path_31665" data-name="Path 31665" d="M876.143,208.3a13.58,13.58,0,0,1-4.083,0,13.63,13.63,0,0,1,4.083,0Z" transform="translate(-872.06 -208.145)" fill="#263238"/>
              </g>
              <g id="Group_102014" data-name="Group 102014" transform="translate(0 0.79)">
                <path id="Path_31666" data-name="Path 31666" d="M835.106,208.423a104.017,104.017,0,0,1,11.306-.034,103.616,103.616,0,0,1-11.306.034Z" transform="translate(-835.106 -208.251)" fill="#263238"/>
              </g>
            </g>
          </g>
        </g>
      </g>
      <g id="Shield" transform="translate(851.638 167.48)">
        <g id="Group_102025" data-name="Group 102025" transform="translate(0 0)">
          <g id="Group_102022" data-name="Group 102022">
            <g id="Group_102019" data-name="Group 102019">
              <g id="Group_102018" data-name="Group 102018">
                <path id="Path_31667" data-name="Path 31667" d="M1010.3,218.234l-22.163-7.959-21.948,7.852c-2.8,1.324-5.259,2.116-5.259,4.726v13a49.115,49.115,0,0,0,7.23,25.865,45.008,45.008,0,0,0,20.193,17.834c8.971-2.914,15.668-10.683,19.762-17.327a49.113,49.113,0,0,0,7.23-25.864V224.669C1015.345,220.947,1013.372,219.129,1010.3,218.234Z" transform="translate(-960.93 -210.275)" fill="#114a88"/>
              </g>
            </g>
            <g id="Group_102020" data-name="Group 102020" opacity="0.83">
              <path id="Path_31668" data-name="Path 31668" d="M988.352,272.005a35.787,35.787,0,0,1-15.976-14.225,38.862,38.862,0,0,1-5.72-20.464V228.2c0-2.065,1.245-4,3.719-5.154l17.765-6.643,0-6.129-21.948,7.852c-2.3,1.007-5.259,2.116-5.259,4.726v13a49.115,49.115,0,0,0,7.23,25.865,45.008,45.008,0,0,0,20.193,17.834Z" transform="translate(-960.93 -210.275)" fill="#002349"/>
            </g>
            <g id="Group_102021" data-name="Group 102021" transform="translate(27.207)">
              <path id="Path_31669" data-name="Path 31669" d="M997.689,279.547a26.682,26.682,0,0,0,4.355-1.867,39.069,39.069,0,0,0,9.213-7.249,49.644,49.644,0,0,0,6.409-8.611,47.282,47.282,0,0,0,7.015-26.114l.013-10.73c-.013-5.343-2.754-5.736-5.057-6.743l-22.163-7.959,0,6.129,17.973,6.75c2.473,1.155,3.719,3.089,3.719,5.154v9.116a38.861,38.861,0,0,1-5.72,20.464,34.982,34.982,0,0,1-15.76,14.118Z" transform="translate(-997.474 -210.275)" fill="#002349"/>
            </g>
          </g>
          <path id="Path_31670" data-name="Path 31670" d="M1000.479,243.234l.025-1.754A5.754,5.754,0,0,0,989,241.471l-.025,1.787a3.218,3.218,0,0,0-2.746,3.181v10.346a3.22,3.22,0,0,0,3.22,3.22h10.74a3.22,3.22,0,0,0,3.22-3.22V246.439A3.219,3.219,0,0,0,1000.479,243.234Zm-4.2,10.976h-2.809l.438-2.029a1.712,1.712,0,1,1,1.856-.032C995.968,252.935,996.284,254.21,996.284,254.21Zm2.956-10.99h-9.029l.025-1.739a4.514,4.514,0,0,1,9.029-.009Z" transform="translate(-967.393 -216.777)" fill="#fff" opacity="0.77"/>
          <g id="Group_102023" data-name="Group 102023" transform="translate(8.791 14.63)">
            <path id="Path_31671" data-name="Path 31671" d="M973.264,241.626a7.059,7.059,0,0,1-.332-1.8,30.8,30.8,0,0,1-.18-4.413c.025-.863.082-1.684.16-2.428a5.2,5.2,0,0,1,.5-1.962,2.254,2.254,0,0,1,.971-.975c.3-.139.488-.133.49-.112a2.512,2.512,0,0,0-1.217,1.211,5.406,5.406,0,0,0-.384,1.871c-.058.739-.1,1.552-.128,2.406-.051,1.709,0,3.257.065,4.378A10.934,10.934,0,0,1,973.264,241.626Z" transform="translate(-972.738 -229.925)" fill="#fff"/>
          </g>
          <g id="Group_102024" data-name="Group 102024" transform="translate(9.425 29.595)">
            <path id="Path_31672" data-name="Path 31672" d="M973.808,252.419c-.107,0-.2-.532-.217-1.193s.063-1.2.171-1.2.2.532.217,1.193S973.915,252.417,973.808,252.419Z" transform="translate(-973.589 -250.026)" fill="#fff"/>
          </g>
        </g>
      </g>
      <g id="Device" transform="translate(785.71 258.704)">
        <g id="Group_102026" data-name="Group 102026" transform="translate(0)">
          <path id="Path_31673" data-name="Path 31673" d="M904.443,325.225,884.816,373.76H849.427v2.191H954.833l21.561-50.726Z" transform="translate(-849.427 -325.225)" fill="#263238"/>
          <path id="Path_31674" data-name="Path 31674" d="M988.433,369.886c-.711,2.443-2.782,3.987-4.625,3.451s-2.761-2.952-2.05-5.395,2.782-3.988,4.625-3.451S989.144,367.443,988.433,369.886Z" transform="translate(-903.418 -341.231)" fill="#fafafa"/>
        </g>
      </g>
    </g>
  </g>
</svg>
